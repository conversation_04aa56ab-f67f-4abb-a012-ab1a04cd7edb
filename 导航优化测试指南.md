# 导航优化测试指南

## 🎯 测试目标
验证新的导航策略是否按预期工作，确保各种导航场景的正确性和用户体验。

## 📋 测试场景

### 1. Root 级导航测试（使用淡入淡出动画）

#### 1.1 首次启动流程
- **场景**：应用首次启动
- **预期行为**：
  - 启动页 → 登录页/主页（根据登录状态）
  - 使用 `NavigationContext.initial`
  - 淡入淡出动画效果
- **测试步骤**：
  1. 清除应用数据，重新启动
  2. 观察页面切换动画
  3. 验证导航栈状态

#### 1.2 登录成功流程
- **场景**：用户成功登录
- **预期行为**：
  - 登录页 → 主页
  - 使用 `NavigationContext.initial`
  - 淡入淡出动画效果
- **测试步骤**：
  1. 在登录页输入正确凭据
  2. 点击登录按钮
  3. 验证跳转到主页且动画正确

#### 1.3 用户主动退出登录
- **场景**：用户在设置中点击退出登录
- **预期行为**：
  - 主页 → 登录页
  - 使用 `NavigationContext.userAction`
  - 清空所有导航栈
  - 淡入淡出动画效果

#### 1.4 Token 失效强制登录
- **场景**：Token 过期，系统强制用户重新登录
- **预期行为**：
  - 任何页面 → 登录页
  - 使用 `NavigationContext.reauth`
  - 清空导航栈
  - 淡入淡出动画效果

### 2. Stack 级导航测试（使用推入推出动画）

#### 2.1 游客登录提示
- **场景**：游客用户尝试访问需要登录的功能
- **预期行为**：
  - 显示登录提示弹窗
  - 用户确认后：主页 → 登录页（栈导航）
  - 使用 `NavigationContext.guestLogin`
  - 推入推出动画效果
  - 用户可以返回主页
- **测试步骤**：
  1. 以游客身份进入主页
  2. 点击需要登录的功能
  3. 验证弹窗显示
  4. 点击"登录"按钮
  5. 验证栈导航和动画
  6. 测试返回按钮

#### 2.2 个人资料页面导航
- **场景**：用户访问个人资料页面
- **预期行为**：
  - 主页 → 个人资料页（栈导航）
  - 推入推出动画效果
  - 用户可以返回主页

### 3. 弹窗机制测试

#### 3.1 游客登录弹窗
- **场景**：游客用户触发需要登录的功能
- **预期行为**：
  - 显示自定义登录提示弹窗
  - 弹窗包含"登录"和"取消"按钮
  - 点击"登录"：跳转到登录页（栈导航）
  - 点击"取消"：关闭弹窗，留在当前页面

## 🔍 验证要点

### 导航上下文验证
- [ ] `NavigationContext.initial` - 首次启动和登录成功
- [ ] `NavigationContext.reauth` - Token 失效重新认证
- [ ] `NavigationContext.userAction` - 用户主动操作
- [ ] `NavigationContext.guestLogin` - 游客登录提示

### 动画效果验证
- [ ] Root 级导航：淡入淡出动画（0.3秒）
- [ ] Stack 级导航：推入推出动画
- [ ] 动画流畅，无卡顿

### 导航栈状态验证
- [ ] Root 级导航：清空导航栈
- [ ] Stack 级导航：保持导航栈
- [ ] 返回按钮正常工作

### 委托方法验证
- [ ] 新的基于上下文的委托方法正常工作
- [ ] 所有导航都使用正确的上下文
- [ ] 不再有重复的导航调用

## 🐛 常见问题排查

### 问题1：动画不生效
- 检查 `.animation()` 修饰符是否正确添加
- 验证 `value` 参数是否正确绑定到状态变化

### 问题2：导航栈状态错误
- 检查 `state.path.removeAll()` 是否在正确的时机调用
- 验证 Root 级和 Stack 级导航的区分逻辑

### 问题3：弹窗不显示
- 检查 `store.requireLoginAlert` 状态
- 验证弹窗触发的 Action 是否正确发送

### 问题4：返回按钮异常
- 检查 Stack 级导航是否正确使用 `path.append()`
- 验证导航栈的状态管理

## 📝 测试记录模板

```
测试场景：[场景名称]
测试时间：[时间]
测试结果：✅ 通过 / ❌ 失败
问题描述：[如果失败，描述具体问题]
修复方案：[如果有问题，记录修复方案]
```

## 🚀 后续优化建议

1. **性能优化**：监控导航动画的性能影响
2. **用户体验**：收集用户对新导航体验的反馈
3. **代码重构**：考虑将 NavigationContext 移到共享模块
4. **测试覆盖**：添加单元测试覆盖导航逻辑
