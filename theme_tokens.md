# Theme Colors

| Token                | Any Color         | Any Opacity | Dark Color        | Dark Opacity | 说明/中文注释 |
|----------------------|------------------|-------------|-------------------|--------------|--------------|
| font-title           | #121212          | 0.87        | #121212           | 0.87         | 标题字体     |
| font-button          | #121212          | 0.87        | #121212           | 0.87         | 按钮字体     |
| font-input           | #121212          | 0.87        | #121212           | 0.87         | 输入框字体   |
| font-Subtitle        | #121212          | 0.87        | #121212           | 0.87         | 副标题字体   |
| font-p1-regular      | #121212          | 0.87        | #121212           | 0.87         | 正文字体     |
| font-primary         | #FFFFFF          | 1.0         | #FFFFFF           | 1.0          | 主要字体颜色 |
| font-secondary       | #FFFFFF          | 0.87        | #FFFFFF           | 0.87         | 次要字体颜色 |
| font-third           | #FFFFFF          | 0.64        | #FFFFFF           | 0.64         | 第三级字体颜色 |
| font-placeholder     | #FFFFFF          | 0.56        | #FFFFFF           | 0.56         | 占位符字体颜色 |
| icon-primary         | #FFFFFF          | 1.0         | #FFFFFF           | 1.0          | 主要图标颜色 |
| icon-secondary       | #FFFFFF          | 0.72        | #FFFFFF           | 0.87         | 次要图标颜色 |
| icon-third           | #FFFFFF          | 0.56        | #FFFFFF           | 0.56         | 第三级图标颜色 |
| color-theme-linear   | #F16779~#F241AC  | 1.0         | #F16779~#F241AC   | 1.0          | 主题线性渐变色 |
| color-theme-red300   | #F16779          | 1.0         | #F16779           | 1.0          | 主题红300    |
| color-red100         | #FFCAD6          | 1.0         | #FFCAD6           | 1.0          | 红100        |
| color-red200         | #F7949F          | 1.0         | #F7949F           | 1.0          | 红200        |
| color-red500         | #FF113F          | 1.0         | #FF113F           | 1.0          | 红500        |
| color-red600         | #F5003E          | 1.0         | #F5003E           | 1.0          | 红600        |
| color-red700         | #E20038          | 1.0         | #E20038           | 1.0          | 红700        |
| color-red700-tab     | #E20038          | 0.24        | #E20038           | 0.24         | 红700-Tab    |
| color-pink300        | #F730AB          | 1.0         | #F730AB           | 1.0          | 夜间粉色300  |
| color-pink200        | #FB76C8          | 1.0         | #FB76C8           | 1.0          | 夜间粉色200  |
| color-pink100        | #FF9EDA          | 1.0         | #FF9EDA           | 1.0          | 夜间粉色100  |
| color-background     | #1F2022          | 1.0         | #241C25           | 1.0          | 页面背景色   |
| color-white          | #FFFFFF          | 1.0         | #FFFFFF           | 1.0          | 夜间白色     |
| surface-primary-linear-button | #F16779~#F241AC | 1.0 | #F16779~#F241AC   | 1.0          | 主按钮渐变色 |
| surface-background   | #1F2022          | 1.0         | #241C25           | 1.0          | 页面背景色   |
| surface-Secondary-Button | #FFFFFF      | 0.08        | #FFFFFF           | 0.08         | 次按钮背景色 |
| surface-Secondary-Button-hover | #FFFFFF | 0.24        | #FFFFFF           | 0.24         | 次按钮悬停色 |
| border-subtle        | #FFFFFF          | 0.10        | #FFFFFF           | 0.10         | 弱边框色     |
| border-medium        | #FFFFFF          | 0.24        | #FFFFFF           | 0.24         | 中等边框色   |

# 字体详细设置表（不含颜色）

| Token            | 字体（Font）         | 字重（Weight） | 字号（Size） | 行高（Line height） | 字间距（Letter spacing） | 对齐方式（Horizontal align） | 其他说明         |
|------------------|---------------------|---------------|-------------|--------------------|-------------------------|-----------------------------|------------------|
| $font-title      | Chiron GoRound TC   | 700           | 36px        | 48px               | -0.4px                  | Right                       |                  |
| $font-button     | Chiron GoRound TC   | 500           | 16px        | 28px               | 1.6px                   | Right                       |                  |
| $font-input      | Noto Sans HK        | 400           | 14px        | 20px               | 0.4px                   | Right                       |                  |
| $font-Subtitle   | Noto Sans HK        | 500           | 14px        | 22px               | 0.4px                   | Right                       | Title case       |
| $font-p1-regular | Noto Sans HK        | 400           | 12px        | 16px               | 0.4px                   | Right                       |                  |

> 注："-" 表示该主题下未单独定义，默认与另一主题一致或无此 Token。

---

# 字体详细设置表（不含颜色）

| Token            | 字体（Font）         | 字重（Weight） | 字号（Size） | 行高（Line height） | 字间距（Letter spacing） | 对齐方式（Horizontal align） | 其他说明         |
|------------------|---------------------|---------------|-------------|--------------------|-------------------------|-----------------------------|------------------|
| $font-title      | Chiron GoRound TC   | 700           | 36px        | 48px               | -0.4px                  | Right                       |                  |
| $font-button     | Chiron GoRound TC   | 500           | 16px        | 28px               | 1.6px                   | Right                       |                  |
| $font-input      | Noto Sans HK        | 400           | 14px        | 20px               | 0.4px                   | Right                       |                  |
| $font-Subtitle   | Noto Sans HK        | 500           | 14px        | 22px               | 0.4px                   | Right                       | Title case       |
| $font-p1-regular | Noto Sans HK        | 400           | 12px        | 16px               | 0.4px                   | Right                       |                  |

</rewritten_file> 