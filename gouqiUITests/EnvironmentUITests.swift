//
//  EnvironmentUITests.swift
//  gouqiUITests
//
//  Created on 2025/6/16.
//

import XCTest

final class EnvironmentUITests: XCTestCase {
    
    override func setUpWithError() throws {
        continueAfterFailure = false
    }
    
    @MainActor
    func testEnvironmentInfoDisplay() throws {
        // 启动应用
        let app = XCUIApplication()
        
        // 根据不同的环境设置启动参数
        #if DEBUG
            #if TESTING
                // 测试环境
                app.launchArguments = ["--UITesting", "--env=testing"]
            #else
                // 开发环境
                app.launchArguments = ["--UITesting", "--env=development"]
            #endif
        #else
            // 生产环境
            app.launchArguments = ["--UITesting", "--env=production"]
        #endif
        
        app.launch()
        
        // 验证环境信息是否正确显示
        #if DEBUG
            #if TESTING
                // 测试环境
                XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "测试环境")).firstMatch.exists)
                XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "api-test.example.com")).firstMatch.exists)
            #else
                // 开发环境
                XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "开发环境")).firstMatch.exists)
                XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "api-dev.example.com")).firstMatch.exists)
            #endif
        #else
            // 生产环境
            XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "生产环境")).firstMatch.exists)
            XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "api.example.com")).firstMatch.exists)
        #endif
        
        // 验证日志记录状态显示
        #if DEBUG
            XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "日志记录: 开启")).firstMatch.exists)
        #else
            XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "日志记录: 关闭")).firstMatch.exists)
        #endif
    }
    
    @MainActor
    func testEnvironmentSpecificUI() throws {
                // 启动应用
        let app = XCUIApplication()
        app.launch()
        
        // 验证环境特定的UI元素颜色
        // 注意：UI测试中难以直接测试颜色，这里仅作为示例
        // 实际测试中可以使用截图比较或其他方法
        
        // 验证环境信息文本存在
        XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "当前环境")).firstMatch.exists)
        XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "API地址")).firstMatch.exists)
        XCTAssertTrue(app.staticTexts.containing(NSPredicate(format: "label CONTAINS %@", "日志记录")).firstMatch.exists)
    }
} 
