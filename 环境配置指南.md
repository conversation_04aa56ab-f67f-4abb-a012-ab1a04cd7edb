# 环境配置指南

本项目支持三种环境配置：
- 开发环境（Development）
- 测试环境（Testing）
- 生产环境（Production）

## 当前配置方式

环境配置通过编译标志（Compiler Flags）和构建配置（Build Configurations）来实现：

1. **开发环境**：Debug模式下的默认环境
2. **测试环境**：Debug模式 + TESTING标志
3. **生产环境**：Release模式

## 如何在Xcode中配置

### 步骤1：创建构建配置

1. 打开项目设置（点击项目名称 > Info标签）
2. 在"Configurations"部分，点击"+"按钮
3. 添加以下构建配置：
   - Debug-Testing（基于Debug）
   - Release-Testing（基于Release，如有需要）

### 步骤2：设置编译标志

1. 选择项目 > Build Settings
2. 搜索"Swift Compiler - Custom Flags"
3. 展开"Other Swift Flags"
4. 为Debug-Testing配置添加`-DTESTING`标志

### 步骤3：创建构建方案（Schemes）

1. 点击Xcode顶部的方案选择器，选择"Manage Schemes..."
2. 创建三个方案：
   - gouqi-Development（使用Debug配置）
   - gouqi-Testing（使用Debug-Testing配置）
   - gouqi-Production（使用Release配置）

### 步骤4：配置方案

对于每个方案，设置相应的构建配置：

1. 选择方案 > Edit Scheme
2. 在Run、Test、Profile、Analyze和Archive选项中，设置相应的构建配置

## 环境配置的使用

在代码中，您可以通过`Environment.shared`访问当前环境配置：

```swift
// 获取当前环境
let currentEnv = Environment.shared.current

// 获取API基础URL
let baseURL = Environment.shared.baseURL

// 检查是否应该记录网络调用
if Environment.shared.shouldLogNetworkCalls {
    print("网络请求：\(url)")
}
```

## 环境特定的条件编译

您可以使用条件编译来为不同环境编写特定代码：

```swift
#if DEBUG
    // 仅在Debug模式（开发和测试环境）执行
    print("调试模式")
    
    #if TESTING
        // 仅在测试环境执行
        print("测试环境特定代码")
    #else
        // 仅在开发环境执行
        print("开发环境特定代码")
    #endif
#else
    // 仅在Release模式（生产环境）执行
    print("生产环境特定代码")
#endif
```

## 测试环境配置

项目包含多种测试来验证环境配置的正确性：

### 单元测试

1. **EnvironmentTests**: 测试基本环境配置功能
   - `testDefaultEnvironment`: 验证默认环境设置
   - `testEnvironmentAPIURLs`: 测试不同环境的API URL
   - `testLoggingConfiguration`: 测试日志记录设置

2. **EnvironmentSwitchTests**: 测试环境切换功能
   - `testEnvironmentSwitching`: 验证环境切换逻辑
   - `testCommandLineArgumentHandling`: 测试命令行参数处理

### UI测试

**EnvironmentUITests**: 测试环境信息在UI中的显示
   - `testEnvironmentInfoDisplay`: 验证环境信息是否正确显示
   - `testEnvironmentSpecificUI`: 测试环境特定的UI元素

### 运行测试

要运行特定环境的测试：

1. 选择相应的方案（Development、Testing或Production）
2. 按下 Command+U 或选择 Product > Test

## 注意事项

- 确保在提交到版本控制系统之前，项目使用正确的环境配置
- 避免在生产构建中包含测试或开发环境的代码
- 敏感信息（如API密钥）应该根据环境配置进行管理
- 在运行UI测试时，可以通过启动参数模拟不同的环境 





# 主题 Token 区分表（含颜色）

| Token                | 白色主图（Light/Default） | 夜间主题（Dark Mode） | 说明/中文注释           |
|----------------------|--------------------------|----------------------|-------------------------|
| $font-title          | Chiron GoRound TC<br>颜色：#121212 87% | Chiron GoRound TC<br>颜色：#121212 87% | 标题字体                |
| $font-button         | Chiron GoRound TC<br>颜色：#121212 87% | Chiron GoRound TC<br>颜色：#121212 87% | 按钮字体                |
| $font-input          | Noto Sans HK<br>颜色：#121212 87%     | Noto Sans HK<br>颜色：#121212 87%     | 输入框字体              |
| $font-Subtitle       | Noto Sans HK<br>颜色：#121212 87%     | Noto Sans HK<br>颜色：#121212 87%     | 副标题字体              |
| $font-p1-regular     | Noto Sans HK<br>颜色：#121212 87%     | Noto Sans TC<br>颜色：#121212 87%     | 正文字体                |
| $font-primary        | #FFFFFF                  | #FFFFFF              | 主要字体颜色            |
| $font-secondary      | #FFFFFF 87%              | #FFFFFF 87%          | 次要字体颜色            |
| $font-third          | #FFFFFF 64%              | #FFFFFF 64%          | 第三级字体颜色          |
| $font-placeholder    | #FFFFFF 56%              | -                    | 占位符字体颜色          |
| $icon-primary        | #FFFFFF                  | -                    | 主要图标颜色            |
| $icon-secondary      | #FFFFFF 72%              | #FFFFFF 87%          | 次要图标颜色            |
| $icon-third          | #FFFFFF 56%              | -                    | 第三级图标颜色          |
| $color-theme-linear  | #F16779 ~ #F241AC        | -                    | 主题线性渐变色          |
| $color-theme-red300  | #F16779                  | -                    | 主题红300               |
| $color-red100        | #FFCAD6                  | -                    | 红100                   |
| $color-red200        | #F7949F                  | -                    | 红200                   |
| $color-red500        | #FF113F                  | -                    | 红500                   |
| $color-red600        | #F5003E                  | -                    | 红600                   |
| $color-red700        | #E20038                  | -                    | 红700                   |
| $color-red700-tab    | #E20038 24%              | -                    | 红700-Tab               |
| $color-pink300       | -                        | #F730AB              | 夜间模式主题粉色300      |
| $color-pink200       | -                        | #FB76C8              | 夜间模式主题粉色200      |
| $color-pink100       | -                        | #FF9EDA              | 夜间模式主题粉色100      |
| $color-background    | #1F2022                  | #241C25              | 页面背景色              |
| $color-white         | -                        | #FFFFFF              | 夜间模式白色            |
| $surface-primary-linear-button | #F16779 ~ #F241AC | -                   | 主按钮渐变色            |
| $surface-background  | #1F2022                  | #241C25              | 页面背景色              |
| $surface-Secondary-Button | #FFFFFF 8%           | -                    | 次按钮背景色            |
| $surface-Secondary-Button-hover | #FFFFFF 24%     | -                    | 次按钮悬停色            |
| $border-subtle       | #FFFFFF 10%              | -                    | 弱边框色                |
| $border-medium       | #FFFFFF 24%              | -                    | 中等边框色              |
| $button-primary      | Noto Sans HK             | Noto Sans HK         | 主要按钮字体            |
| $font-style          | Noto Sans HK             | Noto Sans HK         | 按钮字体样式            |

> 注："-" 表示该主题下未单独定义，默认与另一主题一致或无此 Token。

---

# 字体详细设置表（不含颜色）

| Token            | 字体（Font）         | 字重（Weight） | 字号（Size） | 行高（Line height） | 字间距（Letter spacing） | 对齐方式（Horizontal align） | 其他说明         |
|------------------|---------------------|---------------|-------------|--------------------|-------------------------|-----------------------------|------------------|
| $font-title      | Chiron GoRound TC   | 700           | 36px        | 48px               | -0.4px                  | Right                       |                  |
| $font-button     | Chiron GoRound TC   | 500           | 16px        | 28px               | 1.6px                   | Right                       |                  |
| $font-input      | Noto Sans HK        | 400           | 14px        | 20px               | 0.4px                   | Right                       |                  |
| $font-Subtitle   | Noto Sans HK        | 500           | 14px        | 22px               | 0.4px                   | Right                       | Title case       |
| $font-p1-regular | Noto Sans HK        | 400           | 12px        | 16px               | 0.4px                   | Right                       |                  |

</rewritten_file> 