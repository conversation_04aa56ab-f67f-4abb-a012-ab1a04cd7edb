import os
import re
import json

# 你的 theme_tokens.md 路径
md_path = "theme_tokens.md"
# 你的 Assets.xcassets/color 路径
assets_path = "gouqi/Assets.xcassets/color"

# 解析颜色和透明度
def parse_color(color_str, opacity_str):
    if not color_str or not color_str.strip().startswith('#'):
        return None
    hex_color = color_str.strip().lstrip('#')
    if len(hex_color) != 6:
        return None
    try:
        r = int(hex_color[0:2], 16) / 255.0
        g = int(hex_color[2:4], 16) / 255.0
        b = int(hex_color[4:6], 16) / 255.0
        a = float(opacity_str) if opacity_str else 1.0
        return r, g, b, a
    except Exception:
        return None

def make_color_dict(r, g, b, a):
    return {
        "color-space": "srgb",
        "components": {
            "red": f"{r:.3f}",
            "green": f"{g:.3f}",
            "blue": f"{b:.3f}",
            "alpha": f"{a:.2f}"
        }
    }

# 读取 md 文件并提取颜色
with open(md_path, encoding="utf-8") as f:
    lines = f.readlines()

header_found = False
for idx, line in enumerate(lines):
    if line.strip().startswith("| Token"):
        header_found = True
        header_idx = idx
        break
if not header_found:
    print("未找到表头，脚本终止。")
    exit(1)

for line in lines[header_idx+2:]:
    if not line.strip().startswith("|"):
        continue
    parts = [p.strip() for p in line.strip().split("|")]
    if len(parts) < 6:
        continue
    token = parts[1]
    any_color, any_opacity = parts[2], parts[3]
    dark_color, dark_opacity = parts[4], parts[5]
    color_json = {"info": {"version": 1, "author": "xcode"}, "colors": []}
    # Any appearance
    any_rgba = parse_color(any_color, any_opacity)
    if any_rgba:
        r, g, b, a = any_rgba
        color_json["colors"].append({
            "idiom": "universal",
            "appearances": [{"appearance": "luminosity", "value": "light"}],
            "color": make_color_dict(r, g, b, a)
        })
    # Dark appearance
    dark_rgba = parse_color(dark_color, dark_opacity)
    if dark_rgba:
        r, g, b, a = dark_rgba
        color_json["colors"].append({
            "idiom": "universal",
            "appearances": [{"appearance": "luminosity", "value": "dark"}],
            "color": make_color_dict(r, g, b, a)
        })
    # 跳过无颜色的 Token
    if not color_json["colors"]:
        continue
    dir_path = os.path.join(assets_path, f"{token}.colorset")
    os.makedirs(dir_path, exist_ok=True)
    with open(os.path.join(dir_path, "Contents.json"), "w") as f:
        json.dump(color_json, f, indent=2, ensure_ascii=False)
    print(f"Generated {token}.colorset")

print("批量导入完成！") 