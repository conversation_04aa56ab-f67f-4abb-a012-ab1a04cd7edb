# macOS
.DS_Store
.AppleDouble
.LSOverride
._*

# Xcode
## Build generated
build/
Build/
DerivedData/

## User-specific files
*.xcuserdatad
*.xcworkspace
!default.xcworkspace
xcuserdata/
*.xccheckout
*.moved-aside
*.xcuserstate

# Swift Package Manager
.build/
Packages/

# Carthage
Carthage/Build
Carthage/Checkouts

# Cocoapods
Pods/
Podfile.lock

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# 忽略所有模块下的 .build 目录
Modules/*/.build/ 