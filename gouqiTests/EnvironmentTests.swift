//
//  EnvironmentTests.swift
//  gouqiTests
//
//  Created on 2025/6/16.
//

import Testing
@testable import gouqi

struct EnvironmentTests {
    
    // 测试默认环境配置
    @Test func testDefaultEnvironment() async throws {
        // 获取环境配置单例
        let environment = Environment.shared
        
        // 根据编译标志检查当前环境
        #if DEBUG
            #if TESTING
                #expect(environment.current == .testing)
                #expect(environment.current.rawValue == "测试环境")
            #else
                #expect(environment.current == .development)
                #expect(environment.current.rawValue == "开发环境")
            #endif
        #else
            #expect(environment.current == .production)
            #expect(environment.current.rawValue == "生产环境")
        #endif
    }
    
    // 测试不同环境的API URL
    @Test func testEnvironmentAPIURLs() async throws {
        // 创建一个测试专用的环境配置类，用于测试不同环境的URL
        class TestEnvironment: Environment {
            var overrideEnvironment: EnvironmentType?
            
            override var current: EnvironmentType {
                get { return overrideEnvironment ?? super.current }
                set { overrideEnvironment = newValue }
            }
        }
        
        let testEnv = TestEnvironment()
        
        // 测试开发环境
        testEnv.overrideEnvironment = .development
        #expect(testEnv.baseURL == "https://api-dev.example.com")
        #expect(testEnv.apiURL == "https://api-dev.example.com/v1")
        
        // 测试测试环境
        testEnv.overrideEnvironment = .testing
        #expect(testEnv.baseURL == "https://api-test.example.com")
        #expect(testEnv.apiURL == "https://api-test.example.com/v1")
        
        // 测试生产环境
        testEnv.overrideEnvironment = .production
        #expect(testEnv.baseURL == "https://api.example.com")
        #expect(testEnv.apiURL == "https://api.example.com/v1")
    }
    
    // 测试日志记录设置
    @Test func testLoggingConfiguration() async throws {
        // 创建一个测试专用的环境配置类
        class TestEnvironment: Environment {
            var overrideEnvironment: EnvironmentType?
            
            override var current: EnvironmentType {
                get { return overrideEnvironment ?? super.current }
                set { overrideEnvironment = newValue }
            }
        }
        
        // 测试开发环境应该记录日志
        do {
            let devEnv = TestEnvironment()
            devEnv.current = .development
            #expect(devEnv.shouldLogNetworkCalls == true, "开发环境应该启用日志记录")
        }
        
        // 测试测试环境应该记录日志
        do {
            let testEnv = TestEnvironment()
            testEnv.current = .testing
            #expect(testEnv.shouldLogNetworkCalls == true, "测试环境应该启用日志记录")
        }
        
        // 测试生产环境不应该记录日志
        do {
            let prodEnv = TestEnvironment()
            prodEnv.current = .production
            #expect(prodEnv.shouldLogNetworkCalls == false, "生产环境应该禁用日志记录")
        }
    }
} 