//
//  EnvironmentSwitchTests.swift
//  gouqiTests
//
//  Created on 2025/6/16.
//

import Testing
@testable import gouqi

struct EnvironmentSwitchTests {
    
    // 测试环境切换
    @Test func testEnvironmentSwitching() async throws {
        // 创建一个测试专用的环境配置类
        let testEnv = TestableEnvironment()
        
        // 初始状态应该是默认环境
        #if DEBUG
            #if TESTING
                #expect(testEnv.current == .testing)
            #else
                #expect(testEnv.current == .development)
            #endif
        #else
            #expect(testEnv.current == .production)
        #endif
        
        // 切换到开发环境
        testEnv.overrideEnvironment = .development
        #expect(testEnv.current == .development)
        #expect(testEnv.baseURL == "https://api-dev.example.com")
        #expect(testEnv.shouldLogNetworkCalls == true)
        
        // 切换到测试环境
        testEnv.overrideEnvironment = .testing
        #expect(testEnv.current == .testing)
        #expect(testEnv.baseURL == "https://api-test.example.com")
        #expect(testEnv.shouldLogNetworkCalls == true)
        
        // 切换到生产环境
        testEnv.overrideEnvironment = .production
        #expect(testEnv.current == .production)
        #expect(testEnv.baseURL == "https://api.example.com")
        #expect(testEnv.shouldLogNetworkCalls == false)
        
        // 重置环境
        testEnv.overrideEnvironment = nil
        #if DEBUG
            #if TESTING
                #expect(testEnv.current == .testing)
            #else
                #expect(testEnv.current == .development)
            #endif
        #else
            #expect(testEnv.current == .production)
        #endif
    }
    
    // 测试命令行参数处理
    @Test func testCommandLineArgumentHandling() async throws {
        // 模拟命令行参数
        let mockArgs = ["--UITesting", "--env=testing"]
        
        // 创建一个测试专用的环境处理器
        class TestEnvironmentHandler {
            let testEnv = TestableEnvironment()
            
            func processArguments(_ args: [String]) {
                if args.contains("--UITesting") {
                    if let envArg = args.first(where: { $0.starts(with: "--env=") }) {
                        let envValue = envArg.replacingOccurrences(of: "--env=", with: "")
                        
                        switch envValue {
                        case "development":
                            testEnv.overrideEnvironment = .development
                        case "testing":
                            testEnv.overrideEnvironment = .testing
                        case "production":
                            testEnv.overrideEnvironment = .production
                        default:
                            break
                        }
                    }
                }
            }
        }
        
        // 测试处理器
        let handler = TestEnvironmentHandler()
        handler.processArguments(mockArgs)
        
        // 验证环境被正确设置
        #expect(handler.testEnv.current == .testing)
        
        // 测试其他参数
        handler.processArguments(["--UITesting", "--env=development"])
        #expect(handler.testEnv.current == .development)
        
        handler.processArguments(["--UITesting", "--env=production"])
        #expect(handler.testEnv.current == .production)
        
        // 测试无效参数
        handler.testEnv.overrideEnvironment = nil
        handler.processArguments(["--UITesting", "--env=invalid"])
        #if DEBUG
            #if TESTING
                #expect(handler.testEnv.current == .testing)
            #else
                #expect(handler.testEnv.current == .development)
            #endif
        #else
            #expect(handler.testEnv.current == .production)
        #endif
    }
} 