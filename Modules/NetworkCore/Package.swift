// swift-tools-version: 6.1
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "NetworkCore",
    platforms: [
        .iOS(.v16),
        .macOS(.v12),
        .tvOS(.v16),
        .watchOS(.v8)
    ],
    products: [
        // Products define the executables and libraries a package produces, making them visible to other packages.
        .library(
            name: "NetworkCore",
            targets: ["NetworkCore"]),
    ],
    dependencies: [
        .package(url: "https://github.com/Alamofire/Alamofire.git", .upToNextMajor(from: "5.9.1")),
        .package(url: "https://github.com/Moya/Moya.git", .upToNextMajor(from: "15.0.3")),
        .package(path: "../LogCore")
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        // Targets can depend on other targets in this package and products from dependencies.
        .target(
            name: "NetworkCore",
            dependencies: [
                "Alamofire",
                .product(name: "<PERSON><PERSON>", package: "<PERSON><PERSON>"),
                .product(name: "CombineMoya", package: "<PERSON><PERSON>"),
                "LogCore"
            ]),
        .testTarget(
            name: "NetworkCoreTests",
            dependencies: ["NetworkCore"]
        ),
    ]
)
