import XCTest
import NetworkCore
import LogCore
@testable import NetworkCore

/// 金币套餐相关接口集成测试
final class CoinPackageIntegrationTests: XCTestCase {
    override func setUp() async throws {
        XLog.setup(destinations: [.console])
        await BassUrlAddressConfig.setEnvironment(.test)
    }

    /// 测试获取金币套餐列表接口
    func testGetCoinPackageList() {
        let expectation = XCTestExpectation(description: "获取金币套餐列表 callback 风格")
        CoinPackageRequest.list(params: [:]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.rows, "金币套餐列表不应为nil")
            case .failure(let error):
                XCTFail("获取金币套餐列表接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试新增金币套餐接口
    func testAddCoinPackage() {
        let expectation = XCTestExpectation(description: "新增金币套餐 callback 风格")
        // 这里用假数据，实际应替换为真实参数
        CoinPackageRequest.add(params: ["packageName": "test", "amount": 1, "coin": 10, "recommend": 0, "status": 1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.message)
            case .failure(let error):
                XCTFail("新增金币套餐接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试修改金币套餐接口
    func testUpdateCoinPackage() {
        let expectation = XCTestExpectation(description: "修改金币套餐 callback 风格")
        // 这里用假id，实际应替换为真实id
        CoinPackageRequest.update(params: ["id": 1, "packageName": "updated"]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.message)
            case .failure(let error):
                XCTFail("修改金币套餐接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试删除金币套餐接口
    func testDeleteCoinPackage() {
        let expectation = XCTestExpectation(description: "删除金币套餐 callback 风格")
        // 这里用假id，实际应替换为真实id
        CoinPackageRequest.delete(ids: [1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.message)
            case .failure(let error):
                XCTFail("删除金币套餐接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }
} 