import XCTest
import NetworkCore
import LogCore
@testable import NetworkCore

/// 角色相关接口集成测试
final class CharacterIntegrationTests: XCTestCase {
    override func setUp() async throws {
        XLog.setup(destinations: [.console])
        await BassUrlAddressConfig.setEnvironment(.test)
    }

    /// 测试获取角色标签接口
    func testGetCharacterTags() {
        let expectation = XCTestExpectation(description: "获取角色标签 callback 风格")
        CharacterRequest.tags { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.data, "角色标签不应为nil")
            case .failure(let error):
                XCTFail("获取角色标签接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试获取角色列表接口
    func testGetCharacterList() {
        let expectation = XCTestExpectation(description: "获取角色列表 callback 风格")
        CharacterRequest.list(params: ["pageNum": 1, "pageSize": 1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.rows, "角色列表不应为nil")
            case .failure(let error):
                XCTFail("获取角色列表接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试获取角色详情接口
    func testGetCharacterDetail() {
        let expectation = XCTestExpectation(description: "获取角色详情 callback 风格")
        // 这里用假cid，实际应替换为真实cid
        CharacterRequest.detail(cid: "testcid") { result in
            switch result {
            case .success(let response):
                // 允许data为nil（如果cid不存在）
                XCTAssertNotNil(response.code)
            case .failure(let error):
                XCTFail("获取角色详情接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试获取角色所有帖子接口
    func testGetCharacterPosts() {
        let expectation = XCTestExpectation(description: "获取角色所有帖子 callback 风格")
        // 这里用假cid，实际应替换为真实cid
        CharacterRequest.posts(cid: "testcid") { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.code)
            case .failure(let error):
                XCTFail("获取角色所有帖子接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试获取用户创建的角色接口
    func testGetUserCharacterList() {
        let expectation = XCTestExpectation(description: "获取用户创建角色 callback 风格")
        CharacterRequest.userList(params: ["pageNum": 1, "pageSize": 1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.rows)
            case .failure(let error):
                XCTFail("获取用户创建角色接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试角色搜索接口
    func testSearchCharacter() {
        let expectation = XCTestExpectation(description: "搜索角色 callback 风格")
        CharacterRequest.search(params: ["keyword": "test", "page": 1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.data)
            case .failure(let error):
                XCTFail("搜索角色接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试获取角色搜索词接口
    func testGetCharacterSearchWords() {
        let expectation = XCTestExpectation(description: "获取角色搜索词 callback 风格")
        CharacterRequest.searchWords(num: 3) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.data)
            case .failure(let error):
                XCTFail("获取角色搜索词接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }
} 