import XCTest
import NetworkCore
import LogCore
@testable import NetworkCore

/// 上传相关接口集成测试
final class UploadIntegrationTests: XCTestCase {
    override func setUp() async throws {
        XLog.setup(destinations: [.console])
        await BassUrlAddressConfig.setEnvironment(.test)
    }

    /// 测试上传文件接口
    func testUploadFile() {
        let expectation = XCTestExpectation(description: "上传文件 callback 风格")
        let dummyData = Data([0xFF, 0xD8, 0xFF])
        UploadRequest.upload(file: dummyData, fileName: "test.jpg") { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.data, "上传返回url不应为nil")
            case .failure(let error):
                XCTFail("上传接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }
} 