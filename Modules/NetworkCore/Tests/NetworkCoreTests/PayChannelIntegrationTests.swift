import XCTest
import NetworkCore
import LogCore
@testable import NetworkCore

/// 支付通道相关接口集成测试
final class PayChannelIntegrationTests: XCTestCase {
    override func setUp() async throws {
        XLog.setup(destinations: [.console])
        await BassUrlAddressConfig.setEnvironment(.test)
    }

    /// 测试获取支付通道列表接口
    func testGetPayChannelList() {
        let expectation = XCTestExpectation(description: "获取支付通道列表 callback 风格")
        PayChannelRequest.list(params: [:]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.rows, "支付通道列表不应为nil")
            case .failure(let error):
                XCTFail("获取支付通道列表接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试新增支付通道接口
    func testAddPayChannel() {
        let expectation = XCTestExpectation(description: "新增支付通道 callback 风格")
        // 这里用假数据，实际应替换为真实参数
        PayChannelRequest.add(params: ["channelName": "test", "merchNo": "test", "paymentMethod": 1, "implClassSimpleName": "test", "weight": 10, "stateCode": 1, "requestUrl": "url", "callbackUrl": "url"]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.message)
            case .failure(let error):
                XCTFail("新增支付通道接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试修改支付通道接口
    func testUpdatePayChannel() {
        let expectation = XCTestExpectation(description: "修改支付通道 callback 风格")
        // 这里用假id，实际应替换为真实id
        PayChannelRequest.update(params: ["id": 1, "channelName": "updated"]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.message)
            case .failure(let error):
                XCTFail("修改支付通道接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试删除支付通道接口
    func testDeletePayChannel() {
        let expectation = XCTestExpectation(description: "删除支付通道 callback 风格")
        // 这里用假id，实际应替换为真实id
        PayChannelRequest.delete(ids: [1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.message)
            case .failure(let error):
                XCTFail("删除支付通道接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试获取支付方式接口
    func testGetPaymentMethod() {
        let expectation = XCTestExpectation(description: "获取支付方式 callback 风格")
        PayChannelRequest.paymentMethod { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.data)
            case .failure(let error):
                XCTFail("获取支付方式接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }
} 