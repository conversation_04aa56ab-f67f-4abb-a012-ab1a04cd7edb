import XCTest
import NetworkCore
import LogCore
@testable import NetworkCore

/// 用户相关接口集成测试
final class UserIntegrationTests: XCTestCase {
    /// 测试用用户名和密码
    private let testUsername = "testuser_4852"
    private let testPassword = "TestPassword123!"

    /// 每个测试前初始化日志和设置测试环境
    override func setUp() async throws {
        XLog.setup(destinations: [.console])
        await BassUrlAddressConfig.setEnvironment(.test)
    }

    /// 测试用户登录接口
    func testUserLogin() {
        let expectation = XCTestExpectation(description: "用户登录 callback 风格")
        UserRequest.login(username: testUsername, password: testPassword) { result in
            switch result {
            case .success(let loginData):
                XCTAssertFalse(loginData.token.isEmpty)
                XCTAssertGreaterThan(loginData.user.uid, 0)
            case .failure(let error):
                XCTFail("登录接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试获取用户信息接口
    func testGetUserInfo() {
        let expectation = XCTestExpectation(description: "查询用户信息 callback 风格")
        UserRequest.getUserInfo { result in
            switch result {
            case .success(let user):
                XCTAssertGreaterThan(user.uid, 0)
                XCTAssertFalse(user.nickname.isEmpty)
            case .failure(let error):
                XCTFail("查询用户信息接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试用户登出接口
    func testUserLogout() {
        let expectation = XCTestExpectation(description: "用户登出 callback 风格")
        UserRequest.logout { result in
            switch result {
            case .success(let response):
                XCTAssertEqual(response.code, 200)
            case .failure(let error):
                XCTFail("登出接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试用户修改密码接口
//    func testUpdatePassword() {
//        let expectation = XCTestExpectation(description: "用户修改密码 callback 风格")
//        // 调用修改密码接口（此处用假数据，实际应用真实旧密码和新密码）
//        UserRequest.updatePassword(pw: "old", npw: "new") { result in
//            switch result {
//            case .success(let response):
//                XCTAssertNotNil(response.message)
//            case .failure(let error):
//                XCTFail("修改密码接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }

    /// 测试 Token 获取接口
//    func testGetToken() {
//        let expectation = XCTestExpectation(description: "Token获取 callback 风格")
//        // 调用 Token 获取接口
//        TokenRequest.getToken(username: testUsername, password: testPassword) { result in
//            switch result {
//            case .success(let tokenModel):
//                XCTAssertFalse(tokenModel.token.isEmpty, "Token不应该为空")
//            case .failure(let error):
//                XCTFail("Token获取接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }

    /// 测试网络连通性（错误码/超时等）
    func testNetworkConnectivity() {
        let expectation = XCTestExpectation(description: "网络连接 callback 风格")
        // 用错误账号密码测试网络连通性
        UserRequest.login(username: "nonexistent_user", password: "wrong_password") { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response, "应该收到响应")
            case .failure(let error):
                // 断言网络错误码
                XCTAssertNotEqual(error.code, 0)
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试邮箱验证码发送接口
    func testSendVerificationCode() {
        let expectation = XCTestExpectation(description: "邮箱验证码发送 callback 风格")
        let email = "<EMAIL>"
        UserRequest.sendVerificationCode(email: email) { result in
            switch result {
            case .success(let response):
                XCTAssertEqual(response.code, 200)
            case .failure(let error):
                XCTFail("验证码发送接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试邮箱验证码登录/注册接口
    func testVerifyCode() {
        let expectation = XCTestExpectation(description: "邮箱验证码登录 callback 风格")
        let email = "<EMAIL>"
        let code = "417086" // 测试环境下可用的验证码
        UserRequest.verifyCode(email: email, code: code) { result in
            switch result {
            case .success(let loginData):
                XCTAssertFalse(loginData.token.isEmpty)
                XCTAssertGreaterThan(loginData.user.uid, 0)
            case .failure(let error):
                XCTFail("邮箱验证码登录接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

//    /// 测试错误处理（空用户名、空密码、密码不匹配）
//    func testUserErrorHandling() {
//        let expectation = XCTestExpectation(description: "错误处理 callback 风格")
//        expectation.expectedFulfillmentCount = 3
//        // 1. 空用户名
//        UserRequest.login(username: "", password: testPassword) { result in expectation.fulfill() }
//        // 2. 空密码
//        UserRequest.login(username: testUsername, password: "") { result in expectation.fulfill() }
//        // 3. 密码不匹配
//        UserRequest.register(username: "mismatch_\(Int.random(in: 1000...9999))", password: testPassword, confirmedPassword: "DifferentPassword123!") { result in expectation.fulfill() }
//        wait(for: [expectation], timeout: 10)
//    }
} 
