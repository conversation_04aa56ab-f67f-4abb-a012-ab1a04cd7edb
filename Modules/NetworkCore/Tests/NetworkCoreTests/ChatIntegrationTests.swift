import XCTest
import NetworkCore
import LogCore
@testable import NetworkCore

/// 聊天相关接口集成测试
final class ChatIntegrationTests: XCTestCase {
    override func setUp() async throws {
        XLog.setup(destinations: [.console])
        await BassUrlAddressConfig.setEnvironment(.test)
    }

    /// 测试获取聊天消息列表接口
    func testGetChatMessageList() {
        let expectation = XCTestExpectation(description: "获取聊天消息列表 callback 风格")
        ChatRequest.messageList(params: ["cid": "test", "batchSize": 1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.data)
            case .failure(let error):
                XCTFail("获取聊天消息列表接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试获取最近聊过记录接口
    func testGetLastChatRecord() {
        let expectation = XCTestExpectation(description: "获取最近聊过记录 callback 风格")
        ChatRequest.lastRecord(params: ["pageNum": 1, "pageSize": 1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.rows)
            case .failure(let error):
                XCTFail("获取最近聊过记录接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试发送聊天内容接口
    func testSendChatMessage() {
        let expectation = XCTestExpectation(description: "发送聊天内容 callback 风格")
        ChatRequest.send(params: ["cid": "test", "msgType": "text", "replyType": "text", "message": "hello"]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.data)
            case .failure(let error):
                XCTFail("发送聊天内容接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试打招呼获取clientId接口
    func testGreet() {
        let expectation = XCTestExpectation(description: "打招呼获取clientId callback 风格")
        ChatRequest.greet(cid: "testcid") { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.data)
            case .failure(let error):
                XCTFail("打招呼接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试文本转语音接口
    func testAudio() {
        let expectation = XCTestExpectation(description: "文本转语音 callback 风格")
        ChatRequest.audio(id: "testid") { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.data)
            case .failure(let error):
                XCTFail("文本转语音接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }
} 
