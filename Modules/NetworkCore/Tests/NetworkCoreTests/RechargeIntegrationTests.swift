import XCTest
import NetworkCore
import LogCore
@testable import NetworkCore

/// 充值相关接口集成测试
final class RechargeIntegrationTests: XCTestCase {
    override func setUp() async throws {
        XLog.setup(destinations: [.console])
        await BassUrlAddressConfig.setEnvironment(.test)
    }

    /// 测试获取充值记录接口
    func testGetRechargeList() {
        let expectation = XCTestExpectation(description: "获取充值记录 callback 风格")
        RechargeRequest.userRecordList(params: ["pageNum": 1, "pageSize": 1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.rows, "充值记录不应为nil")
            case .failure(let error):
                XCTFail("获取充值记录接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }
} 