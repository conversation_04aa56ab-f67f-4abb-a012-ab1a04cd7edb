import XCTest
import NetworkCore
import LogCore
@testable import NetworkCore

/// 帖子相关接口集成测试
final class PostIntegrationTests: XCTestCase {
    override func setUp() async throws {
        XLog.setup(destinations: [.console])
        await BassUrlAddressConfig.setEnvironment(.test)
    }

    /// 测试获取帖子列表接口
    func testGetPostList() {
        let expectation = XCTestExpectation(description: "获取帖子列表 callback 风格")
        PostRequest.list(params: ["pageNum": 1, "pageSize": 1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.rows, "帖子列表不应为nil")
            case .failure(let error):
                XCTFail("获取帖子列表接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试获取帖子详情接口
    func testGetPostDetail() {
        let expectation = XCTestExpectation(description: "获取帖子详情 callback 风格")
        // 这里用假pid，实际应替换为真实pid
        PostRequest.detail(pid: 1) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.code)
            case .failure(let error):
                XCTFail("获取帖子详情接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试发帖接口
    func testUserPublishPost() {
        let expectation = XCTestExpectation(description: "发帖 callback 风格")
        // 这里用假数据，实际应替换为真实cid和内容
        PostRequest.publish(params: ["cid": "testcid", "prompt": "test content"]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.code)
            case .failure(let error):
                XCTFail("发帖接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }

    /// 测试搜索帖子接口
    func testSearchPost() {
        let expectation = XCTestExpectation(description: "搜索帖子 callback 风格")
        PostRequest.search(params: ["keyword": "test", "page": 1]) { result in
            switch result {
            case .success(let response):
                XCTAssertNotNil(response.data)
            case .failure(let error):
                XCTFail("搜索帖子接口失败: \(error)")
            }
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 10)
    }
} 
