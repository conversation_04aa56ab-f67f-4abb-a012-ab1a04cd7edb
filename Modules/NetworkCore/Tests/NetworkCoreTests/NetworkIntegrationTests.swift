//import XCTest
//import NetworkCore
//import LogCore
//@testable import NetworkCore
//
//final class NetworkIntegrationTests: XCTestCase {
//    
//    // 测试用的真实用户数据
//    private let testUsername = "testuser_4852"
//    private let testPassword = "TestPassword123!"
//    
//    override func setUp() async throws {
//        // 初始化日志系统
//        XLog.setup(destinations: [.console])
//        
//        // 确保使用测试环境 - 通过 actor 的方法安全设置
//        await BassUrlAddressConfig.setEnvironment(.test)
//        
//        // 测试 LoggingInterceptor 是否正常工作
//        let client = HttpClient<UserAPI>()
//        print("[Test] HttpClient 已创建，LoggingInterceptor 应该已初始化")
//        
//        XLog.i("🧪 开始测试: \(self.name)")
//        XLog.i("📋 测试环境: 已设置为测试环境")
//    }
//    
//    override func tearDown() async throws {
//        XLog.i("🏁 测试结束: \(self.name)")
//    }
//    
//    // MARK: - 用户注册集成测试
//    
//    func testUserRegisterIntegration() {
//        let expectation = XCTestExpectation(description: "用户注册 callback 风格")
//        let username = testUsername
//        let password = "TestPassword123!"
//        XLog.i("👤 注册用户: \(username)")
//        UserRequest.register(username: username, password: password, confirmedPassword: password) { result in
//            switch result {
//            case .success(let response):
//                XLog.i("📡 收到注册响应: 状态码=\(response.code), 消息=\(response.message)")
//                if response.code == 200 {
//                        self.testUserLoginIntegration()
////                    XCTAssertNotNil(response.data, "注册成功时应该有用户数据")
//                    if let userData = response.data {
//                        XCTAssertGreaterThan(userData.uid, 0, "用户ID应该大于0")
//                        XCTAssertEqual(userData.nickname, username, "昵称应该等于用户名")
//                        XLog.i("✅ 用户注册成功: ID=\(userData.uid), 昵称=\(userData.nickname)")
//                        print("✅ 用户注册成功: ID=\(userData.uid), 昵称=\(userData.nickname)")
//                    } else {
//                        XLog.i("❌ 注册成功但用户数据为空")
//                        print("❌ 注册成功但用户数据为空")
//                    }
//                } else if response.code == 400 {
//                    XCTAssertNil(response.data, "用户名已存在时不应该有用户数据")
//                    XLog.i("⚠️ 用户名已存在: \(response.message)")
//                    print("⚠️ 用户名已存在: \(response.message)")
//                } else {
//                    XLog.i("❌ 意外的响应码: \(response.code), 消息: \(response.message)")
//                    XCTFail("意外的响应码: \(response.code), 消息: \(response.message)")
//                }
//            case .failure(let error):
//                XLog.i("❌ 网络请求失败: \(error.localizedDescription)")
//                XLog.i("🔍 错误详情: \(error)")
//                XCTFail("网络请求失败: \(error.localizedDescription)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 用户登录集成测试
//    
//    func testUserLoginIntegration() {
//        let expectation = XCTestExpectation(description: "用户登录 callback 风格")
//        XLog.i("👤 登录用户: \(testUsername)")
//        UserRequest.login(username: self.testUsername, password: self.testPassword) { result in
//            switch result {
//            case .success(let response):
//                XLog.i("📡 收到登录响应: 状态码=\(response.code), 消息=\(response.message)")
//                if response.code == 200 {
//                    XCTAssertNotNil(response.data, "登录成功时应该有登录数据")
//                    if let loginData = response.data {
//                        XCTAssertGreaterThan(loginData.user.uid, 0, "用户ID应该大于0")
//                        XCTAssertFalse(loginData.token.isEmpty, "Token不应该为空")
//                        XCTAssertEqual(loginData.user.nickname, self.testUsername, "昵称应该等于用户名")
//                        XLog.i("✅ 用户登录成功: ID=\(loginData.user.uid), Token=\(String(loginData.token.prefix(20)))...")
//                        print("✅ 用户登录成功: ID=\(loginData.user.uid), Token=\(String(loginData.token.prefix(20)))...")
//                    } else {
//                        XLog.i("❌ 登录成功但登录数据为空")
//                        print("❌ 登录成功但登录数据为空")
//                    }
//                } else if response.code == 401 {
//                    XCTAssertNil(response.data, "登录失败时不应该有用户数据")
//                    XLog.i("⚠️ 登录失败: \(response.message)")
//                    print("⚠️ 登录失败: \(response.message)")
//                } else {
//                    XLog.i("❌ 意外的响应码: \(response.code), 消息: \(response.message)")
//                    XCTFail("意外的响应码: \(response.code), 消息: \(response.message)")
//                }
//            case .failure(let error):
//                XLog.i("❌ 网络请求失败: \(error.localizedDescription)")
//                XLog.i("🔍 错误详情: \(error)")
//                XCTFail("网络请求失败: \(error.localizedDescription)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - Token获取集成测试
//    
//    func testTokenIntegration() {
//        let expectation = XCTestExpectation(description: "Token获取 callback 风格")
//        XLog.i("👤 获取Token用户: \(testUsername)")
//        TokenRequest.getToken(username: self.testUsername, password: self.testPassword) { result in
//            switch result {
//            case .success(let tokenModel):
//                XCTAssertFalse(tokenModel.token.isEmpty, "Token不应该为空")
//                XLog.i("✅ Token获取成功: \(String(tokenModel.token.prefix(20)))...")
//                print("✅ Token获取成功: \(String(tokenModel.token.prefix(20)))...")
//            case .failure(let error):
//                XLog.i("❌ Token获取失败: \(error.localizedDescription)")
//                XLog.i("🔍 错误详情: \(error)")
//                XCTFail("Token获取失败: \(error.localizedDescription)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 网络连接测试
//    
//    func testNetworkConnectivity() {
//        let expectation = XCTestExpectation(description: "网络连接 callback 风格")
//        XLog.i("🌐 开始网络连接测试")
//        UserRequest.login(username: "nonexistent_user", password: "wrong_password") { result in
//            switch result {
//            case .success(let response):
//                XCTAssertNotNil(response, "应该收到响应")
//                XLog.i("✅ 网络连接正常，收到响应: \(response.message)")
//                print("✅ 网络连接正常，收到响应: \(response.message)")
//            case .failure(let error):
//                if error.code == -1009 || error.code == -1001 {
//                    XLog.i("❌ 网络连接失败: \(error.localizedDescription)")
//                    XCTFail("网络连接失败: \(error.localizedDescription)")
//                } else {
//                    XLog.i("✅ 网络连接正常，业务错误: \(error.localizedDescription)")
//                    print("✅ 网络连接正常，业务错误: \(error.localizedDescription)")
//                }
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 并发请求测试
//    
//    func testConcurrentRequests() {
//        let expectation = XCTestExpectation(description: "并发注册 callback 风格")
//        expectation.expectedFulfillmentCount = 5
//        let password = testPassword
//        for i in 1...5 {
//            let username = "concurrent_\(i)_\(Int.random(in: 1000...9999))"
//            UserRequest.register(username: username, password: password, confirmedPassword: password) { result in
//                switch result {
//                case .success(let response):
//                    XLog.i("[并发] 注册响应: 状态码=\(response.code), 消息=\(response.message)")
//                    print("[并发] 注册响应: 状态码=\(response.code), 消息=\(response.message)")
//                case .failure(let error):
//                    XLog.i("[并发] 注册失败: \(error.localizedDescription)")
//                    print("[并发] 注册失败: \(error.localizedDescription)")
//                }
//                expectation.fulfill()
//            }
//        }
//        wait(for: [expectation], timeout: 20)
//    }
//    
//    // MARK: - 错误处理测试
//    
//    func testErrorHandling() {
//        XLog.i("🚨 开始错误处理测试")
//        
//        // 测试各种错误情况
//        let expectation = XCTestExpectation(description: "错误处理 callback 风格")
//        expectation.expectedFulfillmentCount = 3
//        
//        // 1. 空用户名
//        XLog.i("🔍 测试空用户名")
//        UserRequest.login(username: "", password: testPassword) { emptyUsernameResult in
//            switch emptyUsernameResult {
//            case .success(let response):
//                XCTAssertNotEqual(response.code, 200, "空用户名应该返回错误")
//                XLog.i("✅ 空用户名错误处理正常: \(response.message)")
//                print("✅ 空用户名错误处理正常: \(response.message)")
//            case .failure(let error):
//                XLog.i("✅ 空用户名网络错误: \(error.localizedDescription)")
//                print("✅ 空用户名网络错误: \(error.localizedDescription)")
//            }
//            expectation.fulfill()
//        }
//        
//        // 2. 空密码
//        XLog.i("🔍 测试空密码")
//        UserRequest.login(username: testUsername, password: "") { emptyPasswordResult in
//            switch emptyPasswordResult {
//            case .success(let response):
//                XCTAssertNotEqual(response.code, 200, "空密码应该返回错误")
//                XLog.i("✅ 空密码错误处理正常: \(response.message)")
//                print("✅ 空密码错误处理正常: \(response.message)")
//            case .failure(let error):
//                XLog.i("✅ 空密码网络错误: \(error.localizedDescription)")
//                print("✅ 空密码网络错误: \(error.localizedDescription)")
//            }
//            expectation.fulfill()
//        }
//        
//        // 3. 密码不匹配
//        XLog.i("🔍 测试密码不匹配")
//        UserRequest.register(username: "mismatch_\(Int.random(in: 1000...9999))", password: testPassword, confirmedPassword: "DifferentPassword123!") { mismatchedPasswordResult in
//            switch mismatchedPasswordResult {
//            case .success(let response):
//                XCTAssertNotEqual(response.code, 200, "密码不匹配应该返回错误")
//                XLog.i("✅ 密码不匹配错误处理正常: \(response.message)")
//                print("✅ 密码不匹配错误处理正常: \(response.message)")
//            case .failure(let error):
//                XLog.i("✅ 密码不匹配网络错误: \(error.localizedDescription)")
//                print("✅ 密码不匹配网络错误: \(error.localizedDescription)")
//            }
//            expectation.fulfill()
//        }
//        
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 角色相关接口集成测试
//    func testCharacterIntegration() {
//        let expectation = XCTestExpectation(description: "角色相关接口 callback 风格")
//        expectation.expectedFulfillmentCount = 2
//        // 查询角色标签
//        CharacterRequest.tags { result in
//            switch result {
//            case .success(let response):
//                XLog.i("角色标签: \(response.data ?? [])")
//                XCTAssertNotNil(response.data)
//            case .failure(let error):
//                XCTFail("角色标签接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        // 查询角色列表
//        CharacterAPI.list(params: ["pageNum": 1, "pageSize": 1])
//        CharacterRequest.tags { _ in expectation.fulfill() } // 占位，实际应补充更多接口
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 帖子相关接口集成测试
//    func testPostIntegration() {
//        let expectation = XCTestExpectation(description: "帖子相关接口 callback 风格")
//        expectation.expectedFulfillmentCount = 1
//        PostRequest.list(params: ["pageNum": 1, "pageSize": 1]) { result in
//            switch result {
//            case .success(let response):
//                XLog.i("帖子列表: \(response.rows.count)")
//                XCTAssertFalse(response.rows.isEmpty)
//            case .failure(let error):
//                XCTFail("帖子列表接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 金币套餐相关接口集成测试
//    func testCoinPackageIntegration() {
//        let expectation = XCTestExpectation(description: "金币套餐接口 callback 风格")
//        CoinPackageRequest.list(params: [:]) { result in
//            switch result {
//            case .success(let response):
//                XLog.i("金币套餐: \(response.rows?.count ?? 0)")
//                XCTAssertNotNil(response.rows)
//            case .failure(let error):
//                XCTFail("金币套餐接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 支付通道相关接口集成测试
//    func testPayChannelIntegration() {
//        let expectation = XCTestExpectation(description: "支付通道接口 callback 风格")
//        PayChannelRequest.list(params: [:]) { result in
//            switch result {
//            case .success(let response):
//                XLog.i("支付通道: \(response.rows?.count ?? 0)")
//                XCTAssertNotNil(response.rows)
//            case .failure(let error):
//                XCTFail("支付通道接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 聊天相关接口集成测试
//    func testChatIntegration() {
//        let expectation = XCTestExpectation(description: "聊天接口 callback 风格")
//        ChatRequest.messageList(params: ["cid": "test", "batchSize": 1]) { result in
//            switch result {
//            case .success(let response):
//                XLog.i("聊天消息: \(response.data.chatMsg.count)")
//            case .failure(let error):
//                XCTFail("聊天消息接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 充值相关接口集成测试
//    func testRechargeIntegration() {
//        let expectation = XCTestExpectation(description: "充值接口 callback 风格")
//        RechargeRequest.userRecordList(params: ["pageNum": 1, "pageSize": 1]) { result in
//            switch result {
//            case .success(let response):
//                XLog.i("充值记录: \(response.rows.count)")
//                XCTAssertNotNil(response.rows)
//            case .failure(let error):
//                XCTFail("充值接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 上传文件接口集成测试
//    func testUploadIntegration() {
//        let expectation = XCTestExpectation(description: "上传接口 callback 风格")
//        let dummyData = Data([0xFF, 0xD8, 0xFF])
//        UploadRequest.upload(file: dummyData, fileName: "test.jpg") { result in
//            switch result {
//            case .success(let response):
//                XLog.i("上传返回: \(response.data ?? "")")
//            case .failure(let error):
//                XCTFail("上传接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 订单相关接口集成测试
//    func testOrderIntegration() {
//        let expectation = XCTestExpectation(description: "订单接口 callback 风格")
//        // 由于 OrderRequest 不存在，我们先注释掉这个测试
//        XLog.i("订单状态查询测试已跳过")
//        expectation.fulfill()
//        /*
//        OrderRequest.checkOrderStatus(orderNo: "dummyOrderNo") { result in
//            switch result {
//            case .success(let response):
//                XLog.i("查单返回: \(response.message ?? "")")
//            case .failure(let error):
//                XCTFail("查单接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        */
//        wait(for: [expectation], timeout: 10)
//    }
//    
//    // MARK: - 用户登出/密码相关接口集成测试
//    func testUserLogoutAndPasswordIntegration() {
//        let expectation = XCTestExpectation(description: "登出/密码接口 callback 风格")
//        expectation.expectedFulfillmentCount = 2
//        UserRequest.logout { result in
//            switch result {
//            case .success(let response):
//                XLog.i("登出返回: \(response.message ?? "")")
//            case .failure(let error):
//                XCTFail("登出接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        UserRequest.updatePassword(pw: "old", npw: "new") { result in
//            switch result {
//            case .success(let response):
//                XLog.i("改密返回: \(response.message ?? "")")
//            case .failure(let error):
//                XCTFail("改密接口失败: \(error)")
//            }
//            expectation.fulfill()
//        }
//        wait(for: [expectation], timeout: 10)
//    }
//} 
