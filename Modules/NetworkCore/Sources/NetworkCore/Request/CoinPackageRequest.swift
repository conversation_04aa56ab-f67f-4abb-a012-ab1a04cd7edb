import Foundation

/// 金币套餐相关请求封装
public struct CoinPackageRequest {
    /// 获取金币套餐列表
    /// - Parameters:
    ///   - params: 查询参数
    ///   - callback: 回调，返回 CoinPackageListResponse
    public static func list(params: [String: Any], callback: @escaping RequestCallback<CoinPackageListResponse>) {
        let client = HttpClient<CoinPackageAPI>()
        client.request(.list(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(CoinPackageListResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
    /// 新增金币套餐
    /// - Parameters:
    ///   - params: 参数
    ///   - callback: 回调，返回 CoinPackageResponse
    public static func add(params: [String: Any], callback: @escaping RequestCallback<CoinPackageResponse>) {
        let client = HttpClient<CoinPackageAPI>()
        client.request(.add(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(CoinPackageResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
    /// 修改金币套餐
    /// - Parameters:
    ///   - params: 参数
    ///   - callback: 回调，返回 CoinPackageResponse
    public static func update(params: [String: Any], callback: @escaping RequestCallback<CoinPackageResponse>) {
        let client = HttpClient<CoinPackageAPI>()
        client.request(.update(params: params), type: CoinPackageResponse.self, callback: callback)
    }
    /// 删除金币套餐
    /// - Parameters:
    ///   - ids: 套餐id数组
    ///   - callback: 回调，返回 CoinPackageResponse
    public static func delete(ids: [Int], callback: @escaping RequestCallback<CoinPackageResponse>) {
        let client = HttpClient<CoinPackageAPI>()
        client.request(.delete(ids: ids), type: CoinPackageResponse.self, callback: callback)
    }
} 