import Foundation
// import RechargeModel // 修正：无需 import 模块名，Swift 会自动识别同 module 下的文件

/// 充值相关请求封装

public struct RechargeRequest {
    /// 企业端充值
    /// - Parameters:
    ///   - params: 充值参数，包含 coinPackageId 和 paymentMethod
    ///   - callback: 回调，返回 RechargeResponse
    public static func userPay(params: [String: Any], callback: @escaping RequestCallback<RechargeResponse>) {
        let client = HttpClient<RechargeAPI>()
        client.request(.userPay(params: params), type: RechargeResponse.self, callback: callback)
    }
    
    /// 管理端充值
    /// - Parameters:
    ///   - params: 充值参数，包含 userId, paymentMethod, amountRecharged, coin
    ///   - callback: 回调，返回 RechargeResponse
    public static func adminPay(params: [String: Any], callback: @escaping RequestCallback<RechargeResponse>) {
        let client = HttpClient<RechargeAPI>()
        client.request(.adminPay(params: params), type: RechargeResponse.self, callback: callback)
    }
    
    /// 检查订单状态
    /// - Parameters:
    ///   - orderNo: 订单号
    ///   - callback: 回调，返回 RechargeOrderStatusResponse
    public static func checkOrderStatus(orderNo: String, callback: @escaping RequestCallback<RechargeOrderStatusResponse>) {
        let client = HttpClient<RechargeAPI>()
        client.request(.checkOrderStatus(orderNo: orderNo), type: RechargeOrderStatusResponse.self, callback: callback)
    }
    
    /// 查询充值记录(admin)
    /// - Parameters:
    ///   - params: 查询参数
    ///   - callback: 回调，返回 RechargeRecordResponse
    public static func adminRecordList(params: [String: Any], callback: @escaping RequestCallback<RechargeRecordResponse>) {
        let client = HttpClient<RechargeAPI>()
        client.request(.adminRecordList(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(RechargeRecordResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
    
    /// 查询充值记录(merch)
    /// - Parameters:
    ///   - params: 查询参数
    ///   - callback: 回调，返回 RechargeRecordResponse
    public static func userRecordList(params: [String: Any], callback: @escaping RequestCallback<RechargeRecordResponse>) {
        let client = HttpClient<RechargeAPI>()
        client.request(.userRecordList(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(RechargeRecordResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
} 
