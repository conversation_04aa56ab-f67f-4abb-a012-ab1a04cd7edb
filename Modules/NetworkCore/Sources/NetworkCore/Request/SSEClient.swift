import Foundation

/// SSE（Server-Sent Events）客户端，支持流式监听
public class SSEClient {
    private var task: URLSessionDataTask?
    private let url: URL
    private var onEvent: ((String) -> Void)?
    private var onError: ((Error) -> Void)?
    private var isStopped = false

    public init(url: URL) {
        self.url = url
    }

    /// 启动 SSE 监听
    /// - Parameters:
    ///   - onEvent: 收到事件回调（原始字符串）
    ///   - onError: 错误回调
    public func start(onEvent: @escaping (String) -> Void, onError: ((Error) -> Void)? = nil) {
        self.onEvent = onEvent
        self.onError = onError
        let request = URLRequest(url: url)
        let session = URLSession(configuration: .default, delegate: nil, delegateQueue: nil)
        task = session.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }
            if let error = error {
                self.onError?(error)
                return
            }
            if let data = data, let text = String(data: data, encoding: .utf8) {
                self.onEvent?(text)
            }
        }
        task?.resume()
    }

    /// 停止 SSE 监听
    public func stop() {
        isStopped = true
        task?.cancel()
    }
} 