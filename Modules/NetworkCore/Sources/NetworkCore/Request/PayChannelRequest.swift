import Foundation

/// 支付通道相关请求封装
public struct PayChannelRequest {
    /// 获取支付通道列表
    /// - Parameters:
    ///   - params: 查询参数
    ///   - callback: 回调，返回 PayChannelListResponse
    public static func list(params: [String: Any], callback: @escaping RequestCallback<PayChannelListResponse>) {
        let client = HttpClient<PayChannelAPI>()
        client.request(.list(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(PayChannelListResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
    /// 新增支付通道
    /// - Parameters:
    ///   - params: 参数
    ///   - callback: 回调，返回 PayChannelResponse
    public static func add(params: [String: Any], callback: @escaping RequestCallback<PayChannelResponse>) {
        let client = HttpClient<PayChannelAPI>()
        client.request(.add(params: params), type: PayChannelResponse.self, callback: callback)
    }
    /// 修改支付通道
    /// - Parameters:
    ///   - params: 参数
    ///   - callback: 回调，返回 PayChannelResponse
    public static func update(params: [String: Any], callback: @escaping RequestCallback<PayChannelResponse>) {
        let client = HttpClient<PayChannelAPI>()
        client.request(.update(params: params), type: PayChannelResponse.self, callback: callback)
    }
    /// 删除支付通道
    /// - Parameters:
    ///   - ids: 支付通道id数组
    ///   - callback: 回调，返回 PayChannelResponse
    public static func delete(ids: [Int], callback: @escaping RequestCallback<PayChannelResponse>) {
        let client = HttpClient<PayChannelAPI>()
        client.request(.delete(ids: ids), type: PayChannelResponse.self, callback: callback)
    }
    /// 获取支付方式
    /// - Parameter callback: 回调，返回 PayChannelPaymentMethodResponse
    public static func paymentMethod(callback: @escaping RequestCallback<PayChannelPaymentMethodResponse>) {
        let client = HttpClient<PayChannelAPI>()
        client.request(.paymentMethod, type: PayChannelPaymentMethodResponse.self, callback: callback)
    }
} 
