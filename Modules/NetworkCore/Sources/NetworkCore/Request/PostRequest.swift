import Foundation

/// 帖子相关请求封装

public struct PostRequest {
    /// 获取帖子列表
    /// - Parameters:
    ///   - params: 查询参数，包含 pageSize 和 pageNum
    ///   - callback: 回调，返回 PostListResponse
    public static func list(params: [String: Any], callback: @escaping RequestCallback<PostListResponse>) {
        let client = HttpClient<PostAPI>()
        client.request(.list(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(PostListResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
    
    /// 获取帖子详情
    /// - Parameters:
    ///   - pid: 帖子ID
    ///   - callback: 回调，返回 PostDetailResponse
    public static func detail(pid: Int, callback: @escaping RequestCallback<PostDetailResponse>) {
        let client = HttpClient<PostAPI>()
        client.request(.detail(pid: pid), type: PostDetailResponse.self, callback: callback)
    }
    
    /// 搜索帖子
    /// - Parameters:
    ///   - params: 搜索参数，包含 keyword 和 page
    ///   - callback: 回调，返回 PostSearchResponse
    public static func search(params: [String: Any], callback: @escaping RequestCallback<PostSearchResponse>) {
        let client = HttpClient<PostAPI>()
        client.request(.search(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(PostSearchResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
    
    /// 发布帖子
    /// - Parameters:
    ///   - params: 发布参数，包含 cid 和 prompt
    ///   - callback: 回调，返回 PostPublishResponse
    public static func publish(params: [String: Any], callback: @escaping RequestCallback<PostPublishResponse>) {
        let client = HttpClient<PostAPI>()
        client.request(.publish(params: params), type: PostPublishResponse.self, callback: callback)
    }
} 
