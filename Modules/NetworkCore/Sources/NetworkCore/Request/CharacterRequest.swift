import Foundation
import LogCore
// import CharacterModel // Swift 同 module 下自动识别

/// 角色相关请求封装
public struct CharacterRequest {
    /// 获取角色标签
    /// - Parameter callback: 回调，返回 CharacterTagsResponse
    public static func tags(callback: @escaping RequestCallback<CharacterTagsResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.tags, type: CharacterTagsResponse.self, callback: callback)
    }
    
    /// 获取角色列表
    /// - Parameters:
    ///   - params: 请求参数，包含 pageNum 和 pageSize
    ///   - callback: 回调，返回 CharacterListResponse
    public static func list(params: [String: Any], callback: @escaping RequestCallback<CharacterListResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.list(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(CharacterListResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
    
    /// 获取角色详情
    /// - Parameters:
    ///   - cid: 角色ID
    ///   - callback: 回调，返回 CharacterDetailResponse
    public static func detail(cid: String, callback: @escaping RequestCallback<CharacterDetailResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.detail(cid: cid), type: CharacterDetailResponse.self, callback: callback)
    }
    
    /// 获取角色所有帖子
    /// - Parameters:
    ///   - cid: 角色ID
    ///   - callback: 回调，返回 CharacterPostListResponse
    public static func posts(cid: String, callback: @escaping RequestCallback<CharacterPostListResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.posts(cid: cid), type: CharacterPostListResponse.self, callback: callback)
    }
    
    /// 获取用户创建的角色列表
    /// - Parameters:
    ///   - params: 请求参数，包含 pageNum 和 pageSize
    ///   - callback: 回调，返回 CharacterUserListResponse
    public static func userList(params: [String: Any], callback: @escaping RequestCallback<CharacterUserListResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.userList(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(CharacterUserListResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
    
    /// 搜索角色
    /// - Parameters:
    ///   - params: 请求参数，包含 keyword 和 page 
    ///   - callback: 回调，返回 CharacterSearchResponse
    public static func searchTag(params: [String: Any], callback: @escaping RequestCallback<CharacterSearchResponse>) {
        let client = HttpClient<CharacterAPI>()
        XLog.i("请求标签数据   \(params)")
        client.request(.search(params: params), type: CharacterSearchResponse.self, callback: callback)
    }
    
    /// 搜索帖子
    /// - Parameters:
    ///   - params: 请求参数，包含 keyword 和 page 
    ///   - callback: 回调，返回 PostSearchResponse
    public static func searchKeyword(params: [String: Any], callback: @escaping RequestCallback<PostSearchResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.searchKeyword(params: params), type: PostSearchResponse.self, callback: callback)
    }


    /// 获取角色搜索词
    /// - Parameters:
    ///   - num: 获取数量
    ///   - callback: 回调，返回 CharacterSearchWordsResponse
    public static func searchWords(num: Int, callback: @escaping RequestCallback<CharacterSearchWordsResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.searchWords(num: num), type: CharacterSearchWordsResponse.self, callback: callback)
    }

    /// 创建角色
    /// - Parameters:
    ///   - params: 创建角色参数，包含 nickName, gender, personalImageurl, introduction, aiStyle
    ///   - callback: 回调，返回 CharacterCreateResponse
    public static func create(params: [String: Any], callback: @escaping RequestCallback<CharacterCreateResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.create(params: params), type: CharacterCreateResponse.self, callback: callback)
    }

    /// 修改角色
    /// - Parameters:
    ///   - params: 修改角色参数，包含 cid, nickName, gender, personalImageurl, introduction, aiStyle
    ///   - callback: 回调，返回 CharacterEditResponse
    public static func edit(params: [String: Any], callback: @escaping RequestCallback<CharacterEditResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.edit(params: params), type: CharacterEditResponse.self, callback: callback)
    }

    /// 收藏/取消收藏角色
    /// - Parameters:
    ///   - cid: 角色ID
    ///   - isCollect: true收藏 false取消收藏
    ///   - callback: 回调，返回 CharacterCollectResponse
    public static func collect(cid: String, isCollect: Bool, callback: @escaping RequestCallback<CharacterCollectResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.collect(cid: cid, isCollect: isCollect), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(CharacterCollectResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
    
    /// 获取用户收藏的角色列表
    /// - Parameters:
    ///   - params: 查询参数
    ///   - callback: 回调，返回 CharacterUserCollectResponse
    public static func userCollect(params: [String: Any], callback: @escaping RequestCallback<CharacterUserCollectResponse>) {
        let client = HttpClient<CharacterAPI>()
        client.request(.userCollect(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(CharacterUserCollectResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
} 
