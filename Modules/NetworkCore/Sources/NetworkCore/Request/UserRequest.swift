import Foundation

public struct UserRequest {
    // 用户登录
    public static func login(username: String, password: String, callback: @escaping RequestCallback<UserLoginData>) {
        let request = UserLoginRequest(username: username, password: password)
        let client = HttpClient<UserAPI>()
        client.request(.login(request: request), type: UserLoginData.self, callback: callback)
    }
    
    // 查询用户信息
    public static func getUserInfo(callback: @escaping RequestCallback<UserModel>) {
        let client = HttpClient<UserAPI>()
        client.request(.getUserInfo, type: UserModel.self, callback: callback)
    }
    
    // 退出登录
    public static func logout(callback: @escaping RequestCallback<BaseResponseRaw>) {
        let client = HttpClient<UserAPI>()
        client.request(.logout, type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(BaseResponseRaw.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
    
    // 修改密码
    public static func updatePassword(pw: String, npw: String, callback: @escaping RequestCallback<BaseResponseRaw>) {
        let client = HttpClient<UserAPI>()
        client.request(.updatePassword(pw: pw, npw: npw), type: BaseResponseRaw.self, callback: callback)
    }
    
    // 邮箱验证码登录/注册 - 发送验证码
    public static func sendVerificationCode(email: String, callback: @escaping RequestCallback<BaseResponseRaw>) {
        let client = HttpClient<UserAPI>()
        client.request(.sendVerificationCode(email: email), type: BaseResponseRaw.self, callback: callback)
    }
    // 邮箱验证码登录/注册 - 验证并登录/注册
    public static func verifyCode(email: String, code: String, callback: @escaping RequestCallback<UserLoginData>) {
        let client = HttpClient<UserAPI>()
        client.request(.verifyCode(email: email, code: code), type: UserLoginData.self, callback: callback)
    }
//    // Google 登录
//    public static func googleLogin(code: String, callback: @escaping RequestCallback<UserLoginData>) {
//        let client = HttpClient<UserAPI>()
//        client.request(.googleLogin(code: code), type: UserLoginData.self, callback: callback)
//    }
    
    // Google OAuth 回调（如有）
    public static func googleCallback(params: [String: Any], callback: @escaping RequestCallback<BaseResponseRaw>) {
        let client = HttpClient<UserAPI>()
        client.request(.googleCallback(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(BaseResponseRaw.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }
} 
