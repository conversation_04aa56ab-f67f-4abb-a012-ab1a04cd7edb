import Foundation

/// Token相关请求封装

public struct TokenRequest {
    /// 获取Token（callback 风格）
    /// - Parameters:
    ///   - username: 用户名
    ///   - password: 密码
    ///   - callback: 回调，返回 TokenModel
    public static func getToken(username: String, password: String, callback: @escaping RequestCallback<TokenModel>) {
        let client = HttpClient<TokenAPI>()
        client.request(.getToken(username: username, password: password), type: TokenModel.self, callback: callback)
    }

    // Token获取 - async/await 版本（已注释）
    /*
    @available(macOS 10.15, iOS 13.0, *)
    /// 获取Token（async/await 版本）
    public static func getToken(username: String, password: String) async -> Result<TokenModel> {
        let client = HttpClient<TokenAPI>()
        return await client.request(.getToken(username: username, password: password), type: TokenModel.self)
    }
    */
} 
