import Foundation

/// 聊天相关请求封装
public struct ChatRequest {
    /// 打招呼获取 clientId（HTTP）
    /// - 参数: cid 角色ID
    /// - 回调: ChatGreetResponse，data 字段为 clientId
    public static func greet(cid: String, callback: @escaping RequestCallback<ChatGreetResponse>) {
        let client = HttpClient<ChatAPI>()
        client.request(.greet(cid: cid), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(ChatGreetResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }

    /// 用户发送聊天内容（HTTP）
    /// - 参数: params 聊天参数，包含 cid, msgType, replyType, message
    /// - 回调: ChatSendResponse
    public static func send(params: [String: Any], callback: @escaping RequestCallback<ChatSendResponse>) {
        let client = HttpClient<ChatAPI>()
        client.request(.send(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(ChatSendResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }

    /// 查询用户聊天记录（HTTP）
    /// - 参数: params 查询参数，包含 cid, anchorId, batchSize
    /// - 回调: ChatMessageListResponse
    public static func messageList(params: [String: Any], callback: @escaping RequestCallback<ChatMessageListResponse>) {
        let client = HttpClient<ChatAPI>()
        client.request(.messageList(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(ChatMessageListResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }

    /// 查询用户最近聊过的记录（HTTP）
    /// - 参数: params 查询参数，包含 pageSize, pageNum
    /// - 回调: ChatLastRecordResponse
    public static func lastRecord(params: [String: Any], callback: @escaping RequestCallback<ChatLastRecordResponse>) {
        let client = HttpClient<ChatAPI>()
        client.request(.lastRecord(params: params), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(ChatLastRecordResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }

    /// 文本转语音（HTTP）
    /// - 参数: id 消息ID
    /// - 回调: ChatAudioResponse
    public static func audio(id: String, callback: @escaping RequestCallback<ChatAudioResponse>) {
        let client = HttpClient<ChatAPI>()
        client.request(.audio(id: id), type: RawDataModel.self) { result in
            switch result {
            case .success(let raw):
                do {
                    let model = try JSONDecoder().decode(ChatAudioResponse.self, from: raw.data)
                    callback(.success(model))
                } catch {
                    callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                }
            case .failure(let err):
                callback(.failure(err))
            }
        }
    }

    /// SSE消息监听（/sse/message，流式，推荐直接用SSEClient）
    /// - 参数: clientId 打招呼接口返回的clientId
    /// - 参数: token 直接传token字符串（不带Bearer）
    /// - onEvent: 收到事件回调（原始字符串）
    /// - onError: 错误回调
    /// - 返回: SSEClient 实例，业务方可调用 stop() 主动断开
    public static func sseMessage(clientId: String, token: String, onEvent: @escaping (String) -> Void, onError: ((Error) -> Void)? = nil) -> SSEClient {
        let base = "https://your.api/sse/message"
        var components = URLComponents(string: base)!
        components.queryItems = [
            URLQueryItem(name: "clientId", value: clientId),
            URLQueryItem(name: "token", value: token)
        ]
        guard let url = components.url else {
            let dummy = SSEClient(url: URL(string: base)!)
            onError?(NSError(domain: "SSE", code: -1, userInfo: [NSLocalizedDescriptionKey: "SSE URL 构造失败"]))
            return dummy
        }
        let client = SSEClient(url: url)
        client.start(onEvent: onEvent, onError: onError)
        return client
    }

    /// SSE心跳检查（HTTP）
    /// - 参数: clientId 会话clientId，token 鉴权token
    /// - 回调: HeartbeatResponse
    public static func heartbeat(clientId: String, token: String, callback: @escaping RequestCallback<HeartbeatResponse>) {
        let urlStr = "https://your.api/sse/heartbeat?clientId=\(clientId)&token=\(token)"
        guard let url = URL(string: urlStr) else {
            callback(.failure(NetWorkError(code: -1, msg: "URL无效")))
            return
        }
        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
                return
            }
            guard let data = data else {
                callback(.failure(NetWorkError(code: -1, msg: "无数据")))
                return
            }
            do {
                let model = try JSONDecoder().decode(HeartbeatResponse.self, from: data)
                callback(.success(model))
            } catch {
                callback(.failure(NetWorkError(code: -1, msg: error.localizedDescription)))
            }
        }
        task.resume()
    }
} 
