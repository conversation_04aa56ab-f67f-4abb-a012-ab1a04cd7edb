import Foundation

/// 文件上传相关请求封装
public struct UploadRequest {
    /// 上传文件
    /// - Parameters:
    ///   - file: 文件数据
    ///   - fileName: 文件名
    ///   - callback: 回调，返回 UploadResponse
    public static func upload(file: Data, fileName: String, callback: @escaping RequestCallback<UploadResponse>) {
        let client = HttpClient<UploadAPI>()
        client.request(.upload(file: file, fileName: fileName), type: UploadResponse.self, callback: callback)
    }
} 