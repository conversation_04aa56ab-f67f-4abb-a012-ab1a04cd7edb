# API日志过滤配置使用指南

## 🎯 概述

`LoggingConfig.swift` 提供了一个简单直观的方式来控制API日志的显示，通过注释/取消注释来选择要显示的API路径。

## 📋 使用方法

### 1. 基本配置

在 `LoggingConfig.swift` 文件中，找到 `apiPaths` 数组，通过注释/取消注释来控制显示哪些API的日志：

```swift
let apiPaths: [String] = [
    // 🔍 搜索相关API
    "/search/post/only-pass",           // 帖子搜索 ✅ 显示
    "/search/character",                // 角色搜索 ✅ 显示
    
    // 👤 用户相关API
//  "/user/info",                       // 获取用户信息 ❌ 不显示
//  "/user/account/login",              // 用户登录 ❌ 不显示
]
```

### 2. 过滤模式选择

在同一文件中选择过滤模式：

```swift
// 方式1: 白名单模式 - 只显示列表中的API
return LoggingInterceptor.whitelist(apiPaths)

// 方式2: 黑名单模式 - 显示除列表外的所有API
// return LoggingInterceptor.blacklist(apiPaths)

// 方式3: 关键词模式 - 显示包含关键词的API
// return LoggingInterceptor.pathContains(["search", "user"])
```

## 🚀 快速配置

### 测试搜索功能
```swift
// 在 LoggingConfig.swift 中取消注释搜索相关的API
"/search/post/only-pass",
"/search/character", 
"/character/searchWords",
```

### 测试用户功能
```swift
// 在 LoggingConfig.swift 中取消注释用户相关的API
"/user/info",
"/user/account/login",
"/user/account/logout",
```

### 测试聊天功能
```swift
// 在 LoggingConfig.swift 中取消注释聊天相关的API
"/ark/greet",
"/ark/chat",
"/chat/message/list",
```

## 🛠 便利方法

也可以直接使用预定义的便利方法：

```swift
// 在 HttpClient.swift 中替换为：

// 只显示搜索相关日志
allPlugins.append(LoggingConfig.searchOnly())

// 只显示用户相关日志
allPlugins.append(LoggingConfig.userOnly())

// 只显示角色相关日志
allPlugins.append(LoggingConfig.characterOnly())

// 只显示聊天相关日志
allPlugins.append(LoggingConfig.chatOnly())

// 排除分析相关日志
allPlugins.append(LoggingConfig.excludeAnalytics())
```

## 📝 实际使用场景

### 场景1: 调试搜索功能
1. 打开 `LoggingConfig.swift`
2. 注释掉所有API路径
3. 只取消注释搜索相关的API：
   ```swift
   "/search/post/only-pass",
   "/search/character", 
   "/character/searchWords",
   ```
4. 运行应用，现在只会看到搜索相关的API日志

### 场景2: 调试登录流程
1. 注释掉所有API路径
2. 只取消注释用户相关的API：
   ```swift
   "/user/account/login",
   "/user/info",
   "/auth/token",
   ```

### 场景3: 完全关闭日志
```swift
// 在 createLoggingInterceptor() 方法中返回：
return LoggingInterceptor.blacklist([]) // 空黑名单 = 显示所有
// 或者
return LoggingInterceptor.whitelist([]) // 空白名单 = 不显示任何
```

## 💡 提示

1. **修改后需要重新编译**：修改配置后需要重新运行应用才能生效
2. **组合使用**：可以同时取消注释多个相关的API来调试完整的功能流程
3. **临时调试**：可以临时添加新的API路径到列表中进行调试
4. **性能考虑**：在生产环境中建议关闭不必要的日志以提高性能

## 🔧 高级用法

### 自定义过滤规则
```swift
return LoggingInterceptor.custom { target in
    // 只显示POST请求
    return target.method == .post
    
    // 只显示特定域名的请求
    // return target.baseURL.absoluteString.contains("api.example.com")
    
    // 只显示响应时间超过1秒的请求（需要结合其他拦截器）
    // return someCondition
}
```

### 只显示请求或响应
```swift
// 只显示请求日志，不显示响应日志
return LoggingInterceptor.requestOnly(filterMode: .whitelist(apiPaths))

// 只显示响应日志，不显示请求日志  
return LoggingInterceptor.responseOnly(filterMode: .whitelist(apiPaths))
```
