import Foundation

public class TokenManager : @unchecked Sendable{
    public static let shared = TokenManager()
    private let key = "token"
    private init() {}

    public func setToken(_ token: String) {
        UserDefaults.standard.set(token, forKey: key)
        notifyTokenChanged(token)
    }

    public func getToken() -> String? {
        UserDefaults.standard.string(forKey: key)
    }

    public func removeToken() {
        UserDefaults.standard.removeObject(forKey: key)
        notifyTokenChanged(nil)
    }

    // MARK: - token 变更流式监听
    private static func makeTokenStream() -> (stream: AsyncStream<String?>, continuation: AsyncStream<String?>.Continuation) {
        var continuation: AsyncStream<String?>.Continuation!
        let stream = AsyncStream<String?> { c in
            continuation = c
        }
        return (stream, continuation)
    }
    private static let tokenStreamTuple = makeTokenStream()
    public var tokenChanges: AsyncStream<String?> { Self.tokenStreamTuple.stream }
    private func notifyTokenChanged(_ token: String?) {
        Self.tokenStreamTuple.continuation.yield(token)
    }
} 
