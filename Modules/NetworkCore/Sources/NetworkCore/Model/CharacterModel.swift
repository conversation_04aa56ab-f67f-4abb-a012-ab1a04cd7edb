import Foundation

/// 角色标签数据
public typealias CharacterTagsResponse = [CharacterTag]

/// 角色创建响应数据
public typealias CharacterCreateResponse = String // ai角色id

/// 角色编辑响应数据
public typealias CharacterEditResponse = String // ai角色id

/// 角色收藏响应数据
public typealias CharacterCollectResponse = Bool

public struct Character: Codable, Sendable {
    public let cid: String?
    public let modId: String?
    public let name: String?
    public let gender: Int?
    public let avatar: String?
    public let content: String?
    public let coverUrl: String?
    public let gallery: String?
    public let contentRating: Int?
    public let status: Int?
    public let type: Int?
    public let updateTimestamp: Int?
    public let collect: Int?
    public let chat: Int?
    public let tags: String?
    public let hasVideo: Int?
    public let createByUid: Int?
    public let userIsCollect: Int?
    public let postList: [CharacterPost]?
}

/// 角色列表响应数据
/// 用于BaseResponse<CharacterListResponse>的data字段
public struct CharacterListResponse: Codable, Sendable {
    public let total: Int?
    public let rows: [Character]?
    
    public init(total: Int?, rows: [Character]?) {
        self.total = total
        self.rows = rows
    }
}

/// 角色详情响应数据
public typealias CharacterDetailResponse = Character

public struct CharacterPost: Codable, Sendable {
    public let pid: Int?
    public let cid: String?
    public let title: String?
    public let content: String?
    public let gallery: String?
    public let contentRating: Int?
    public let status: Int?
    public let modId: String?
    public let name: String?
    public let avatar: String?
    public let userIsCollect: Int?
    public let createTime: String?
}

/// 角色帖子列表响应数据
public typealias CharacterPostListResponse = Character

/// 用户角色列表响应数据 
public typealias CharacterUserListResponse = CharacterListResponse

/// 角色添加响应数据
public typealias CharacterAddResponse = String

/// 用户收藏角色响应数据
public struct CharacterUserCollectResponse: Codable {
    public let total: Int?
    public let rows: [Character]?
    
    public init(total: Int?, rows: [Character]?) {
        self.total = total
        self.rows = rows
    }
}

/// 角色搜索响应数据
public typealias CharacterSearchResponse = [Character]

/// 角色搜索词响应数据
public typealias CharacterSearchWordsResponse = [String]

public struct CharacterTag: Codable, Sendable {
    public let id: String
    public let name: String
    public let tagSort: Int?
    public let translations: String?

    public init(id: String, name: String, tagSort: Int? = nil, translations: String? = nil) {
        self.id = id
        self.name = name
        self.tagSort = tagSort
        self.translations = translations
    }
}
