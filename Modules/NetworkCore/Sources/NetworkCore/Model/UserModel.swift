import Foundation

/// 用户信息模型
public struct UserModel: Codable, Sendable {
    public let uid: Int
    public let nickname: String
    public let avatarUrl: String?
    public let bgUrl: String?
    public let gender: Int
    public let description: String
    public let exp: Int
    public let availableAmount: String?
    public let coin: Double
    public let vip: Int
    public let state: Int
    public let auth: Int
    public let authMsg: String?
    public let videoCount: Int?
    public let followsCount: Int?
    public let fansCount: Int?
    public let loveCount: Int?
    public let playCount: Int?
    
    private enum CodingKeys: String, CodingKey {
        case uid, nickname, gender, description, exp, coin, vip, state, auth, authMsg, videoCount, followsCount, fansCount, loveCount, playCount, availableAmount
        case avatarUrl = "avatar_url"
        case bgUrl = "bg_url"
    }
}

/// 用户登录请求模型
public struct UserLoginRequest: Codable, Sendable {
    public let username: String
    public let password: String
    
    public init(username: String, password: String) {
        self.username = username
        self.password = password
    }
}

/// 用户登录响应数据模型
/// 包含用户信息和令牌数据
/// 可直接与BaseResponse一起使用: BaseResponse<UserLoginData>
public struct UserLoginData: Codable, Sendable {
    public let user: UserModel
    public let token: String
    
    public init(user: UserModel, token: String) {
        self.user = user
        self.token = token
    }
} 
