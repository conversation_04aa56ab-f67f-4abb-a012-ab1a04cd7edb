import Foundation

public struct PayChannel: Codable, Sendable {
    public let id: Int?
    public let channelName: String?
    public let merchNo: String?
    public let paymentMethod: Int?
    public let implClassSimpleName: String?
    public let weight: Int?
    public let stateCode: Int?
    public let requestUrl: String?
    public let callbackUrl: String?
    public let signKey: String?
    public let encryptKey: String?
    public let key1: String?
    public let key2: String?
    public let key3: String?
    public let key4: String?
    public let key5: String?
    public let remark: String?
    public let createTime: String?
}

/// 支付渠道列表响应数据
public struct PayChannelListResponse: Codable, Sendable {
    public let total: Int?
    public let rows: [PayChannel]?
    
    public init(total: Int?, rows: [PayChannel]?) {
        self.total = total
        self.rows = rows
    }
}

/// 支付渠道操作响应数据
public typealias PayChannelResponse = String?

/// 支付渠道支付方式响应数据
public typealias PayChannelPaymentMethodResponse = [Int]? 
