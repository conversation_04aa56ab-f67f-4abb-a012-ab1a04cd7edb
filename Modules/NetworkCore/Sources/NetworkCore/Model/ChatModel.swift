import Foundation

/// 聊天消息
public struct ChatMessage: Codable, Sendable {
    public let id: String
    public let msgFrom: String
    public let msgTo: String
    public let msgType: String
    public let msgId: String?
    public let duration: Int?
    public let content: String
    public let sendTime: Int64?
    public let time: String
}

/// 打招呼响应数据类型
public typealias ChatGreetResponse = String // clientId

/// 发送聊天内容响应数据
public struct ChatSendData: Codable, Sendable {
    public let replyType: String
    public let msgId: String
    public let receiveTime: Int64
}

/// 发送聊天内容响应类型
public typealias ChatSendResponse = ChatSendData

/// 聊天记录响应类型
public struct ChatMessageListResponse: Codable, Sendable {
    public let anchorId: String
    public let hasMore: Bool
    public let chatMsg: [ChatMessage]
}

/// 聊天最近记录响应类型
public struct ChatLastRecordResponse: Codable, Sendable {
    public let total: Int
    public let rows: [ChatLastRecord]
    
    public init(total: Int, rows: [ChatLastRecord]) {
        self.total = total
        self.rows = rows
    }
}

public struct ChatLastRecord: Codable, Sendable {
    public let name: String
    public let avatar: String
    public let duration: Int?
    public let msgType: String
    public let content: String
    public let time: String
}

/// 文本转语音响应类型
public typealias ChatAudioResponse = ChatMessage

public struct ChatSSEMessageResponse: Codable,Sendable {
    public let clientId: String?
    public let data: ChatSSEMessageData?
    public let msgType: String?
}

public struct ChatSSEMessageData: Codable,Sendable {
    public let msgId: String?
    public let content: String?
}

/// SSE心跳响应类型
public typealias HeartbeatResponse = [String: String]? 
