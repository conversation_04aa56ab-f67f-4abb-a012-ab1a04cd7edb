import Foundation

public struct CoinPackage: Codable, Sendable {
    public let id: Int?
    public let packageName: String?
    public let amount: String?
    public let coin: Int?
    public let bonusCoin: Int?
    public let recommend: Int?
    public let status: Int?
    public let remark: String?
    public let createTime: String?
    public let updateTime: String?
}

/// 套餐列表响应数据
public struct CoinPackageListResponse: Codable, Sendable {
    public let total: Int?
    public let rows: [CoinPackage]?
    
    public init(total: Int?, rows: [CoinPackage]?) {
        self.total = total
        self.rows = rows
    }
}

/// 套餐操作响应数据
public typealias CoinPackageResponse = String?

/// 套餐新增响应数据
public typealias CoinPackageAddResponse = String?

/// 套餐更新响应数据
public typealias CoinPackageUpdateResponse = String?

/// 套餐删除响应数据
public typealias CoinPackageDeleteResponse = String? 
