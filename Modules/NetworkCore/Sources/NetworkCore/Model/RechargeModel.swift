import Foundation

/// 充值响应类型
public struct RechargeResponse: Codable, Sendable {
    public let payUrl: String
    public let orderNo: String
    public let expiryTime: Int
}

/// 订单状态响应类型
public typealias RechargeOrderStatusResponse = String?

/// 充值记录
public struct RechargeRecord: Codable, Sendable {
    public let userId: Int
    public let orderNo: String
    public let payChannelId: Int
    public let paymentMethod: Int
    public let rechargeType: Int
    public let amountRecharged: String
    public let coin: Int
    public let reqStateCode: Int
    public let reqStateDescription: String?
    public let reqTime: String?
    public let rechargeStateCode: Int
    public let notifyDescription: String?
    public let rechargeCompleteTime: String?
    public let upStreamPayId: String?
    public let remark: String?
    public let createUser: String?
    public let createTime: String
}

/// 充值记录响应数据
public struct RechargeRecordResponse: Codable, Sendable {
    public let total: Int
    public let rows: [RechargeRecord]
    
    public init(total: Int, rows: [RechargeRecord]) {
        self.total = total
        self.rows = rows
    }
} 
