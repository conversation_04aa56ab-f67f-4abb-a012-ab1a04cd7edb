import Foundation

/// 订单状态响应类型
public struct OrderCheckStatusResponse: Codable,Sendable {
    // 根据实际需要添加字段
    public init() {}
}

/// 企业端充值响应类型
public struct OrderUserPayResponse: Codable,Sendable {
    public let payUrl: String
    public let orderNo: String
    public let expiryTime: Int
    
    public init(payUrl: String, orderNo: String, expiryTime: Int) {
        self.payUrl = payUrl
        self.orderNo = orderNo
        self.expiryTime = expiryTime
    }
}

/// 管理端充值响应类型
public struct OrderAdminPayResponse: Codable,Sendable {
    public let payUrl: String
    public let orderNo: String
    public let expiryTime: Int
    
    public init(payUrl: String, orderNo: String, expiryTime: Int) {
        self.payUrl = payUrl
        self.orderNo = orderNo
        self.expiryTime = expiryTime
    }
} 
