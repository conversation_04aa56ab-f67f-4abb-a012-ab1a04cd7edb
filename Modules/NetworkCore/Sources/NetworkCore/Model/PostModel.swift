import Foundation

/// 帖子信息
public struct PostInfo: Codable,Sendable  {
    public let pid: String?
    public let cid: String?
    public let title: String?
    public let content: String?
    public let gallery: String?
    public let contentRating: Int?
    public let status: Int?
    public let modId: String?
    public let name: String?
    public let avatar: String?
    public let userIsCollect: Int?
    public let createTime: String?
    public let hasVideo: Int?
    public let isShow: String?
}

/// 帖子列表响应类型
/// 用于BaseResponse<PostListResponse>的data字段
public struct PostListResponse: Codable,Sendable  {
    public let total: Int
    public let rows: [PostInfo]
}

/// 帖子详情响应类型 
/// 用于BaseResponse<PostDetailResponse>的data字段
public typealias PostDetailResponse = PostInfo

/// 帖子搜索响应类型
/// 用于BaseResponse<PostSearchResponse>的data字段
public typealias PostSearchResponse = [PostInfo]

/// 帖子发布响应类型
/// 用于BaseResponse<PostPublishResponse>的data字段
public typealias PostPublishResponse = String // 帖子ID 
