import Foundation

public struct BaseResponse<T:  Codable & Sendable>: Codable, Sendable {
    public let code: Int
    public let message: String
    public let data: T?
    // 可根据实际后端返回结构扩展字段
    public init(code: Int, message: String, data: T?) {
        self.code = code
        self.message = message
        self.data = data
    }
}

/// 原始数据模型，专用于直接获取原始 Data，不参与 Codable/协议解析
/// 用法：当接口返回类型为 RawDataModel 时，直接返回原始 Data，由调用方自行处理序列化
public struct RawDataModel: Codable, Sendable {
    /// 原始响应数据
    public let data: Data
    public init(data: Data) {
        self.data = data
    }
} 
