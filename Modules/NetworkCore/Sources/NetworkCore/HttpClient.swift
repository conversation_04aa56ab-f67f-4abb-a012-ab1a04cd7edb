// 主网络客户端 
import Foundation
import Moya
import LogCore

// 基础协议模型
public struct BaseResponseRaw: Codable,Sendable {
    public let code: Int
    public let message: String
}

public class HttpClient<T: TargetType> {
    public let provider: MoyaProvider<T>
    
    public init(plugins: [PluginType] = []) {
        // 可根据 NetWorkConfig 配置超时、环境等
        let session = MoyaProvider<T>.defaultAlamofireSession()
        var allPlugins = plugins
        // 注册拦截器，顺序如下：
        allPlugins.append(LoggingConfig.createLoggingInterceptor())  // 日志 - 使用配置化的日志拦截器
        allPlugins.append(NetworkStatusInterceptor())    // 网络状态检查
        allPlugins.append(MockInterceptor())             // Mock 支持
        allPlugins.append(HeaderInterceptor())           // Header/Token
        allPlugins.append(CryptoInterceptor())           // 加密解密
        allPlugins.append(PerformanceInterceptor())      // 性能监控
        allPlugins.append(ErrorCodeInterceptor())        // 错误码统一处理
        allPlugins.append(RetryInterceptor())            // 自动重试
        self.provider = MoyaProvider<T>(session: session, plugins: allPlugins)
    }
    
    // async/await 风格
    @available(macOS 10.15, iOS 13.0, *)
    public func request<M: Codable & Sendable>(_ target: T, type: M.Type) async -> Result<M> {
        await withCheckedContinuation { continuation in
            provider.request(target) { result in
                switch result {
                case .success(let response):
                    // 优先处理 RawDataModel，直接返回原始 Data
                    if M.self == RawDataModel.self, let raw = RawDataModel(data: response.data) as? M {
                        continuation.resume(returning: .success(raw))
                        return
                    }
                    do {
                        // 先尝试解析为 BaseResponse<M>
                        let base = try JSONDecoder().decode(BaseResponse<M>.self, from: response.data)
                        if base.code == 200, let model = base.data {
                            continuation.resume(returning: .success(model))
                        } else {
                            XLog.e("HttpClient: 请求失败，code: \(base.code), message: \(base.message)")
                            continuation.resume(returning: .failure(NetWorkError(code: base.code, msg: base.message)))
                        }
                    } catch {
                        let rawString = String(data: response.data, encoding: .utf8) ?? "<无法转为字符串>"
                        XLog.e("HttpClient: 解析失败，statusCode: \(response.statusCode), error: \(error.localizedDescription), 原始data: \(rawString), 目标Model: \(M.self)")
                        continuation.resume(returning: .failure(NetWorkError(code: response.statusCode, msg: error.localizedDescription)))
                    }
                case .failure(let error):
                    XLog.e("HttpClient: 网络请求失败，statusCode: \(error.response?.statusCode ?? -1), error: \(error.localizedDescription)")
                    continuation.resume(returning: .failure(NetWorkError(code: error.response?.statusCode ?? -1, msg: error.localizedDescription)))
                }
            }
        }
    }
    
    // callback 风格，先解析基础协议，再二次解析 data
    public func request<M: Codable & Sendable>(_ target: T, type: M.Type, callback: @escaping RequestCallback<M>) {
        provider.request(target) { result in
            switch result {
            case .success(let response):
                do {
                      if M.self == RawDataModel.self, let raw = RawDataModel(data: response.data) as? M {
                         callback(.success(raw))
                        return
                    }
                    let base = try JSONDecoder().decode(BaseResponse<M>.self, from: response.data)
                    
                    if M.self == BaseResponseRaw.self ,let raw = BaseResponseRaw(code: base.code, message: base.message) as? M{
                        callback(.success(raw))
                        return
                    }
                    if base.code == 200, let model = base.data {
                        callback(.success(model))
                    } else {
                        
                        XLog.e("HttpClient: 请求失败，code: \(base.code), message: \(base.message)")
                        callback(.failure(NetWorkError(code: base.code, msg: base.message)))
                    }
                } catch {
                    let rawString = String(data: response.data, encoding: .utf8) ?? "<无法转为字符串>"
                    XLog.e("HttpClient: 解析失败，statusCode: \(response.statusCode), error: \(error.localizedDescription), 原始data: \(rawString), 目标Model: \(M.self)")
                    callback(.failure(NetWorkError(code: response.statusCode, msg: error.localizedDescription)))
                }
            case .failure(let error):
                XLog.e("HttpClient: 网络请求失败，statusCode: \(error.response?.statusCode ?? -1), error: \(error.localizedDescription)")
                callback(.failure(NetWorkError(code: error.response?.statusCode ?? -1, msg: error.localizedDescription)))
            }
        }
    }
} 
