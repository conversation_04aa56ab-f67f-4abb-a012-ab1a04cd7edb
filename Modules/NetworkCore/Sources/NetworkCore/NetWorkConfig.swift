// 网络环境与全局配置 
import Foundation

public enum NetWorkEnvironment {
    case test
    case pro
}


public actor  NetWorkConfig {
    // 这里主要处理环境切换，网络请求时间，重连次数，以及各种额外设置
    public static var curEnvironment: NetWorkEnvironment = .test
    public static var connectTimeout: TimeInterval = 10
    public static var readTimeout: TimeInterval = 15
    public static var writeTimeout: TimeInterval = 15
    // 可扩展更多配置项
} 
