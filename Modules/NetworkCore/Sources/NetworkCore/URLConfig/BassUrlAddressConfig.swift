import Foundation


public actor Bass<PERSON><PERSON><PERSON>ddressConfig {
    public static var curEnvironment: NetWorkEnvironment = .test
    public static var API_KEY: String {
        switch curEnvironment {
        case .test: return "test-key"
        case .pro: return "pro-key"
        }
    }
    public static var API_SECRET: String {
        switch curEnvironment {
        case .test: return "test-secret"
        case .pro: return "pro-secret"
        }
    }
    public static var baseURL: String {
        switch curEnvironment {//
        case .test: return "http://47.245.112.102:7070"
        case .pro: return "http://47.245.112.102:7070"
        }
    }
 
    public static func setEnvironment(_ env: NetWorkEnvironment) async {
        curEnvironment = env
    }
} 
