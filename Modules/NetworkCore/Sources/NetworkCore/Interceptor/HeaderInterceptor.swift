import Foundation
import Moya

public final class HeaderInterceptor: PluginType {
    public init() {}
    public func prepare(_ request: URLRequest, target: TargetType) -> URLRequest {
        var req = request
        // 动态添加 token
        if let token = TokenManager.shared.getToken(), !token.isEmpty {
            req.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        return req
    }
} 