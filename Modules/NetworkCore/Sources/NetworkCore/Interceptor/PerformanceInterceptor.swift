import Foundation
import Moya
import LogCore

public final class PerformanceInterceptor: PluginType {
    private var startTimes: [String: Date] = [:]
    public init() {}
    public func willSend(_ request: RequestType, target: TargetType) {
        // 记录请求开始时间
        startTimes[target.path] = Date()
        XLog.i("[PerformanceInterceptor] willSend: \(target.path) 开始计时")
    }
    public func didReceive(_ result: Swift.Result<Response, MoyaError>, target: TargetType) {
        // 计算耗时并输出
        if let start = startTimes[target.path] {
            let duration = Date().timeIntervalSince(start)
            XLog.i("[PerformanceInterceptor] didReceive: \(target.path) 耗时: \(String(format: "%.3f", duration)) 秒")
            startTimes.removeValue(forKey: target.path)
        } else {
            XLog.i("[PerformanceInterceptor] didReceive: \(target.path) 未找到开始时间")
        }
    }
} 