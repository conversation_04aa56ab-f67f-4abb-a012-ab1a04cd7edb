import Foundation
import Moya

public final class NetworkStatusInterceptor: PluginType {
    public init() {
        print("[NetworkStatusInterceptor] 初始化")
    }
    
    public func prepare(_ request: URLRequest, target: TargetType) -> URLRequest {
        print("[NetworkStatusInterceptor] prepare 被调用: \(target.path)")
        
        if !NetworkMonitor.shared.isReachable {
            // 输出日志或通知
            print("[NetworkStatusInterceptor] 当前无网络，阻止请求: \(target.path)")
            // 这里可以抛出自定义错误，或通过其他方式通知上层
            // 例如：throw NetWorkError(code: -1009, msg: "无网络连接")
        } else {
            print("[NetworkStatusInterceptor] 网络状态正常: \(target.path)")
        }
        
        return request
    }
} 