import Foundation
import Moya

public final class ErrorCodeInterceptor: PluginType {
    public init() {}
    public func didReceive(_ result: Swift.Result<Response, MoyaError>, target: TargetType) {
        if case .success(let response) = result {
            if response.statusCode == 401 {
                // 触发全局 token 失效通知
                NotificationCenter.default.post(name: .tokenInvalid, object: nil)
            }
        }
    }
}

extension Notification.Name {
    static let tokenInvalid = Notification.Name("TokenInvalidNotification")
} 
