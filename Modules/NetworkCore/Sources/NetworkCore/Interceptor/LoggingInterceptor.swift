import Foundation
import Moya
import LogCore

/**
 LoggingInterceptor - 网络请求日志拦截器，支持灵活的过滤功能

 使用示例：

 1. 显示所有API日志（默认）：
    let interceptor = LoggingInterceptor()

 2. 只显示特定API路径的日志：
    let interceptor = LoggingInterceptor.whitelist(["/api/search", "/api/user"])

 3. 排除特定API路径的日志：
    let interceptor = LoggingInterceptor.blacklist(["/api/analytics", "/api/tracking"])

 4. 只显示路径包含特定关键词的日志：
    let interceptor = LoggingInterceptor.pathContains(["search", "recommend"])

 5. 只显示请求日志，不显示响应日志：
    let interceptor = LoggingInterceptor.requestOnly()

 6. 自定义过滤规则：
    let interceptor = LoggingInterceptor.custom { target in
        // 只记录POST请求的日志
        return target.method == .post
    }

 7. 组合使用：
    let interceptor = LoggingInterceptor(
        filterMode: .pathContains(["search"]),
        enableRequestLog: true,
        enableResponseLog: false
    )
 */

// 用于包装任意 Encodable 对象，便于 JSONEncoder.encode
private struct AnyEncodable: Encodable {
    private let _encode: (Encoder) throws -> Void
    init(_ encodable: Encodable) {
        self._encode = encodable.encode
    }
    func encode(to encoder: Encoder) throws {
        try _encode(encoder)
    }
}

public final class LoggingInterceptor: PluginType {

    // MARK: - 过滤配置

    /// 日志过滤模式
    public enum FilterMode {
        case all                    // 显示所有API日志（默认）
        case whitelist([String])    // 白名单：只显示指定路径的API日志
        case blacklist([String])    // 黑名单：不显示指定路径的API日志
        case pathContains([String]) // 路径包含：只显示路径包含指定字符串的API日志
        case custom((TargetType) -> Bool) // 自定义过滤函数
    }

    private let filterMode: FilterMode
    private let enableRequestLog: Bool
    private let enableResponseLog: Bool

    /// 初始化LoggingInterceptor
    /// - Parameters:
    ///   - filterMode: 过滤模式，默认显示所有
    ///   - enableRequestLog: 是否启用请求日志，默认true
    ///   - enableResponseLog: 是否启用响应日志，默认true
    public init(
        filterMode: FilterMode = .all,
        enableRequestLog: Bool = true,
        enableResponseLog: Bool = true
    ) {
        self.filterMode = filterMode
        self.enableRequestLog = enableRequestLog
        self.enableResponseLog = enableResponseLog
        print("[LoggingInterceptor] 初始化 - 过滤模式: \(filterMode)")
    }

    /// 检查是否应该记录该API的日志
    private func shouldLog(target: TargetType) -> Bool {
        switch filterMode {
        case .all:
            return true

        case .whitelist(let paths):
            return paths.contains(target.path)

        case .blacklist(let paths):
            return !paths.contains(target.path)

        case .pathContains(let keywords):
            return keywords.contains { keyword in
                target.path.contains(keyword)
            }

        case .custom(let filter):
            return filter(target)
        }
    }
    
    public func willSend(_ request: Moya.RequestType, target: Moya.TargetType) {
        // 检查是否应该记录请求日志
        guard enableRequestLog && shouldLog(target: target) else {
            return
        }

        var logInfo: [String] = []
        logInfo.append("\n================= 🚀 [API Request] =================")
        logInfo.append("➡️  [API] \(target.method.rawValue) \(target.baseURL)\(target.path)")
        logInfo.append("--------------------------------------------------")
        if let headers = target.headers {
            logInfo.append("🟦 Headers:")
            for (key, value) in headers {
                logInfo.append("    \(key): \(value)")
            }
        }
        switch target.task {
            case let .requestParameters(parameters, _):
                logInfo.append("🟩 Parameters:")
                logInfo.append("    \(parameters)")
            case .requestPlain:
                logInfo.append("🟩 Parameters: <空>")
            case let .requestCompositeParameters(bodyParameters, _, urlParameters):
                logInfo.append("🟩 Body参数:")
                logInfo.append("    \(bodyParameters)")
                logInfo.append("🟦 URL参数:")
                logInfo.append("    \(urlParameters)")
            case let .uploadMultipart(multipartData):
                logInfo.append("🟧 Multipart:")
                for part in multipartData {
                    logInfo.append("    name: \(part.name), fileName: \(part.fileName ?? "<无>")")
                }
            case let .requestData(data):
                if let str = String(data: data, encoding: .utf8) {
                    logInfo.append("🟩 Body Data:")
                    logInfo.append("    \(str)")
                } else {
                    logInfo.append("🟩 Body Data: <二进制数据，非UTF8>")
                }
            default:
                // 针对 requestJSONEncodable/Encodable/CustomJSONEncodable 类型美化输出
                let mirror = Mirror(reflecting: target.task)
                let label = String(describing: mirror.subjectType)
                if label.contains("requestJSONEncodable") || label.contains("requestCustomJSONEncodable") || label.contains("requestCompositeEncodable") {
                    // 尝试提取 Encodable 对象并序列化
                    if let child = mirror.children.first, let encodable = child.value as? Encodable {
                        let encoder = JSONEncoder()
                        encoder.outputFormatting = .prettyPrinted
                        if let data = try? encoder.encode(AnyEncodable(encodable)), let json = String(data: data, encoding: .utf8) {
                            logInfo.append("🟩 JSON Body:")
                            logInfo.append("    \(json.replacingOccurrences(of: "\n", with: "\n    "))")
                        } else {
                            logInfo.append("🟦 其他 Task 类型: \(target.task)")
                        }
                    } else {
                        logInfo.append("🟦 其他 Task 类型: \(target.task)")
                    }
                } else {
                    logInfo.append("🟦 其他 Task 类型: \(target.task)")
                }
            }
        logInfo.append("--------------------------------------------------")
        logInfo.append("[LoggingInterceptor] willSend 完成")
        logInfo.append("==================================================\n")
        XLog.i(.init(stringLiteral: logInfo.joined(separator: "\n")))
    }
    
    public func didReceive(_ result: Swift.Result<Response, MoyaError>, target: TargetType) {
        // 检查是否应该记录响应日志
        guard enableResponseLog && shouldLog(target: target) else {
            return
        }

        var logInfo: [String] = []
        logInfo.append("\n================= 📦 [API Response] ================")
        logInfo.append("⬅️  [API] \(target.method.rawValue) \(target.baseURL)\(target.path)")
        logInfo.append("--------------------------------------------------")
        switch result {
        case .success(let response):
            logInfo.append("✅ 状态码: \(response.statusCode)")
            logInfo.append("📏 响应大小: \(response.data.count) bytes")
            if let headers = response.response?.allHeaderFields {
                logInfo.append("🟦 Headers:")
                for (key, value) in headers {
                    logInfo.append("    \(key): \(value)")
                }
            }
            if let jsonString = String(data: response.data, encoding: .utf8) {
                logInfo.append("🟩 Body:")
                logInfo.append("    \(jsonString)")
            }
        case .failure(let error):
            logInfo.append("❌ 请求失败: \(error.localizedDescription)")
            switch error {
            case .statusCode(let response):
                logInfo.append("📊 错误状态码: \(response.statusCode)")
                if let errorData = String(data: response.data, encoding: .utf8) {
                    logInfo.append("🟩 错误响应:")
                    logInfo.append("    \(errorData)")
                }
            case .underlying(let underlyingError, _):
                logInfo.append("🔍 底层错误: \(underlyingError.localizedDescription)")
            default:
                logInfo.append("🔍 其他错误: \(error)")
            }
        }
        logInfo.append("--------------------------------------------------")
        logInfo.append("[LoggingInterceptor] didReceive 完成")
        logInfo.append("==================================================\n")
        XLog.i(.init(stringLiteral: logInfo.joined(separator: "\n")))
    }
}

// MARK: - 便利方法
extension LoggingInterceptor {

    /// 创建只记录特定API路径的LoggingInterceptor
    /// - Parameter paths: 要记录的API路径数组，例如 ["/api/search", "/api/user"]
    /// - Returns: 配置好的LoggingInterceptor实例
    public static func whitelist(_ paths: [String]) -> LoggingInterceptor {
        return LoggingInterceptor(filterMode: .whitelist(paths))
    }

    /// 创建排除特定API路径的LoggingInterceptor
    /// - Parameter paths: 要排除的API路径数组
    /// - Returns: 配置好的LoggingInterceptor实例
    public static func blacklist(_ paths: [String]) -> LoggingInterceptor {
        return LoggingInterceptor(filterMode: .blacklist(paths))
    }

    /// 创建只记录路径包含特定关键词的LoggingInterceptor
    /// - Parameter keywords: 关键词数组，例如 ["search", "user"]
    /// - Returns: 配置好的LoggingInterceptor实例
    public static func pathContains(_ keywords: [String]) -> LoggingInterceptor {
        return LoggingInterceptor(filterMode: .pathContains(keywords))
    }

    /// 创建只记录请求日志的LoggingInterceptor
    /// - Parameter filterMode: 过滤模式
    /// - Returns: 配置好的LoggingInterceptor实例
    public static func requestOnly(filterMode: FilterMode = .all) -> LoggingInterceptor {
        return LoggingInterceptor(filterMode: filterMode, enableRequestLog: true, enableResponseLog: false)
    }

    /// 创建只记录响应日志的LoggingInterceptor
    /// - Parameter filterMode: 过滤模式
    /// - Returns: 配置好的LoggingInterceptor实例
    public static func responseOnly(filterMode: FilterMode = .all) -> LoggingInterceptor {
        return LoggingInterceptor(filterMode: filterMode, enableRequestLog: false, enableResponseLog: true)
    }

    /// 创建自定义过滤的LoggingInterceptor
    /// - Parameter filter: 自定义过滤函数，返回true表示记录该API日志
    /// - Returns: 配置好的LoggingInterceptor实例
    public static func custom(_ filter: @escaping (TargetType) -> Bool) -> LoggingInterceptor {
        return LoggingInterceptor(filterMode: .custom(filter))
    }
}
