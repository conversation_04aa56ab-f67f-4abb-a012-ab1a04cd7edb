import Foundation
import Moya

public final class MockInterceptor: PluginType {
    public init() {}
    public func prepare(_ request: URLRequest, target: TargetType) -> URLRequest {
        // 检查是否需要 mock，若需要可直接返回 mock 响应
        // if isMock(target) { ... }
        return request
    }
    public func didReceive(_ result: Swift.Result<Response, MoyaError>, target: TargetType) {
        // 可在此处处理 mock 响应
    }
} 