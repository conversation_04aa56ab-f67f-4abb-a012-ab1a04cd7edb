import Foundation
import Moya

public final class CryptoInterceptor: PluginType {
    public init() {}
    public func prepare(_ request: URLRequest, target: TargetType) -> URLRequest {
        var req = request
        // 在此处对 req.httpBody 进行加密处理
        // req.httpBody = encrypt(req.httpBody)
        return req
    }
    public func didReceive(_ result: Swift.Result<Response, MoyaError>, target: TargetType) {
        // 在此处对 response.data 进行解密处理
        // if case .success(let response) = result { let decrypted = decrypt(response.data) }
    }
} 