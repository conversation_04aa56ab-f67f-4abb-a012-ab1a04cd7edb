import Foundation

/**
 API日志过滤配置
 
 使用方法：
 1. 在 HttpClient.swift 中将 LoggingInterceptor() 替换为 LoggingConfig.createLoggingInterceptor()
 2. 通过注释/取消注释下面的路径来控制显示哪些API的日志
 3. 可以选择不同的过滤模式：whitelist（白名单）、blacklist（黑名单）、pathContains（路径包含）
 */
public struct LoggingConfig {
    
    /// 创建配置好的LoggingInterceptor
    /// 通过注释/取消注释下面的路径来控制日志显示
    public static func createLoggingInterceptor() -> LoggingInterceptor {
        
        // ==================== 🎯 API路径配置 ====================
        // 💡 通过注释/取消注释来控制显示哪些API的日志
        
        let apiPaths: [String] = [
            
            // 🔍 搜索相关API
//             "/search/post/only-pass",           // 帖子搜索
//            "/search/character",                // 角色搜索
//            "/character/searchWords",           // 角色搜索词
            
            // 👤 用户相关API
//            "/user/info",                       // 获取用户信息
//            "/user/profile",                    // 用户资料
//            "/user/account/login",              // 用户登录
//            "/user/account/logout",             // 用户登出
//            "/user/password/update",            // 更新密码
//            "/user/account/sendVerificationCode", // 发送验证码
//            "/user/login/verification",         // 验证码登录
//            "/user/oauth/google/callback",      // Google登录回调
            
            // 🎭 角色相关API
            // "/character/tags",                  // 角色标签
//            "/character/list",                  // 角色列表
//            "/character/user/list",             // 用户角色列表
//            "/character/user/add",              // 创建角色
//            "/character/user/edit",             // 编辑角色
//            "/character/collect",               // 收藏角色
//            "/character/userCollect",           // 用户收藏的角色
            
            // 📝 帖子相关API
//            "/post/list",                       // 帖子列表
//            "/post/user/publish",               // 发布帖子
            
            // 💬 聊天相关API
//            "/ark/greet",                       // 打招呼
//            "/ark/chat",                        // 发送聊天
//            "/ark/audio",                       // 文本转语音
//            "/chat/message/list",               // 聊天记录
//            "/chat/message/lastRecord",         // 最近聊天记录
            
            // 💰 支付相关API
//            "/coinPackage/list",                // 金币包列表
//            "/coinPackage/add",                 // 添加金币包
//            "/coinPackage/update",              // 更新金币包
//            "/coinPackage/delete",              // 删除金币包
//            "/payChannel/list",                 // 支付渠道列表
//            "/payChannel/add",                  // 添加支付渠道
//            "/payChannel/update",               // 更新支付渠道
//            "/payChannel/delete",               // 删除支付渠道
//            "/payChannel/paymentMethod",        // 支付方式
//            "/userRechargeOrder/user/pay",      // 用户支付
//            "/userRechargeOrder/admin/pay",     // 管理员支付
//            "/userRechargeOrder/user/checkOrderStatus", // 检查订单状态
//            "/userRechargeOrder/list",          // 充值记录
            
            // 🔐 认证相关API
//            "/auth/token",                      // 获取Token
        ]
        
        // ==================== 📋 过滤模式选择 ====================
        // 💡 选择一种过滤模式，注释掉其他的
        
        // 方式1: 白名单模式 - 只显示上面列表中的API日志
        return LoggingInterceptor.whitelist(apiPaths)
        
        // 方式2: 黑名单模式 - 显示除了上面列表之外的所有API日志
//        return LoggingInterceptor.blacklist(apiPaths)
        
        // 方式3: 路径包含模式 - 显示路径包含指定关键词的API日志
//        let keywords = ["search", "recommend", "character"]
//        return LoggingInterceptor.pathContains(keywords)
        
        // 方式4: 只显示请求日志，不显示响应日志
//        return LoggingInterceptor.requestOnly(filterMode: .whitelist(apiPaths))
        
        // 方式5: 只显示响应日志，不显示请求日志
//        return LoggingInterceptor.responseOnly(filterMode: .whitelist(apiPaths))
        
        // 方式6: 显示所有API日志（默认行为）
//        return LoggingInterceptor()
        
        // 方式7: 自定义过滤规则
//        return LoggingInterceptor.custom { target in
//            // 例如：只显示POST请求的日志
//            return target.method == .post
//        }
    }
    
    // ==================== 🛠 便利方法 ====================
    
    /// 快速配置：只显示搜索相关的API日志
    public static func searchOnly() -> LoggingInterceptor {
        return LoggingInterceptor.pathContains(["search", "searchWords"])
    }
    
    /// 快速配置：只显示用户相关的API日志
    public static func userOnly() -> LoggingInterceptor {
        return LoggingInterceptor.pathContains(["user", "auth", "login", "oauth"])
    }
    
    /// 快速配置：只显示角色相关的API日志
    public static func characterOnly() -> LoggingInterceptor {
        return LoggingInterceptor.pathContains(["character"])
    }
    
    /// 快速配置：只显示聊天相关的API日志
    public static func chatOnly() -> LoggingInterceptor {
        return LoggingInterceptor.pathContains(["chat", "ark"])
    }
    
    /// 快速配置：只显示支付相关的API日志
    public static func paymentOnly() -> LoggingInterceptor {
        return LoggingInterceptor.pathContains(["coin", "pay", "recharge"])
    }
    
    /// 快速配置：排除性能监控和分析相关的API日志
    public static func excludeAnalytics() -> LoggingInterceptor {
        return LoggingInterceptor.blacklist([
            "/analytics",
            "/tracking",
            "/metrics",
            "/performance"
        ])
    }
}
