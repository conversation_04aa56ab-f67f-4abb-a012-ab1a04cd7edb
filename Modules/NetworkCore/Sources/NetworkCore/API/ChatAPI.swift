import Foundation
import Moya

/// 聊天相关API
public enum ChatAPI {
    /// 打招呼获取clientId
    case greet(cid: String)
    /// 发送聊天内容
    case send(params: [String: Any])
    /// 获取聊天记录
    case messageList(params: [String: Any])
    /// 获取最近聊天记录
    case lastRecord(params: [String: Any])
    /// 文本转语音
    case audio(id: String)
}


extension ChatAPI: TargetType {
    public var baseURL: URL {
        return URL(string: BassUrlAddressConfig.baseURL)!
    }
    public var path: String {
        switch self {
        case .greet:
            return "/ark/greet"
        case .send:
            return "/ark/chat"
        case .messageList:
            return "/chat/message/list"
        case .lastRecord:
            return "/chat/message/lastRecord"
        case .audio:
            return "/ark/audio"
        }
    }
    public var method: Moya.Method {
        switch self {
        case .send:
            return .post
        default:
            return .get
        }
    }
    public var task: Task {
        switch self {
        case .messageList(let params), .lastRecord(let params):
            return .requestParameters(parameters: params, encoding: URLEncoding.default)
        case .send(let params):
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        case .audio(let id):
            return .requestParameters(parameters: ["id": id], encoding: URLEncoding.default)
        case .greet(let cid):
            return .requestParameters(parameters: ["cid": cid], encoding: URLEncoding.default)
        }
    }
    public var headers: [String: String]? {
        return ["Content-Type": "application/json"]
    }
    public var sampleData: Data {
        return Data()
    }
} 
