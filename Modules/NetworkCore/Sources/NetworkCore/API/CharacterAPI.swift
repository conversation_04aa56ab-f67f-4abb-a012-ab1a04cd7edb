import Foundation
import Moya

/// 角色相关API
public enum CharacterAPI {
    /// 获取角色标签
    case tags
    /// 获取角色列表
    case list(params: [String: Any])
    /// 获取角色详情
    case detail(cid: String)
    /// 获取角色所有帖子
    case posts(cid: String)
    /// 获取用户创建的角色列表
    case userList(params: [String: Any])
    /// 搜索角色
    case search(params: [String: Any])
    ///搜索帖子
    case searchKeyword(params: [String: Any])
    /// 获取角色搜索词
    case searchWords(num: Int)
    /// 创建角色
    case create(params: [String: Any])
    /// 修改角色
    case edit(params: [String: Any])
    /// 收藏/取消收藏角色
    case collect(cid: String, isCollect: Bool)
    /// 获取用户收藏的角色列表
    case userCollect(params: [String: Any])
}


extension CharacterAPI: TargetType {
    public var baseURL: URL {
        return URL(string: BassUrlAddressConfig.baseURL)!
    }
    public var path: String {
        switch self {
        case .tags:
            return "/character/tags"
        case .posts(let cid):
            return "/character/post/\(cid)"
        case .list:
            return "/character/list"
        case .detail(let cid):
            return "/character/\(cid)"
        case .userList:
            return "/character/user/list"
        case .create:
            return "/character/user/add"
        case .edit:
            return "/character/user/edit"
        case .collect:
            return "/character/collect"
        case .search:
            return "/search/character"
        case .searchKeyword:
            return "/search/post/only-pass"
        case .searchWords:
            return "/character/searchWords"
        case .userCollect:
            return "/character/userCollect"
        }
    }
    public var method: Moya.Method {
        switch self {
        case .tags, .posts, .list, .detail, .userList, .search, .searchWords, .searchKeyword:
            return .get
        case .create, .edit, .collect:
            return .post
        case .userCollect:
            return .get
        }
    }
    public var task: Task {
        switch self {
        case .tags, .posts, .detail:
            return .requestPlain
        case .list(let params), .userList(let params), .search(let params), 
        .searchKeyword(let params):
            return .requestParameters(parameters: params, encoding: URLEncoding.default)
        case .create(let params), .edit(let params):
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        case .collect(let cid, let isCollect):
            return .requestParameters(parameters: ["cid": cid, "isCollect": isCollect], encoding: URLEncoding.default)
        case .searchWords(let num):
            return .requestParameters(parameters: ["num": num], encoding: URLEncoding.default)
        case .userCollect(let params):
            return .requestParameters(parameters: params, encoding: URLEncoding.default)
        }
    }
    public var headers: [String: String]? {
        return ["Content-Type": "application/json"]
    }
    public var sampleData: Data {
        return Data()
    }
} 
