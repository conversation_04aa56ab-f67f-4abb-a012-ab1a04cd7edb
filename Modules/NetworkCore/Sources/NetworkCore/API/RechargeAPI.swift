import Foundation
import Moya

/// 充值相关API
public enum RechargeAPI {
    /// 企业端充值
    case userPay(params: [String: Any])
    /// 管理端充值
    case adminPay(params: [String: Any])
    /// 检查订单状态
    case checkOrderStatus(orderNo: String)
    /// 查询充值记录(admin)
    case adminRecordList(params: [String: Any])
    /// 查询充值记录(merch)
    case userRecordList(params: [String: Any])
}


extension RechargeAPI: TargetType {
    public var baseURL: URL {
        return URL(string: BassUrlAddressConfig.baseURL)!
    }
    public var path: String {
        switch self {
        case .userPay:
            return "/userRechargeOrder/user/pay"
        case .adminPay:
            return "/userRechargeOrder/admin/pay"
        case .checkOrderStatus:
            return "/userRechargeOrder/user/checkOrderStatus"
        case .adminRecordList, .userRecordList:
            return "/userRechargeOrder/list"
        }
    }
    public var method: Moya.Method {
        switch self {
        case .userPay, .adminPay, .checkOrderStatus:
            return .post
        default:
            return .get
        }
    }
    public var task: Task {
        switch self {
        case .userPay(let params),
             .adminPay(let params),
             .adminRecordList(let params),
             .userRecordList(let params):
            return .requestParameters(parameters: params, encoding: URLEncoding.default)
        case .checkOrderStatus(let orderNo):
            return .requestParameters(parameters: ["orderNo": orderNo], encoding: URLEncoding.default)
        }
    }
    public var headers: [String: String]? {
        return ["Content-Type": "application/json"]
    }
    public var sampleData: Data {
        return Data()
    }
} 
