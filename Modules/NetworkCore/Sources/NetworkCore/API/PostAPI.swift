import Foundation
import Moya

/// 帖子相关API
public enum PostAPI {
    /// 获取帖子列表
    case list(params: [String: Any])
    /// 获取帖子详情
    case detail(pid: Int)
    /// 搜索帖子
    case search(params: [String: Any])
    /// 发布帖子
    case publish(params: [String: Any])
}


extension PostAPI: TargetType {
    public var baseURL: URL {
        return URL(string: BassUrlAddressConfig.baseURL)!
    }
    public var path: String {
        switch self {
        case .list:
            return "/post/list"
        case .detail(let pid):
            return "/post/\(pid)"
        case .search:
            return "/search/post/only-pass" // 修改为新路径
        case .publish:
            return "/post/user/publish"
        }
    }
    public var method: Moya.Method {
        switch self {
        case .publish:
            return .post
        default:
            return .get
        }
    }
    public var task: Task {
        switch self {
        case .list(let params), .search(let params):
            return .requestParameters(parameters: params, encoding: URLEncoding.default)
        case .detail:
            return .requestPlain
        case .publish(let params):
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        }
    }
    public var headers: [String: String]? {
        return ["Content-Type": "application/json"]
    }
    public var sampleData: Data {
        return Data()
    }
} 
