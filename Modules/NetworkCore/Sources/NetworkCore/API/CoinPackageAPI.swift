import Foundation
import Moya

public enum CoinPackageAPI {
    case list(params: [String: Any])
    case add(params: [String: Any])
    case update(params: [String: Any])
    case delete(ids: [Int])
}


extension CoinPackageAPI: TargetType {
    public var baseURL: URL {
        return URL(string: BassUrlAddressConfig.baseURL)!
    }
    public var path: String {
        switch self {
        case .list:
            return "/coinPackage/list"
        case .add:
            return "/coinPackage/add"
        case .update:
            return "/coinPackage/update"
        case .delete:
            return "/coinPackage/delete"
        }
    }
    public var method: Moya.Method {
        switch self {
        case .list:
            return .get
        case .add, .update:
            return .post
        case .delete:
            return .delete
        }
    }
    public var task: Task {
        switch self {
        case .list(let params):
            return .requestParameters(parameters: params, encoding: URLEncoding.default)
        case .add(let params), .update(let params):
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        case .delete(let ids):
            var params: [String: Any] = [:]
            for id in ids { params["coinPackageId"] = id }
            return .requestParameters(parameters: params, encoding: URLEncoding.default)
        }
    }
    public var headers: [String: String]? {
        return ["Content-Type": "application/json"]
    }
    public var sampleData: Data {
        return Data()
    }
} 
