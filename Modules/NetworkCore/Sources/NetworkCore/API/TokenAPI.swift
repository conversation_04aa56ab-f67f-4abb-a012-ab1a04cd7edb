import Foundation
import Moya

public enum TokenAPI {
    case getToken(username: String, password: String)
}


extension TokenAPI: TargetType {
    public var baseURL: URL {
        return URL(string: BassUrlAddressConfig.baseURL)!
    }
    public var path: String {
        switch self {
        case .getToken:
            return "/auth/token"
        }
    }
    public var method: Moya.Method {
        switch self {
        case .getToken:
            return .post
        }
    }
    public var task: Task {
        switch self {
        case let .getToken(username, password):
            let params = ["username": username, "password": password]
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        }
    }
    public var headers: [String: String]? {
        return ["Content-Type": "application/json"]
    }
    public var sampleData: Data {
        // 用于单元测试/Mock
        return Data()
    }
} 
