import Foundation
import Moya

public enum PayChannelAPI {
    case list(params: [String: Any])
    case add(params: [String: Any])
    case update(params: [String: Any])
    case delete(ids: [Int])
    case paymentMethod
}


extension PayChannelAPI: TargetType {
    public var baseURL: URL {
        return URL(string: BassUrlAddressConfig.baseURL)!
    }
    public var path: String {
        switch self {
        case .list:
            return "/payChannel/list"
        case .add:
            return "/payChannel/add"
        case .update:
            return "/payChannel/update"
        case .delete:
            return "/payChannel/delete"
        case .paymentMethod:
            return "/payChannel/paymentMethod"
        }
    }
    public var method: Moya.Method {
        switch self {
        case .list, .paymentMethod:
            return .get
        case .add, .update:
            return .post
        case .delete:
            return .delete
        }
    }
    public var task: Task {
        switch self {
        case .list(let params):
            return .requestParameters(parameters: params, encoding: URLEncoding.default)
        case .add(let params), .update(let params):
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        case .delete(let ids):
            var params: [String: Any] = [:]
            for id in ids { params["payChannelId"] = id }
            return .requestParameters(parameters: params, encoding: URLEncoding.default)
        case .paymentMethod:
            return .requestPlain
        }
    }
    public var headers: [String: String]? {
        return ["Content-Type": "application/json"]
    }
    public var sampleData: Data {
        return Data()
    }
} 
