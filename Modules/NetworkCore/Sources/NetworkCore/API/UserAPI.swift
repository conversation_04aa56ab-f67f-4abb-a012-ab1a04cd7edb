import Foundation
import Moya

public enum UserAPI {
    case getUserProfile(userId: String)
    case getUserInfo
    case login(request: UserLoginRequest)
    case logout
    case updatePassword(pw: String, npw: String)
    // 新增邮箱验证码登录/注册
    case sendVerificationCode(email: String)
    case verifyCode(email: String, code: String)
    case googleCallback(params: [String: Any])
}


extension UserAPI: TargetType {
    public var baseURL: URL {
        return URL(string: BassUrlAddressConfig.baseURL)!
    }
    public var path: String {
        switch self {
        case .getUserProfile(let userId):
            return "/user/profile/\(userId)"
        case .getUserInfo:
            return "/user/info"
        case .login:
            return "/user/account/login"
        case .logout:
            return "/user/account/logout"
        case .updatePassword:
            return "/user/password/update"
        case .sendVerificationCode:
            return "/user/account/sendVerificationCode"
        case .verifyCode:
            return "/user/login/verification"
        case .googleCallback:
            return "/user/oauth/google/callback"
        }
    }
    public var method: Moya.Method {
        switch self {
        case .getUserProfile, .getUserInfo:
            return .get
        case .login, .sendVerificationCode, .verifyCode:
            return .post
        case .logout:
            return .get
        case .updatePassword:
            return .post
        case .googleCallback:
            return .post
        }
    }
    public var task: Task {
        switch self {
        case .getUserProfile, .getUserInfo:
            return .requestPlain
        case .login(let request):
            return .requestJSONEncodable(request)
        case .logout:
            return .requestPlain
        case .updatePassword(let pw, let npw):
            return .requestParameters(parameters: ["pw": pw, "npw": npw], encoding: URLEncoding.default)
        case .sendVerificationCode(let email):
            return .requestJSONEncodable(["email": email])
        case .verifyCode(let email, let code):
            return .requestJSONEncodable(["email": email, "verificationCode": code])
        case .googleCallback(let params):
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        }
    }
    public var headers: [String: String]? {
        return ["Content-Type": "application/json"]
    }
    public var sampleData: Data {
        // 用于单元测试/Mock
        return Data()
    }
} 
