import Foundation
import Moya

/// 文件上传相关API
public enum UploadAPI {
    /// 上传文件
    case upload(file: Data, fileName: String)
}

extension UploadAPI: TargetType {
    public var baseURL: URL {
        // TODO: 替换为你的服务器地址
        return URL(string: "https://your.api.base.url")!
    }

    public var path: String {
        switch self {
        case .upload:
            return "/common/user/upload"
        }
    }

    public var method: Moya.Method {
        switch self {
        case .upload:
            return .post
        }
    }

    public var sampleData: Data {
        return Data()
    }

    public var task: Task {
        switch self {
        case .upload(let file, let fileName):
            let formData = MultipartFormData(provider: .data(file), name: "file", fileName: fileName, mimeType: "application/octet-stream")
            return .uploadMultipart([formData])
        }
    }

    public var headers: [String: String]? {
        return ["Content-Type": "multipart/form-data"]
    }
} 