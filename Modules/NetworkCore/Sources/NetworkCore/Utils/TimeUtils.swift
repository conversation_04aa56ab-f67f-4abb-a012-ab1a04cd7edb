import Foundation

public struct TimeUtils {
    /// 获取当前时间戳（秒）
    public static func currentTimestamp() -> Int {
        return Int(Date().timeIntervalSince1970)
    }

    /// 时间戳转字符串
    public static func timestampToString(_ timestamp: Int, format: String = "yyyy-MM-dd HH:mm:ss") -> String {
        let date = Date(timeIntervalSince1970: TimeInterval(timestamp))
        let formatter = DateFormatter()
        formatter.dateFormat = format
        return formatter.string(from: date)
    }

    /// 字符串转时间戳
    public static func stringToTimestamp(_ string: String, format: String = "yyyy-MM-dd HH:mm:ss") -> Int? {
        let formatter = DateFormatter()
        formatter.dateFormat = format
        if let date = formatter.date(from: string) {
            return Int(date.timeIntervalSince1970)
        }
        return nil
    }
} 