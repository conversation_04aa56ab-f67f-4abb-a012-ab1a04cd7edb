# NetworkCore

NetworkCore 是一个高可维护性、易扩展、支持多环境切换的 Swift 网络库，参考了现代 Android/Kotlin 网络架构，基于 Moya 封装，适用于中大型 iOS 项目。

## 目录结构
```
Sources/NetworkCore/
├── API/           # TargetType 定义（如 TokenAPI、UserAPI）
├── Request/       # 业务请求封装（如 TokenRequest、UserRequest）
├── Model/         # 数据模型（如 BaseResponse、TokenModel、UserModel）
├── Interceptor/   # 各类拦截器（日志、加密、Mock、性能等）
├── URLConfig/     # 多环境配置（如 BassUrlAddressConfig）
├── Utils/         # 工具类（加解密、时间、网络监控等）
├── HttpClient.swift         # 主网络客户端
├── NetWorkConfig.swift      # 全局配置
├── NetWorkError.swift       # 错误定义
├── RequestCallback.swift    # 回调与结果
```

## 环境切换
- 通过 `BassUrlAddressConfig.curEnvironment` 或 `NetWorkConfig` 切换 test/pro 环境。
- API 层自动适配 baseURL，无需手动修改。

## 请求用法
### 1. async/await
```swift
let result = await TokenRequest.getToken(username: "user", password: "pass")
switch result {
case .success(let token):
    print(token.token)
case .failure(let error):
    print(error.msg)
}
```

### 2. 回调
```swift
UserRequest.getUserProfile(userId: "123") { result in
    switch result {
    case .success(let user):
        print(user.name)
    case .failure(let error):
        print(error.msg)
    }
}
```

## 用户注册接口
### 异步方式
```swift
let result = await UserRequest.register(
    username: "testuser",
    password: "password123",
    confirmedPassword: "password123"
)

switch result {
case .success(let response):
    if response.code == 200 {
        print("注册成功: \(response.message)")
        if let userData = response.data {
            print("用户ID: \(userData.uid), 昵称: \(userData.nickname)")
        }
    } else {
        print("注册失败: \(response.message)")
    }
case .failure(let error):
    print("网络请求失败: \(error.localizedDescription)")
}
```

### 错误处理
注册接口会返回以下错误码：
- `200`: 注册成功
- `403`: 两次输入的密码不一致
- 其他错误码根据具体业务定义

## 用户登录接口
### 异步方式
```swift
let result = await UserRequest.login(
    username: "testuser",
    password: "password123"
)

switch result {
case .success(let response):
    if response.code == 200 {
        print("登录成功: \(response.message)")
        if let loginData = response.data {
            print("Token: \(loginData.token)")
            print("用户ID: \(loginData.user.uid)")
            print("昵称: \(loginData.user.nickname)")
            print("头像: \(loginData.user.avatarUrl)")
            print("金币: \(loginData.user.coin)")
            print("VIP等级: \(loginData.user.vip)")
        }
    } else {
        print("登录失败: \(response.message)")
    }
case .failure(let error):
    print("网络请求失败: \(error.localizedDescription)")
}
```

### 错误处理
登录接口会返回以下错误码：
- `200`: 登录成功
- `401`: 用户名或密码错误
- 其他错误码根据具体业务定义

## 拦截器
- 支持日志、Header、加密解密、错误码、重试、Mock、性能监控、网络状态等拦截器
- 可在 HttpClient 初始化时统一注册

## 工具类
- CryptoUtils：加解密工具
- TimeUtils：时间工具
- NetworkMonitor：网络状态监控

## 数据模型
- BaseResponse<T>：通用响应结构
- TokenModel、UserModel 等业务模型
- UserRegisterRequest：用户注册请求模型
- UserRegisterResponse：用户注册响应模型
- UserLoginRequest：用户登录请求模型
- UserLoginResponse：用户登录响应模型
- UserLoginData：用户登录数据模型

## 扩展建议
- 可按需扩展更多 API、业务模型、拦截器、工具类
- 支持 WebSocket、文件上传下载等高级功能

---

如需更多示例或集成说明，请查阅源码或联系维护者。 