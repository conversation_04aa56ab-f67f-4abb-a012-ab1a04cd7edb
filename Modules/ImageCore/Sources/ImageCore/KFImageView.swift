// The Swift Programming Language
// https://docs.swift.org/swift-book

import SwiftUI
import Kingfisher

/**
 KFImageView
 =====================
 SwiftUI 组件，便捷加载网络图片，自动缓存，支持占位图和失败图。
 封装 Kingfisher，适用于 SwiftUI 场景。
 
 - 参数：
    - url: 图片的网络地址（URL）
    - placeholder: 加载中显示的占位图（可选）
    - errorImage: 加载失败时显示的图片（可选）
    - options: Kingfisher 的高级配置参数（可选）
    - processors: 图片处理器数组（如圆角、模糊等，默认无）
    - enableFadeIn: 是否启用淡入动画（默认 true）
    - onProgress: 下载进度回调 (received, total)

 - 用法示例：
 ```swift
 KFImageView(
     url: URL(string: "https://example.com/image.png"),
     placeholder: Image(systemName: "photo"),
     errorImage: Image(systemName: "exclamationmark.triangle"),
     processors: [RoundCornerImageProcessor(cornerRadius: 20)],
     enableFadeIn: true,
     onProgress: { received, total in
         print("下载进度: \(received)/\(total)")
     }
 )
 .frame(width: 100, height: 100)
 ```
*/
@available(iOS 13.0, *)
public struct KFImageView: View {
    private let url: URL?
    private let placeholder: Image?
    private let errorImage: Image?
    private let options: KingfisherOptionsInfo?
    private let processors: [ImageProcessor]
    private let enableFadeIn: Bool
    private let onProgress: ((Int64, Int64) -> Void)?

    @State private var didFail: Bool = false
    @State private var isLoaded: Bool = false
    @State private var progress: (Int64, Int64) = (0, 0)

    public init(url: URL?,
                placeholder: Image? = nil,
                errorImage: Image? = nil,
                options: KingfisherOptionsInfo? = nil,
                processors: [ImageProcessor] = [],
                enableFadeIn: Bool = true,
                onProgress: ((Int64, Int64) -> Void)? = nil) {
        self.url = url
        self.placeholder = placeholder
        self.errorImage = errorImage
        self.options = options
        self.processors = processors
        self.enableFadeIn = enableFadeIn
        self.onProgress = onProgress
    }

    @ViewBuilder
    public var body: some View {
        if didFail, let errorImage = errorImage {
            errorImage
                .resizable()
                .aspectRatio(contentMode: .fit)
        } else {
            KFImage(url)
                .setProcessor(processors.first ?? DefaultImageProcessor())
                .loadDiskFileSynchronously()
                .cacheMemoryOnly()
                .fade(duration: enableFadeIn ? 0.3 : 0)
                .placeholder {
                    if let placeholder = placeholder {
                        placeholder
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    } else {
                        ProgressView()
                    }
                }
                .onFailure { _ in
                    Task { @MainActor in
                        didFail = true
                    }
                }
                .onSuccess { _ in
                    Task { @MainActor in
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isLoaded = true
                        }
                    }
                }
                .onProgress { received, total in
                    Task { @MainActor in
                        progress = (received, total)
                        onProgress?(received, total)
                    }
                }
                .resizable()
                .aspectRatio(contentMode: .fit)
                .opacity(enableFadeIn ? (isLoaded ? 1.0 : 0.0) : 1.0)
        }
    }

    private func buildOptions() -> KingfisherOptionsInfo? {
        guard !processors.isEmpty else { return options }
        var opts = options ?? []
        opts.append(.processor(processors.first ?? DefaultImageProcessor()))
        return opts
    }
}

/**
 ImageCacheManager
 =====================
 图片缓存管理工具，封装 Kingfisher 缓存相关常用操作。

 - 功能：
    - 清除所有图片缓存
    - 获取当前磁盘缓存大小
    - 按 URL 删除缓存

 - 用法示例：
 ```swift
 // 清除缓存
 ImageCacheManager.clearCache()

 // 获取缓存大小
 ImageCacheManager.cacheSize { size in
     print("缓存大小: \(size) 字节")
 }

 // 删除指定图片缓存
 ImageCacheManager.removeCache(for: url)
 ```
*/
public enum ImageCacheManager {
    /// 清除所有图片缓存
    public static func clearCache(completion: (() -> Void)? = nil) {
        let cache = KingfisherManager.shared.cache
        cache.clearMemoryCache()
        cache.clearDiskCache {
            completion?()
        }
    }

    /// 获取当前磁盘缓存大小（字节）
    public static func cacheSize(completion: @escaping (UInt) -> Void) {
        let cache = KingfisherManager.shared.cache
        cache.calculateDiskStorageSize { result in
            switch result {
            case .success(let size):
                completion(size)
            case .failure(_):
                completion(0)
            }
        }
    }

    /// 按 URL 删除缓存
    public static func removeCache(for url: URL, completion: (() -> Void)? = nil) {
        let cache = KingfisherManager.shared.cache
        let key = url.absoluteString
        cache.removeImage(forKey: key) {
            completion?()
        }
    }
}

/**
 ImagePreloader
 =====================
 图片预加载工具，支持批量预加载图片到缓存。

 - 用法示例：
 ```swift
 ImagePreloader.preload(urls: [URL(string: "https://example.com/image1.png")!])
 ```
*/
public enum ImagePreloader {
    /// 预加载图片到缓存
    public static func preload(urls: [URL], completion: (() -> Void)? = nil) {
        let prefetcher = ImagePrefetcher(urls: urls) { _, _, _ in
            completion?()
        }
        prefetcher.start()
    }
}
