// swift-tools-version: 6.1
import PackageDescription

let package = Package(
    name: "PermissionCore",
    platforms: [
        .iOS(.v16),
        .macOS(.v12),
        .tvOS(.v16),
        .watchOS(.v8)
    ],
    products: [
        .library(
            name: "PermissionCore",
            targets: ["PermissionCore"]
        ),
    ],
    dependencies: [
        .package(url: "https://github.com/sparrowcode/PermissionsKit", .upToNextMajor(from: "11.1.0"))
    ],
    targets: [
        .target(
            name: "PermissionCore",
            dependencies: [
                .product(name: "CameraPermission", package: "PermissionsKit"),
                .product(name: "PhotoLibraryPermission", package: "PermissionsKit"),
                .product(name: "NotificationPermission", package: "PermissionsKit"),
                .product(name: "LocationPermission", package: "PermissionsKit")
            ]
        ),
        .testTarget(
            name: "PermissionCoreTests",
            dependencies: ["PermissionCore"]
        ),
    ]
)
