// The Swift Programming Language
// https://docs.swift.org/swift-book

import Foundation
import PermissionsKit
import CameraPermission
import PhotoLibraryPermission
import NotificationPermission
import LocationPermission
import UIKit

public enum AppPermission: CaseIterable {
    case camera
    case photoLibrary
    case notification
    case locationWhenInUse
    case locationAlways
}

public enum PermissionStatus {
    case authorized
    case denied
    case notDetermined
}

public struct PermissionCore {
    public static func check(_ permission: AppPermission, completion: @escaping (PermissionStatus) -> Void) {
        switch permission {
        case .camera:
            completion(mapKitStatus(Permission.camera.status))
        case .photoLibrary:
            completion(mapKitStatus(Permission.photoLibrary.status))
        case .notification:
            completion(mapKitStatus(Permission.notification([.alert, .badge, .sound]).status))
        case .locationWhenInUse:
            completion(mapKitStatus(Permission.location(access: .whenInUse).status))
        case .locationAlways:
            completion(mapKitStatus(Permission.location(access: .always).status))
        }
    }

    public static func request(_ permission: AppPermission, completion: @escaping (Bool) -> Void) {
        switch permission {
        case .camera:
            Permission.camera.request { completion(Permission.camera.authorized) }
        case .photoLibrary:
            Permission.photoLibrary.request { completion(Permission.photoLibrary.authorized) }
        case .notification:
            Permission.notification([.alert, .badge, .sound]).request {
                completion(Permission.notification([.alert, .badge, .sound]).authorized)
            }
        case .locationWhenInUse:
            Permission.location(access: .whenInUse).request {
                completion(Permission.location(access: .whenInUse).authorized)
            }
        case .locationAlways:
            Permission.location(access: .always).request {
                completion(Permission.location(access: .always).authorized)
            }
        }
    }

    public static func checkAndRequestAllPermissions(completion: @escaping ([AppPermission: PermissionStatus]) -> Void) {
        var results: [AppPermission: PermissionStatus] = [:]
        let group = DispatchGroup()
        for permission in AppPermission.allCases {
            group.enter()
            check(permission) { status in
                if status == .notDetermined {
                    request(permission) { _ in
                        check(permission) { newStatus in
                            results[permission] = newStatus
                            group.leave()
                        }
                    }
                } else {
                    results[permission] = status
                    group.leave()
                }
            }
        }
        group.notify(queue: .main) {
            let denied = results.filter { $0.value == .denied }
            if !denied.isEmpty {
                Task { await openSettings() }
            }
            completion(results)
        }
    }

    @MainActor
    public static func openSettings() {
        guard let url = URL(string: UIApplication.openSettingsURLString),
              UIApplication.shared.canOpenURL(url) else { return }
        UIApplication.shared.open(url)
    }

    private static func mapKitStatus(_ status: Permission.Status) -> PermissionStatus {
        switch status {
        case .authorized:
            return .authorized
        case .denied:
            return .denied
        case .notDetermined:
            return .notDetermined
        @unknown default:
            return .denied
        }
    }
}
