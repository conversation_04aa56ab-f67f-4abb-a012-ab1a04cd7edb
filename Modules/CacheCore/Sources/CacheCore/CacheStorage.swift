import Foundation
import Cache
import LogCore

/// 基于 Cache 三方库的磁盘缓存封装，支持 Codable 类型

public class CacheStorage: @unchecked Sendable {
    private let storage: DiskStorage<String, Data>

    /// 初始化磁盘缓存，支持自定义命名空间和过期时间（秒），默认一周
    public init(namespace: String, expirySeconds: Int = 60 * 60 * 24 * 7) {
        let diskConfig = DiskConfig(name: namespace, expiry: .seconds(TimeInterval(expirySeconds)))
        self.storage = try! DiskStorage<String, Data>(config: diskConfig, transformer: TransformerFactory.forData())
    }

    /// 存储 Codable 对象，成功返回 true
    public func set<T: Codable>(_ key: String, value: T) -> Bool {
        do {
            let data = try JSONEncoder().encode(value)
            try storage.setObject(data, forKey: key)
            return true
        } catch {
            XLog.i("[CacheStorage][set] Failed to encode/set value for key: \(key), error: \(error)")
            return false
        }
    }

    /// 存储 Codable 对象，支持自定义过期策略，成功返回 true
    public func set<T: Codable>(_ key: String, value: T, expiry: Expiry) -> Bool {
        do {
            let data = try JSONEncoder().encode(value)
            try storage.setObject(data, forKey: key, expiry: expiry)
            return true
        } catch {
            XLog.i("[CacheStorage][set] Failed to encode/set value for key: \(key), error: \(error)")
            return false
        }
    }

    /// 读取 Codable 对象，失败返回 defaultValue
    public func get<T: Codable>(_ key: String, defaultValue: T? = nil) -> T? {
        do {
            let data = try storage.object(forKey: key)
            let value = try JSONDecoder().decode(T.self, from: data)
            return value
        } catch {
            XLog.i("[CacheStorage][get] Failed to get/decode value for key: \(key), error: \(error)")
            return defaultValue
        }
    }

    /// 移除指定 key
    public func remove(_ key: String) {
        do {
            try storage.removeObject(forKey: key)
        } catch {
            XLog.i("[CacheStorage][remove] Failed to remove key: \(key), error: \(error)")
        }
    }

    /// 清空所有缓存
    public func removeAll() {
        do {
            try storage.removeAll()
        } catch {
            XLog.i("[CacheStorage][removeAll] Failed to remove all, error: \(error)")
        }
    }

    /// 异步存储 Codable 对象，成功返回 true
    @preconcurrency
    @available(macOS 10.15, iOS 13.0, *)
    public func set<T: Codable & Sendable>(_ key: String, value: T) async -> Bool {
        await withCheckedContinuation { [self] continuation in
            continuation.resume(returning: self.set(key, value: value))
        }
    }

    /// 异步存储 Codable 对象，支持自定义过期策略，成功返回 true
    @preconcurrency
    @available(macOS 10.15, iOS 13.0, *)
    public func set<T: Codable & Sendable>(_ key: String, value: T, expiry: Expiry) async -> Bool {
        await withCheckedContinuation { [self] continuation in
            continuation.resume(returning: self.set(key, value: value, expiry: expiry))
        }
    }

    /// 异步读取 Codable 对象，失败返回 defaultValue
    @preconcurrency
    @available(macOS 10.15, iOS 13.0, *)
    public func get<T: Codable & Sendable>(_ key: String, defaultValue: T? = nil) async -> T? {
        await withCheckedContinuation { [self] continuation in
            continuation.resume(returning: self.get(key, defaultValue: defaultValue))
        }
    }

    /// 异步移除指定 key
    @preconcurrency
    @available(macOS 10.15, iOS 13.0, *)
    public func remove(_ key: String) async {
        await withCheckedContinuation { [self] continuation in
            self.remove(key)
            continuation.resume()
        }
    }

    /// 异步清空所有缓存
    @preconcurrency
    @available(macOS 10.15, iOS 13.0, *)
    public func removeAll() async {
        await withCheckedContinuation { [self] continuation in
            self.removeAll()
            continuation.resume()
        }
    }

    /// 缓存策略枚举
    public enum CachePolicy {
        case cacheOnly           // 只读缓存
        case networkOnly         // 只请求网络并写入缓存
        case cacheThenNetwork    // 先查缓存，未命中再请求网络
        case networkThenCache    // 先请求网络，失败再查缓存
        case refresh             // 强制刷新缓存（忽略缓存，始终请求网络并覆盖缓存）
    }

    /// 根据缓存策略自动处理缓存/网络读取逻辑
    /// - Parameters:
    ///   - key: 缓存 key
    ///   - policy: 缓存策略，默认 cacheThenNetwork
    ///   - expiry: 可选过期策略，写入缓存时生效，默认 nil
    ///   - fetcher: 网络请求闭包（async）
    /// - Returns: 业务模型或 nil
    public func fetch<T: Codable>(key: String, policy: CachePolicy = .cacheThenNetwork, expiry: Expiry? = nil, fetcher: () async -> T?) async -> T? {
        switch policy {
        case .cacheOnly:
            return await self.get(key)
        case .networkOnly:
            if let value = await fetcher() {
                if let expiry = expiry {
                    _ = await self.set(key, value: value, expiry: expiry)
                } else {
                    _ = await self.set(key, value: value)
                }
                return value
            }
            return nil
        case .cacheThenNetwork:
            if let cached: T = await self.get(key) {
                return cached
            }
            if let value = await fetcher() {
                if let expiry = expiry {
                    _ = await self.set(key, value: value, expiry: expiry)
                } else {
                    _ = await self.set(key, value: value)
                }
                return value
            }
            return nil
        case .networkThenCache:
            if let value = await fetcher() {
                if let expiry = expiry {
                    _ = await self.set(key, value: value, expiry: expiry)
                } else {
                    _ = await self.set(key, value: value)
                }
                return value
            }
            // 网络失败再查缓存
            return await self.get(key)
        case .refresh:
            if let value = await fetcher() {
                if let expiry = expiry {
                    _ = await self.set(key, value: value, expiry: expiry)
                } else {
                    _ = await self.set(key, value: value)
                }
                return value
            }
            return nil
        }
    }
}

/// 多命名空间缓存工厂/管理器

public class CacheStorageManager {
    private var storages: [String: CacheStorage] = [:]
    nonisolated(unsafe) public static let shared = CacheStorageManager()
    private init() {}

    /// 获取指定命名空间的缓存实例，已存在则复用，否则新建
    public func storage(for namespace: String, expirySeconds: Int = 60 * 60 * 24) -> CacheStorage {
        if let s = storages[namespace] { return s }
        let s = CacheStorage(namespace: namespace, expirySeconds: expirySeconds)
        storages[namespace] = s
        return s
    }

    /// 批量清理指定命名空间的缓存
    @preconcurrency
    @available(macOS 10.15, iOS 13.0, *)
    public func removeAll(for namespaces: [String]) async {
        let namespacesCopy = namespaces
        await withTaskGroup(of: Void.self) { group in
            for ns in namespacesCopy {
                if let storage = storages[ns] {
                    group.addTask { [storage] in await storage.removeAll() }
                }
            }
        }
    }

    /// 清理所有命名空间的缓存
    @preconcurrency
    @available(macOS 10.15, iOS 13.0, *)
    public func removeAll() async {
        let storagesCopy = Array(storages.values)
        await withTaskGroup(of: Void.self) { group in
            for storage in storagesCopy {
                group.addTask { [storage] in await storage.removeAll() }
            }
        }
    }
}

// MARK: - 监控信息结构体
public struct CacheStorageStats {
    public let namespace: String
    public let itemCount: Int
    public let totalSize: UInt64 // 字节数
    public let hitCount: Int
    public let missCount: Int
}

// MARK: - CacheStorage 监控扩展

extension CacheStorage {
    nonisolated(unsafe) private static var hitCounter: [String: Int] = [:]
    nonisolated(unsafe) private static var missCounter: [String: Int] = [:]

    public func stats() -> CacheStorageStats {
        let files = (try? storage.fileManager.contentsOfDirectory(atPath: storage.path)) ?? []
        let itemCount = files.count
        var totalSize: UInt64 = 0
        for file in files {
            let filePath = (storage.path as NSString).appendingPathComponent(file)
            if let attr = try? storage.fileManager.attributesOfItem(atPath: filePath), let size = attr[FileAttributeKey.size] as? UInt64 {
                totalSize += size
            }
        }
        let namespace = (storage.path as NSString).lastPathComponent
        let hit = CacheStorage.hitCounter[namespace] ?? 0
        let miss = CacheStorage.missCounter[namespace] ?? 0
        return CacheStorageStats(namespace: namespace, itemCount: itemCount, totalSize: totalSize, hitCount: hit, missCount: miss)
    }

    // 命中/未命中计数
    public func recordHit() {
        let ns = (storage.path as NSString).lastPathComponent
        CacheStorage.hitCounter[ns, default: 0] += 1
    }
    public func recordMiss() {
        let ns = (storage.path as NSString).lastPathComponent
        CacheStorage.missCounter[ns, default: 0] += 1
    }

    // 在 get 方法中自动计数
    public func getWithStats<T: Codable>(_ key: String, defaultValue: T? = nil) -> T? {
        do {
            let data = try storage.object(forKey: key)
            let value = try JSONDecoder().decode(T.self, from: data)
            self.recordHit()
            return value
        } catch {
            self.recordMiss()
            XLog.i("[CacheStorage][get] Failed to get/decode value for key: \(key), error: \(error)")
            return defaultValue
        }
    }
}

// MARK: - CacheStorageManager 监控扩展

extension CacheStorageManager {
    /// 获取所有命名空间的监控信息
    public func allStats() -> [CacheStorageStats] {
        return storages.values.map { $0.stats() }
    }
    /// 获取单个命名空间的监控信息
    public func stats(for namespace: String) -> CacheStorageStats? {
        return storages[namespace]?.stats()
    }
}

/*
CacheStorage/CacheStorageManager 功能总览与用法示例
--------------------------------------------------

默认过期时间：一周（7天）
默认缓存策略：cacheThenNetwork

1. 命名空间管理（多业务隔离）
--------------------------------------------------
// 获取指定命名空间的缓存实例（推荐通过工厂管理器）
let userCache = CacheStorageManager.shared.storage(for: "user")
let postCache = CacheStorageManager.shared.storage(for: "post", expirySeconds: 600)

2. 同步/异步存储与读取
--------------------------------------------------
// 同步存储
userCache.set("user_profile_123", value: profile)
// 异步存储
await userCache.set("user_profile_123", value: profile)

// 同步读取
let cached: UserProfile? = userCache.get("user_profile_123")
// 异步读取
let cached: UserProfile? = await userCache.get("user_profile_123")

3. 支持自定义过期策略
--------------------------------------------------
import Cache
// 存储时自定义过期时间（如10分钟）
userCache.set("user_profile_123", value: profile, expiry: .seconds(600))
// 异步存储自定义过期
await userCache.set("user_profile_123", value: profile, expiry: .date(Date().addingTimeInterval(3600)))

4. 移除与批量清理
--------------------------------------------------
// 移除单个 key
userCache.remove("user_profile_123")
await userCache.remove("user_profile_123")
// 清空当前命名空间所有缓存
userCache.removeAll()
await userCache.removeAll()
// 批量清理指定命名空间
await CacheStorageManager.shared.removeAll(for: ["user", "post"])
// 清理所有命名空间
await CacheStorageManager.shared.removeAll()

5. 监控与统计
--------------------------------------------------
// 获取单个命名空间的监控信息
if let stats = CacheStorageManager.shared.stats(for: "user") {
    print("user缓存条数: \(stats.itemCount), 总大小: \(stats.totalSize) bytes, 命中: \(stats.hitCount), 未命中: \(stats.missCount)")
}
// 获取所有命名空间的监控信息
let allStats = CacheStorageManager.shared.allStats()
for stat in allStats {
    print("namespace: \(stat.namespace), items: \(stat.itemCount), size: \(stat.totalSize), hit: \(stat.hitCount), miss: \(stat.missCount)")
}
// 统计命中/未命中（getWithStats 方法自动计数）
let cached: UserProfile? = userCache.getWithStats("user_profile_123")

6. 缓存策略（CachePolicy）
--------------------------------------------------
// 定义网络请求闭包
func fetchFromNetwork() async -> UserProfile? { /* ... */ }
// cacheThenNetwork 策略优先返回缓存，缓存未命中再请求网络并写入缓存
let result = await userCache.fetch(key: "user_profile_123", policy: .cacheThenNetwork, expiry: .seconds(600), fetcher: fetchFromNetwork)
// networkOnly 策略始终请求网络并写入缓存
let result = await userCache.fetch(key: "user_profile_123", policy: .networkOnly, expiry: .seconds(600), fetcher: fetchFromNetwork)
// cacheOnly 只查缓存
let result = await userCache.fetch(key: "user_profile_123", policy: .cacheOnly, fetcher: fetchFromNetwork)
// refresh 强制刷新缓存
let result = await userCache.fetch(key: "user_profile_123", policy: .refresh, expiry: .seconds(600), fetcher: fetchFromNetwork)

--------------------------------------------------
支持自定义扩展：如缓存策略、批量操作、命名空间动态配置等。
*/ 
