// KeyValueStore.swift
// 键值对存储统一入口，支持命名空间分组
//
// 推荐调用方式：
//
// // User 命名空间
// KeyValueStore.User.set("token", value: "abc")
// let token: String? = KeyValueStore.User.get("token")
// KeyValueStore.User.remove("token")
// KeyValueStore.User.removeAll()
//
// // Setting 命名空间
// KeyValueStore.Setting.set("theme", value: "dark")
// let theme: String? = KeyValueStore.Setting.get("theme")
//
// // 通用接口（需先设置命名空间，异步调用）
// await KeyValueStore.setNamespace("custom")
// await KeyValueStore.set("key", value: 123)
// let value: Int? = await KeyValueStore.get("key")
// await KeyValueStore.removeAll()

import Foundation
import SwiftyUserDefaults
import LogCore

// MARK: - 键值对存储协议
/// 通用键值对存储协议，支持泛型、命名空间，便于扩展多种后端实现。
protocol KeyValueStoreProtocol {
    /// 存储值到指定 key，可选命名空间
    func set<T: Codable & Sendable>(_ key: String, value: T, namespace: String?) -> T?
    /// 读取指定 key 的值，可选命名空间
    func get<T: Codable & Sendable>(_ key: String, namespace: String?, defaultValue: T?) -> T?
    /// 移除指定 key，可选命名空间
    func remove(_ key: String, namespace: String?)
    /// 移除指定命名空间下所有 key
    func removeAll(namespace: String?)
}

// MARK: - UserDefaults 实现
/// 基于 SwiftyUserDefaults 的键值对存储实现，支持命名空间隔离。
final class UserDefaultsStore: KeyValueStoreProtocol {
    /// 拼接命名空间前缀，便于分组和批量清理
    private func namespacedKey(_ key: String, namespace: String?) -> String {
        guard let ns = namespace, !ns.isEmpty else { return key }
        return "\(ns)__\(key)"
    }
    /// 存储值，使用 JSON 编码，支持任意 Codable 类型
    func set<T: Codable & Sendable>(_ key: String, value: T, namespace: String?) -> T? {
        let nsKey = namespacedKey(key, namespace: namespace)
        do {
            let data = try JSONEncoder().encode(value)
            UserDefaults.standard.set(data, forKey: nsKey)
            return value
        } catch {
            XLog.i("[KeyValueStore][set] Failed to encode value for key: \(nsKey), error: \(error)")
            return nil
        }
    }
    /// 读取值，自动解码为指定类型
    func get<T: Codable & Sendable>(_ key: String, namespace: String?, defaultValue: T?) -> T? {
        let nsKey = namespacedKey(key, namespace: namespace)
        guard let data = UserDefaults.standard.data(forKey: nsKey) else {
            return defaultValue
        }
        do {
            let value = try JSONDecoder().decode(T.self, from: data)
            return value
        } catch {
            XLog.i("[KeyValueStore][get] Failed to decode value for key: \(nsKey), error: \(error)")
            return defaultValue
        }
    }
    /// 移除指定 key
    func remove(_ key: String, namespace: String?) {
        let nsKey = namespacedKey(key, namespace: namespace)
        UserDefaults.standard.removeObject(forKey: nsKey)
    }
    /// 批量移除命名空间下所有 key
    func removeAll(namespace: String?) {
        guard let ns = namespace, !ns.isEmpty else { return }
        for (key, _) in UserDefaults.standard.dictionaryRepresentation() {
            if key.hasPrefix("\(ns)__") {
                UserDefaults.standard.removeObject(forKey: key)
            }
        }
    }
}

// MARK: - 统一静态入口
/// KeyValueStore 提供统一静态接口进行键值对存储，支持命名空间分组，所有操作为并发安全 async。
///
/// ## 用法示例：
///
/// ```swift
/// // 设置命名空间（如 "user"、"setting"），后续操作均在该命名空间下
/// await KeyValueStore.setNamespace("user")
///
/// // 存储数据
/// await KeyValueStore.set("token", value: "abc123")
/// await KeyValueStore.set("userId", value: 12345)
///
/// // 读取数据
/// let token: String? = await KeyValueStore.get("token")
/// let userId: Int? = await KeyValueStore.get("userId")
///
/// // 移除单个 key
/// await KeyValueStore.remove("token")
///
/// // 清理当前命名空间下所有缓存
/// await KeyValueStore.removeAll()
/// ```

actor KeyValueStoreActor {
    private var _namespace: String? = nil
    private let store: KeyValueStoreProtocol = UserDefaultsStore()
    
    /// 设置当前命名空间，后续 set/get/remove 操作均在该空间下
    func setNamespace(_ namespace: String?) {
        _namespace = namespace
    }
    /// 获取当前命名空间
    var namespace: String? { _namespace }
    /// 存储值到当前命名空间
    func set<T: Codable & Sendable>(_ key: String, value: T) -> T? {
        store.set(key, value: value, namespace: _namespace)
    }
    /// 读取当前命名空间下的 key
    func getWithNamespace<T: Codable & Sendable>(_ key: String, namespace: String?, defaultValue: T?) -> T? {
        store.get(key, namespace: namespace, defaultValue: defaultValue)
    }
    /// 移除当前命名空间下的 key
    func remove(_ key: String) {
        store.remove(key, namespace: _namespace)
    }
    /// 清理当前命名空间下所有缓存
    func removeAll() {
        store.removeAll(namespace: _namespace)
    }
}


public enum KeyValueStore {
    private static let actor = KeyValueStoreActor()
    
    /// 设置命名空间（如 "user"、"setting"），后续操作均在该命名空间下
    public static func setNamespace(_ namespace: String?) async {
        await actor.setNamespace(namespace)
    }
    /// 获取当前命名空间
    public static var namespace: String? {
        get async { await actor.namespace }
    }
    /// 存储值到当前命名空间（异步，成功返回设置的值，失败返回 nil）
    public static func set<T: Codable & Sendable>(_ key: String, value: T) async -> T? {
        await actor.set(key, value: value)
    }
    /// 读取当前命名空间下的 key（异步，失败返回 defaultValue）
    public static func get<T: Codable & Sendable>(_ key: String, defaultValue: T? = nil) async -> T? {
        let ns = await actor.namespace
        return await actor.getWithNamespace(key, namespace: ns, defaultValue: defaultValue)
    }
    /// 移除当前命名空间下的 key
    public static func remove(_ key: String) async {
        await actor.remove(key)
    }
    /// 清理当前命名空间下所有缓存
    public static func removeAll() async {
        await actor.removeAll()
    }

    // MARK: - 专用命名空间：User
    /// User 命名空间，所有操作自动存储到 "user" 分组下
    public struct User {
        private static let namespace = "user"
        /// 存储值到 user 命名空间，成功返回设置的值，失败返回 nil
        public static func set<T: Codable & Sendable>(_ key: String, value: T) -> T? {
            let store = UserDefaultsStore()
            return store.set(key, value: value, namespace: namespace)
        }
        /// 读取 user 命名空间下的 key，失败返回 defaultValue
        public static func get<T: Codable & Sendable>(_ key: String, defaultValue: T? = nil) -> T? {
            let store = UserDefaultsStore()
            return store.get(key, namespace: namespace, defaultValue: defaultValue)
        }
        /// 移除 user 命名空间下的 key
        public static func remove(_ key: String) {
            let store = UserDefaultsStore()
            store.remove(key, namespace: namespace)
        }
        /// 清理 user 命名空间下所有缓存
        public static func removeAll() {
            let store = UserDefaultsStore()
            store.removeAll(namespace: namespace)
        }
    }

    // MARK: - 专用命名空间：Setting
    /// Setting 命名空间，所有操作自动存储到 "setting" 分组下
    public struct Setting {
        private static let namespace = "setting"
        /// 存储值到 setting 命名空间，成功返回设置的值，失败返回 nil
        public static func set<T: Codable & Sendable>(_ key: String, value: T) -> T? {
            let store = UserDefaultsStore()
            return store.set(key, value: value, namespace: namespace)
        }
        /// 读取 setting 命名空间下的 key，失败返回 defaultValue
        public static func get<T: Codable & Sendable>(_ key: String, defaultValue: T? = nil) -> T? {
            let store = UserDefaultsStore()
            return store.get(key, namespace: namespace, defaultValue: defaultValue)
        }
        /// 移除 setting 命名空间下的 key
        public static func remove(_ key: String) {
            let store = UserDefaultsStore()
            store.remove(key, namespace: namespace)
        }
        /// 清理 setting 命名空间下所有缓存
        public static func removeAll() {
            let store = UserDefaultsStore()
            store.removeAll(namespace: namespace)
        }
    }
}
