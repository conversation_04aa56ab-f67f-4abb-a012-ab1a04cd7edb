# CacheCore 本地存储推荐方案

本项目建议根据不同数据类型选择合适的本地存储方式，具体如下：

| 内容类型         | 推荐存储方式         | 说明                     |
|------------------|----------------------|--------------------------|
| 用户信息         | Realm                | 结构化、可扩展           |
| 聊天/帖子记录    | Realm                | 结构化、可查询           |
| 图片/音频缓存    | Cache                | 大对象、可过期           |
| 网络接口缓存     | Cache                | 临时数据、可过期         |
| 配置/偏好        | SwiftyUserDefaults   | 轻量、简单               |
| token/标志位     | SwiftyUserDefaults   | 轻量、简单               |

> 后续开发请参考以上建议选择合适的本地存储方案。 