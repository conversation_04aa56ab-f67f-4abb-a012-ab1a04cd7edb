// swift-tools-version: 6.1
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "CacheCore",
    platforms: [
        .iOS(.v16),
        .macOS(.v12),
        .tvOS(.v16),
        .watchOS(.v8)
    ],
    products: [
        // Products define the executables and libraries a package produces, making them visible to other packages.
        .library(
            name: "Cache<PERSON>ore",
            targets: ["CacheCore"]),
    ],
    dependencies: [
        .package(url: "https://github.com/realm/realm-swift.git", .upToNextMajor(from: "10.50.0")),
        .package(url: "https://github.com/hyperoslo/Cache.git", .upToNextMajor(from: "6.0.0")),
        .package(url: "https://github.com/sunshinejr/SwiftyUserDefaults.git", .upToNextMajor(from: "5.3.0")),
        .package(path: "../LogCore")
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        // Targets can depend on other targets in this package and products from dependencies.
        .target(
            name: "<PERSON>ache<PERSON>ore",
            dependencies: [
                .product(name: "RealmSwift", package: "realm-swift"),
                .product(name: "Cache", package: "Cache"),
                .product(name: "SwiftyUserDefaults", package: "SwiftyUserDefaults"),
                "LogCore"
            ]),
        .testTarget(
            name: "CacheCoreTests",
            dependencies: ["CacheCore"]
        ),
    ]
)
