import XCTest
import NetworkCore
import CacheCore
@testable import DataRepositoryCore

final class CharacterRepositoryTests: XCTestCase {
    func testTags_normal() async throws {
        print("【CharacterRepository】测试 tags 正常，期望返回标签列表")
        let repo = CharacterRepository()
        print("无输入参数")
        let tags = await repo.tags()
        print("实际返回:", tags as Any)
        XCTAssertNotNil(tags)
    }
    func testTags_networkOnly() async throws {
        print("【CharacterRepository】测试 tags networkOnly 策略，期望返回标签列表")
        let repo = CharacterRepository()
        print("无输入参数，policy: .networkOnly")
        let tags = await repo.tags(policy: .networkOnly)
        print("实际返回:", tags as Any)
        XCTAssertNotNil(tags)
    }
    func testList_normal() async throws {
        print("【CharacterRepository】测试 list 正常参数，期望返回角色列表")
        let repo = CharacterRepository()
        let params: [String: Any] = ["pageNum": "1", "pageSize": "10"]
        print("输入参数:", params)
        let list = await repo.list(params: params)
        print("实际返回:", list as Any)
        XCTAssertNotNil(list)
    }
    func testList_emptyParams() async throws {
        print("【CharacterRepository】测试 list 空参数，期望返回角色列表")
        let repo = CharacterRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let list = await repo.list(params: params)
        print("实际返回:", list as Any)
        XCTAssertNotNil(list)
    }
    func testDetail_normal() async throws {
        print("【CharacterRepository】测试 detail 正常 cid，期望返回详情")
        let repo = CharacterRepository()
        let cid = "1"
        print("输入参数: cid=", cid)
        let detail = await repo.detail(cid: cid)
        print("实际返回:", detail as Any)
        XCTAssertNotNil(detail)
    }
    func testDetail_emptyCid() async throws {
        print("【CharacterRepository】测试 detail 空 cid，期望返回 nil")
        let repo = CharacterRepository()
        let cid = ""
        print("输入参数: cid=", cid)
        let detail = await repo.detail(cid: cid)
        print("实际返回:", detail as Any)
        XCTAssertNil(detail)
    }
    func testPosts_normal() async throws {
        print("【CharacterRepository】测试 posts 正常 cid，期望返回帖子列表")
        let repo = CharacterRepository()
        let cid = "1"
        print("输入参数: cid=", cid)
        let posts = await repo.posts(cid: cid)
        print("实际返回:", posts as Any)
        XCTAssertNotNil(posts)
    }
    func testUserList_normal() async throws {
        print("【CharacterRepository】测试 userList 正常参数，期望返回用户角色列表")
        let repo = CharacterRepository()
        let params: [String: Any] = ["pageNum": "1", "pageSize": "10"]
        print("输入参数:", params)
        let list = await repo.userList(params: params)
        print("实际返回:", list as Any)
        XCTAssertNotNil(list)
    }
    func testSearch_normal() async throws {
        print("【CharacterRepository】测试 search 正常参数，期望返回搜索结果")
        let repo = CharacterRepository()
        let params: [String: Any] = ["keyword": "test", "page": "1"]
        print("输入参数:", params)
        let result = await repo.searchTag(params: params)
        print("实际返回:", result as Any)
        XCTAssertNotNil(result)
    }
    func testSearchWords_normal() async throws {
        print("【CharacterRepository】测试 searchWords 正常 num，期望返回搜索词")
        let repo = CharacterRepository()
        let num = 100
        print("输入参数: num=", num)
        let result = await repo.searchWords(num: num)
        print("实际返回:", result as Any)
        XCTAssertNotNil(result)
    }
    func testCreate_normal() async throws {
        print("【CharacterRepository】测试 create 正常参数，期望创建成功")
        let repo = CharacterRepository()
        let params: [String: Any] = ["nickName": "test"]
        print("输入参数:", params)
        let result = await repo.create(params: params)
        print("实际返回:", result as Any)
        XCTAssertNotNil(result)
    }
    func testEdit_normal() async throws {
        print("【CharacterRepository】测试 edit 正常参数，期望编辑成功")
        let repo = CharacterRepository()
        let params: [String: Any] = ["cid": "1"]
        print("输入参数:", params)
        let result = await repo.edit(params: params)
        print("实际返回:", result as Any)
        XCTAssertNotNil(result)
    }
    func testCollect_normal() async throws {
        print("【CharacterRepository】测试 collect 正常参数，期望收藏/取消收藏成功")
        let repo = CharacterRepository()
        let cid = "1"
        print("输入参数: cid=", cid, "isCollect=true")
        let result = await repo.collect(cid: cid, isCollect: true)
        print("实际返回:", result as Any)
        XCTAssertNotNil(result)
    }
    func testCollect_invalidCid() async throws {
        print("【CharacterRepository】测试 collect 空 cid，期望返回 nil")
        let repo = CharacterRepository()
        let cid = ""
        print("输入参数: cid=", cid, "isCollect=true")
        let result = await repo.collect(cid: cid, isCollect: true)
        print("实际返回:", result as Any)
        XCTAssertNil(result)
    }
} 
