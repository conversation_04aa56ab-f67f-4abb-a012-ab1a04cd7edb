import XCTest
import NetworkCore
import CacheCore
@testable import DataRepositoryCore

final class RechargeRepositoryTests: XCTestCase {
    func testUserPay_normal() async throws {
        print("【RechargeRepository】测试 userPay 正常参数，期望充值成功")
        let repo = RechargeRepository()
        let params: [String: Any] = ["coinPackageId": "1", "paymentMethod": "test"]
        print("输入参数:", params)
        let pay = await repo.userPay(params: params)
        print("实际返回:", pay as Any)
        XCTAssertNotNil(pay)
    }
    func testUserPay_emptyParams() async throws {
        print("【RechargeRepository】测试 userPay 空参数，期望返回 nil")
        let repo = RechargeRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let pay = await repo.userPay(params: params)
        print("实际返回:", pay as Any)
        XCTAssertNil(pay)
    }
    func testAdminPay_normal() async throws {
        print("【RechargeRepository】测试 adminPay 正常参数，期望充值成功")
        let repo = RechargeRepository()
        let params: [String: Any] = ["userId": "1", "paymentMethod": "test"]
        print("输入参数:", params)
        let pay = await repo.adminPay(params: params)
        print("实际返回:", pay as Any)
        XCTAssertNotNil(pay)
    }
    func testCheckOrderStatus_normal() async throws {
        print("【RechargeRepository】测试 checkOrderStatus 正常 orderNo，期望返回订单状态")
        let repo = RechargeRepository()
        let orderNo = "123456"
        print("输入参数: orderNo=", orderNo)
        let resp = await repo.checkOrderStatus(orderNo: orderNo)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testCheckOrderStatus_emptyOrderNo() async throws {
        print("【RechargeRepository】测试 checkOrderStatus 空 orderNo，期望返回 nil")
        let repo = RechargeRepository()
        let orderNo = ""
        print("输入参数: orderNo=", orderNo)
        let resp = await repo.checkOrderStatus(orderNo: orderNo)
        print("实际返回:", resp as Any)
        XCTAssertNil(resp)
    }
    func testAdminRecordList_normal() async throws {
        print("【RechargeRepository】测试 adminRecordList 正常参数，期望返回记录列表")
        let repo = RechargeRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let resp = await repo.adminRecordList(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testUserRecordList_normal() async throws {
        print("【RechargeRepository】测试 userRecordList 正常参数，期望返回记录列表")
        let repo = RechargeRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let resp = await repo.userRecordList(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
} 