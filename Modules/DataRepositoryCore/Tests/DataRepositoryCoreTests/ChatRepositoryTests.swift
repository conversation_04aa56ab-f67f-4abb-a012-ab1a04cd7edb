import XCTest
import NetworkCore
import CacheCore
@testable import DataRepositoryCore

final class ChatRepositoryTests: XCTestCase {
    func testGreet_normal() async throws {
        print("【ChatRepository】测试 greet 正常 cid，期望返回 greet 信息")
        let repo = ChatRepository()
        let cid = "1"
        print("输入参数: cid=", cid)
        let greet = await repo.greet(cid: cid)
        print("实际返回:", greet as Any)
        XCTAssertNotNil(greet)
    }
    func testGreet_emptyCid() async throws {
        print("【ChatRepository】测试 greet 空 cid，期望返回 nil")
        let repo = ChatRepository()
        let cid = ""
        print("输入参数: cid=", cid)
        let greet = await repo.greet(cid: cid)
        print("实际返回:", greet as Any)
        XCTAssertNil(greet)
    }
    func testSend_normal() async throws {
        print("【ChatRepository】测试 send 正常参数，期望发送成功")
        let repo = ChatRepository()
        let params: [String: Any] = ["cid": "1", "message": "hello"]
        print("输入参数:", params)
        let resp = await repo.send(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testSend_emptyParams() async throws {
        print("【ChatRepository】测试 send 空参数，期望返回 nil")
        let repo = ChatRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let resp = await repo.send(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNil(resp)
    }
    func testMessageList_normal() async throws {
        print("【ChatRepository】测试 messageList 正常参数，期望返回消息列表")
        let repo = ChatRepository()
        let params: [String: Any] = ["cid": "1", "anchorId": "0"]
        print("输入参数:", params)
        let resp = await repo.messageList(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testLastRecord_normal() async throws {
        print("【ChatRepository】测试 lastRecord 正常参数，期望返回最近聊天记录")
        let repo = ChatRepository()
        let params: [String: Any] = ["pageNum": "1", "pageSize": "10"]
        print("输入参数:", params)
        let resp = await repo.lastRecord(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testAudio_normal() async throws {
        print("【ChatRepository】测试 audio 正常 id，期望返回音频信息")
        let repo = ChatRepository()
        let id = "1"
        print("输入参数: id=", id)
        let resp = await repo.audio(id: id)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testAudio_emptyId() async throws {
        print("【ChatRepository】测试 audio 空 id，期望返回 nil")
        let repo = ChatRepository()
        let id = ""
        print("输入参数: id=", id)
        let resp = await repo.audio(id: id)
        print("实际返回:", resp as Any)
        XCTAssertNil(resp)
    }
} 