import XCTest
import NetworkCore
import CacheCore
@testable import DataRepositoryCore

final class CoinPackageRepositoryTests: XCTestCase {
    func testList_normal() async throws {
        print("【CoinPackageRepository】测试 list 正常参数，期望返回列表数据")
        let repo = CoinPackageRepository()
        let params: [String: Any] = ["page": 1, "size": 10]
        print("输入参数:", params)
        let list = await repo.list(params: params)
        print("实际返回:", list as Any)
        XCTAssertNotNil(list)
    }
    func testList_emptyParams() async throws {
        print("【CoinPackageRepository】测试 list 空参数，期望返回列表数据")
        let repo = CoinPackageRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let list = await repo.list(params: params)
        print("实际返回:", list as Any)
        XCTAssertNotNil(list)
    }
    func testList_networkOnly() async throws {
        print("【CoinPackageRepository】测试 list networkOnly 策略，期望返回网络数据")
        let repo = CoinPackageRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params, "policy: .networkOnly")
        let list = await repo.list(params: params, policy: .networkOnly)
        print("实际返回:", list as Any)
        XCTAssertNotNil(list)
    }
    func testAdd_normal() async throws {
        print("【CoinPackageRepository】测试 add 正常参数，期望新增成功")
        let repo = CoinPackageRepository()
        let params: [String: Any] = ["name": "test", "amount": 100]
        print("输入参数:", params)
        let resp = await repo.add(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testAdd_emptyParams() async throws {
        print("【CoinPackageRepository】测试 add 空参数，期望返回 nil")
        let repo = CoinPackageRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let resp = await repo.add(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNil(resp)
    }
    func testUpdate_normal() async throws {
        print("【CoinPackageRepository】测试 update 正常参数，期望修改成功")
        let repo = CoinPackageRepository()
        let params: [String: Any] = ["id": 1, "amount": 200]
        print("输入参数:", params)
        let resp = await repo.update(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testUpdate_emptyParams() async throws {
        print("【CoinPackageRepository】测试 update 空参数，期望返回 nil")
        let repo = CoinPackageRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let resp = await repo.update(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNil(resp)
    }
    func testDelete_normal() async throws {
        print("【CoinPackageRepository】测试 delete 正常参数，期望删除成功")
        let repo = CoinPackageRepository()
        let ids = [1,2]
        print("输入参数: ids=", ids)
        let resp = await repo.delete(ids: ids)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testDelete_emptyIds() async throws {
        print("【CoinPackageRepository】测试 delete 空数组，期望返回 nil")
        let repo = CoinPackageRepository()
        let ids: [Int] = []
        print("输入参数: ids=", ids)
        let resp = await repo.delete(ids: ids)
        print("实际返回:", resp as Any)
        XCTAssertNil(resp)
    }
} 