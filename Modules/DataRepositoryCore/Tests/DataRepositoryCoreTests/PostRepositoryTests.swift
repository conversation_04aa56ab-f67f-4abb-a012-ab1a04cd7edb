import XCTest
import NetworkCore
import CacheCore
@testable import DataRepositoryCore

final class PostRepositoryTests: XCTestCase {
    func testList_normal() async throws {
        print("【PostRepository】测试 list 正常参数，期望返回帖子列表")
        let repo = PostRepository()
        let params: [String: Any] = ["pageNum": "1", "pageSize": "10"]
        print("输入参数:", params)
        let list = await repo.list(params: params)
        print("实际返回:", list as Any)
        XCTAssertNotNil(list)
    }
    func testList_emptyParams() async throws {
        print("【PostRepository】测试 list 空参数，期望返回帖子列表")
        let repo = PostRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let list = await repo.list(params: params)
        print("实际返回:", list as Any)
        XCTAssertNotNil(list)
    }
    func testDetail_normal() async throws {
        print("【PostRepository】测试 detail 正常 pid，期望返回帖子详情")
        let repo = PostRepository()
        let pid = 1
        print("输入参数: pid=", pid)
        let detail = await repo.detail(pid: pid)
        print("实际返回:", detail as Any)
        XCTAssertNotNil(detail)
    }
    func testDetail_invalidPid() async throws {
        print("【PostRepository】测试 detail 非法 pid，期望返回 nil")
        let repo = PostRepository()
        let pid = -1
        print("输入参数: pid=", pid)
        let detail = await repo.detail(pid: pid)
        print("实际返回:", detail as Any)
        XCTAssertNil(detail)
    }
    func testSearch_normal() async throws {
        print("【PostRepository】测试 search 正常参数，期望返回搜索结果")
        let repo = PostRepository()
        let params: [String: Any] = ["keyword": "test", "page": "1"]
        print("输入参数:", params)
        let result = await repo.search(params: params)
        print("实际返回:", result as Any)
        XCTAssertNotNil(result)
    }
    func testPublish_normal() async throws {
        print("【PostRepository】测试 publish 正常参数，期望发布成功")
        let repo = PostRepository()
        let params: [String: Any] = ["cid": "1", "prompt": "test"]
        print("输入参数:", params)
        let result = await repo.publish(params: params)
        print("实际返回:", result as Any)
        XCTAssertNotNil(result)
    }
    func testPublish_emptyParams() async throws {
        print("【PostRepository】测试 publish 空参数，期望返回 nil")
        let repo = PostRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let result = await repo.publish(params: params)
        print("实际返回:", result as Any)
        XCTAssertNil(result)
    }

    // MARK: - 推荐卡片数据转换测试

    func testGetRecommendCardItems_normal() async throws {
        print("【PostRepository】测试 getRecommendCardItems 正常参数，期望返回推荐卡片列表")
        let repo = PostRepository()
        print("输入参数: pageNum=1, pageSize=10")
        let items = await repo.getRecommendCardItems(pageNum: 1, pageSize: 10)
        print("实际返回:", items as Any)
        XCTAssertNotNil(items)

        if let items = items {
            print("返回的卡片数量:", items.count)
            // 验证数据转换是否正确
            for item in items.prefix(3) { // 只检查前3个
                print("卡片ID:", item.id)
                print("标题:", item.title)
                print("封面URL:", item.coverURL)
                print("头像URL:", item.avatarURL)
                print("副标题:", item.subtitle)
                print("点赞数:", item.likeCount)
                print("是否点赞:", item.isLiked)
                print("---")
            }
        }
    }

    func testSearchRecommendCardItems_normal() async throws {
        print("【PostRepository】测试 searchRecommendCardItems 正常参数，期望返回搜索卡片列表")
        let repo = PostRepository()
        print("输入参数: pageNum=1, pageSize=10, keyword=test")
        let items = await repo.searchRecommendCardItems(pageNum: 1, pageSize: 10, keyword: "test")
        print("实际返回:", items as Any)
        XCTAssertNotNil(items)

        if let items = items {
            print("返回的搜索卡片数量:", items.count)
            // 验证搜索结果的数据转换是否正确
            for item in items.prefix(2) { // 只检查前2个
                print("搜索卡片ID:", item.id)
                print("搜索标题:", item.title)
                print("---")
            }
        }
    }
}
