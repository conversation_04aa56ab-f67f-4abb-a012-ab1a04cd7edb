import XCTest
import NetworkCore
import CacheCore
@testable import DataRepositoryCore

final class DataRepositoryCoreTests: XCTestCase {
    func testExample() {
        XCTAssertTrue(true)
    }

    func testSendVerificationCode() async throws {
        let userRepository = UserRepository()
        let email = "<EMAIL>"
        print("【发送验证码测试】邮箱：\(email)")
        let response = await userRepository.sendVerificationCode(email: email)
        XCTAssertNotNil(response, "验证码发送响应不能为空")
        XCTAssertEqual(response?.code, 200, "验证码发送 code 应为 200")
        // 验证缓存
        let cache = CacheStorageManager.shared.storage(for: "user")
        let cacheKey = "sendCode_\(email)"
        let cached: BaseResponse<String>? = await cache.get(cacheKey)
        print("验证码发送缓存：", cached as Any)
        XCTAssertNotNil(cached, "验证码发送缓存不能为空")
    }

    func testVerifyCode() async throws {
        let userRepository = UserRepository()
        let email = "<EMAIL>"
        let code = "308174"
        print("【验证码登录测试】邮箱：\(email)")
        let response = await userRepository.verifyCode(email: email, code: code)
        XCTAssertNotNil(response, "登录响应不能为空")
        XCTAssertFalse(response?.token.isEmpty ?? true, "token 不能为空")
        XCTAssertGreaterThan(response?.user.uid ?? 0, 0, "用户ID应大于0")
        // 验证缓存
        let cache = CacheStorageManager.shared.storage(for: "user")
        let cacheKey = "verifyCode_\(email)_\(code)"
        let cached: BaseResponseRaw? = await cache.get(cacheKey)
        print("验证码登录缓存：", cached as Any)
        XCTAssertNotNil(cached, "验证码登录缓存不能为空")
        // 验证 token
        let token = TokenManager.shared.getToken()
        print("本地 token：", token as Any)
        XCTAssertNotNil(token, "本地 token 不能为空")
    }

    func testGetUserInfo() async throws {
        let userRepository = UserRepository()
        TokenManager.shared.setToken("eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI0MzljYzA3NDYyMTA0MzY5OTJjMTgyYjI4MTlmYWEyNiIsInN1YiI6IjQ4Iiwicm9sZSI6InVzZXIiLCJpc3MiOiJodHRwczovL2FwaS45N2FpLmZ1biIsImF1ZCI6Imh0dHBzOi8vd3d3Ljk3YWkuZnVuIiwiaWF0IjoxNzUyMDQ1OTEyLCJleHAiOjE3NTI2NTA3MTJ9.whE3ZPUyrBxBGWPLyL4u6BTk1rV2B5ucFOMFFggLoXM")
        let userInfo = await userRepository.getUserInfo()
        XCTAssertNotNil(userInfo, "用户信息不能为空")
        XCTAssertGreaterThan(userInfo?.uid ?? 0, 0, "用户ID应大于0")
        // 验证缓存
        let cache = CacheStorageManager.shared.storage(for: "user")
        let cached: UserModel? = await cache.get("userInfo")
        print("用户信息缓存：", cached as Any)
        XCTAssertNotNil(cached, "用户信息缓存不能为空")
    }

//    func testUpdatePassword() async throws {
//        let userRepository = UserRepository()
//        let email = "<EMAIL>"
//        let code = "123456"
//        let newPassword = "testpassword2"
//        print("【修改密码测试】邮箱：\(email)")
//        // 验证码登录
//        _ = await userRepository.verifyCode(email: email, code: code)
//        // 修改密码
//        let response = await userRepository.updatePassword(pw: "testpassword", npw: newPassword)
//        XCTAssertNotNil(response, "修改密码响应不能为空")
//        XCTAssertEqual(response?.code, 0, "修改密码 code 应为 0")
//        // 验证缓存
//        let cache = CacheStorageManager.shared.storage(for: "user")
//        let cacheKey = "updatePassword_testpassword_\(newPassword)"
//        let cached: BaseResponseRaw? = await cache.get(cacheKey)
//        print("修改密码缓存：", cached as Any)
//        XCTAssertNotNil(cached, "修改密码缓存不能为空")
//    }

    func testLogout() async throws {
        let userRepository = UserRepository()
        TokenManager.shared.setToken("eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI0MzljYzA3NDYyMTA0MzY5OTJjMTgyYjI4MTlmYWEyNiIsInN1YiI6IjQ4Iiwicm9sZSI6InVzZXIiLCJpc3MiOiJodHRwczovL2FwaS45N2FpLmZ1biIsImF1ZCI6Imh0dHBzOi8vd3d3Ljk3YWkuZnVuIiwiaWF0IjoxNzUyMDQ1OTEyLCJleHAiOjE3NTI2NTA3MTJ9.whE3ZPUyrBxBGWPLyL4u6BTk1rV2B5ucFOMFFggLoXM")
        // 退出登录
        let response = await userRepository.logout()
        XCTAssertNotNil(response, "退出登录响应不能为空")
        XCTAssertEqual(response?.code, 0, "退出登录 code 应为 0")
        // 验证缓存
        let cache = CacheStorageManager.shared.storage(for: "user")
        let cached: BaseResponseRaw? = await cache.get("logout")
        print("退出登录缓存：", cached as Any)
        XCTAssertNotNil(cached, "退出登录缓存不能为空")
    }
} 
