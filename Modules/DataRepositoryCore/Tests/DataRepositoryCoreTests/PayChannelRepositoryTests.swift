import XCTest
import NetworkCore
import CacheCore
@testable import DataRepositoryCore

final class PayChannelRepositoryTests: XCTestCase {
    func testList_normal() async throws {
        print("【PayChannelRepository】测试 list 正常参数，期望返回支付通道列表")
        let repo = PayChannelRepository()
        let params: [String: Any] = [:]
        print("输入参数:", params)
        let list = await repo.list(params: params)
        print("实际返回:", list as Any)
        XCTAssertNotNil(list)
    }
    func testAdd_normal() async throws {
        print("【PayChannelRepository】测试 add 正常参数，期望新增成功")
        let repo = PayChannelRepository()
        let params: [String: Any] = ["name": "test"]
        print("输入参数:", params)
        let resp = await repo.add(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testUpdate_normal() async throws {
        print("【PayChannelRepository】测试 update 正常参数，期望修改成功")
        let repo = PayChannelRepository()
        let params: [String: Any] = ["id": 1, "name": "test"]
        print("输入参数:", params)
        let resp = await repo.update(params: params)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testDelete_normal() async throws {
        print("【PayChannelRepository】测试 delete 正常参数，期望删除成功")
        let repo = PayChannelRepository()
        let ids = [1]
        print("输入参数: ids=", ids)
        let resp = await repo.delete(ids: ids)
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
    func testDelete_emptyIds() async throws {
        print("【PayChannelRepository】测试 delete 空数组，期望返回 nil")
        let repo = PayChannelRepository()
        let ids: [Int] = []
        print("输入参数: ids=", ids)
        let resp = await repo.delete(ids: ids)
        print("实际返回:", resp as Any)
        XCTAssertNil(resp)
    }
    func testPaymentMethod_normal() async throws {
        print("【PayChannelRepository】测试 paymentMethod，期望返回支付方式列表")
        let repo = PayChannelRepository()
        print("无输入参数")
        let resp = await repo.paymentMethod()
        print("实际返回:", resp as Any)
        XCTAssertNotNil(resp)
    }
} 