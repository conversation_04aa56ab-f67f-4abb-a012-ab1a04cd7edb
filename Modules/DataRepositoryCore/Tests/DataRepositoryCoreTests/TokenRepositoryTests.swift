import XCTest
import NetworkCore
import CacheCore
@testable import DataRepositoryCore

final class TokenRepositoryTests: XCTestCase {
    func testGetToken_normal() async throws {
        print("【TokenRepository】测试 getToken 正常参数，期望获取 token")
        let repo = TokenRepository()
        let username = "test"
        let password = "test"
        print("输入参数: username=", username, "password=", password)
        let token = await repo.getToken(username: username, password: password)
        print("实际返回:", token as Any)
        XCTAssertNotNil(token)
    }
    func testGetToken_emptyUsername() async throws {
        print("【TokenRepository】测试 getToken 空用户名，期望返回 nil")
        let repo = TokenRepository()
        let username = ""
        let password = "test"
        print("输入参数: username=", username, "password=", password)
        let token = await repo.getToken(username: username, password: password)
        print("实际返回:", token as Any)
        XCTAssertNil(token)
    }
    func testGetToken_emptyPassword() async throws {
        print("【TokenRepository】测试 getToken 空密码，期望返回 nil")
        let repo = TokenRepository()
        let username = "test"
        let password = ""
        print("输入参数: username=", username, "password=", password)
        let token = await repo.getToken(username: username, password: password)
        print("实际返回:", token as Any)
        XCTAssertNil(token)
    }
} 