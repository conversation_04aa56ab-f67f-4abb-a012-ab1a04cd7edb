import XCTest
import NetworkCore
import CacheCore
@testable import DataRepositoryCore

final class UploadRepositoryTests: XCTestCase {
    func testUpload_normal() async throws {
        print("【UploadRepository】测试 upload 正常参数，期望上传成功")
        let repo = UploadRepository()
        let dummyData = Data([0x01, 0x02])
        let fileName = "test.txt"
        print("输入参数: file=Data([0x01, 0x02]), fileName=", fileName)
        let upload = await repo.upload(file: dummyData, fileName: fileName)
        print("实际返回:", upload as Any)
        XCTAssertNotNil(upload)
    }
    func testUpload_emptyFile() async throws {
        print("【UploadRepository】测试 upload 空文件，期望返回 nil")
        let repo = UploadRepository()
        let fileName = "test.txt"
        print("输入参数: file=Data(), fileName=", fileName)
        let upload = await repo.upload(file: Data(), fileName: fileName)
        print("实际返回:", upload as Any)
        XCTAssertNil(upload)
    }
    func testUpload_emptyFileName() async throws {
        print("【UploadRepository】测试 upload 空文件名，期望返回 nil")
        let repo = UploadRepository()
        let dummyData = Data([0x01, 0x02])
        let fileName = ""
        print("输入参数: file=Data([0x01, 0x02]), fileName=", fileName)
        let upload = await repo.upload(file: dummyData, fileName: fileName)
        print("实际返回:", upload as Any)
        XCTAssertNil(upload)
    }
} 