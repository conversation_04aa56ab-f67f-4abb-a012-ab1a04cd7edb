// swift-tools-version: 6.1
import PackageDescription

let package = Package(
    name: "DataRepositoryCore",
    platforms: [
        .iOS(.v16),
        .macOS(.v12),
        .tvOS(.v16),
        .watchOS(.v8)
    ],
    products: [
        .library(
            name: "DataRepositoryCore",
            targets: ["DataRepositoryCore"]
        ),
    ],
    dependencies: [
        .package(path: "../ModelCore"),
        .package(path: "../CacheCore"),
        .package(path: "../NetworkCore")
    ],
    targets: [
        .target(
            name: "DataRepositoryCore",
            dependencies: [
                "ModelCore",
                "CacheCore",
                "NetworkCore"
            ]
        ),
        .testTarget(
            name: "DataRepositoryCoreTests",
            dependencies: ["DataRepositoryCore"]
        ),
    ]
) 