import Foundation
import CacheCore
import NetworkCore
import LogCore

/// 用户数据仓库，负责用户相关数据的本地缓存与网络请求
public class UserRepository {
    private let cache: CacheStorage
    
    public init() {
        self.cache = CacheStorageManager.shared.storage(for: "user")
    }
    
    /// 发送邮箱验证码
    public func sendVerificationCode(email: String, policy: CacheStorage.CachePolicy = .networkOnly) async -> BaseResponseRaw? {
        let cacheKey = "sendCode_\(email)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                UserRequest.sendVerificationCode(email: email) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                      
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 邮箱验证码登录/注册
    public func verifyCode(email: String, code: String, policy: CacheStorage.CachePolicy = .networkOnly) async -> UserLoginData? {
        let cacheKey = "verifyCode_\(email)_\(code)"
        let response = await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                UserRequest.verifyCode(email: email, code: code) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
        XLog.i("获取token1 \(response?.token ?? "nil")")
        // 自动存储 token
        if let token = response?.token, !token.isEmpty {
            XLog.i("存储token")
            TokenManager.shared.setToken(token)
        }
        return response
    }
    /// 查询用户信息
    public func getUserInfo(policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> UserModel? {
        let cacheKey = "userInfo"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                UserRequest.getUserInfo { result in
                    switch result {
                    case .success(let data):
                        XLog.i("获取data\(data)")
                        continuation.resume(returning: data)
                    case .failure:
                        XLog.i("接口请求失败")
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 退出登录
    public func logout(policy: CacheStorage.CachePolicy = .networkOnly) async -> BaseResponseRaw? {
        let cacheKey = "logout"
        let response = await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                UserRequest.logout { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
        // 自动移除 token
        TokenManager.shared.removeToken()
        return response
    }
    /// 修改密码
    public func updatePassword(pw: String, npw: String, policy: CacheStorage.CachePolicy = .networkOnly) async -> BaseResponseRaw? {
        let cacheKey = "updatePassword_\(pw)_\(npw)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                UserRequest.updatePassword(pw: pw, npw: npw) { result in
                    switch result {
                    case .success(let response):
                        continuation.resume(returning: response)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 检查是否已登录（本地有 token 即视为已登录）
    public func isLoggedIn() -> Bool {
        guard let token = TokenManager.shared.getToken(), !token.isEmpty else { return false }
        return true
    }
    /// 检查是否为游客（本地无 token 即视为游客）
    public func isGuest() -> Bool {
        guard let token = TokenManager.shared.getToken(), !token.isEmpty else { return true }
        return false
    }
    
    /// 获取用户头像URL
    /// - Returns: 返回用户头像URL，如果获取失败或用户未登录则返回nil
    public func getUserAvatarURL() async -> URL? {
        // 先检查用户是否登录
        guard isLoggedIn() else { return nil }
        
        // 获取用户信息
        if let userInfo = await getUserInfo() {
            // 如果有头像URL，转换为URL类型并返回
            if let avatarUrl = userInfo.avatarUrl, !avatarUrl.isEmpty {
                return URL(string: avatarUrl)
            }
        }
        return nil
    }
    
    // 移除本地 token 相关方法
    // public func setToken(_ token: String) { ... }
    // public func getToken() -> String? { ... }
    // public func removeToken() { ... }
    // public var tokenChanges: AsyncStream<String?> { ... }
    // private func notifyTokenChanged(_ token: String?) { ... }
} 
