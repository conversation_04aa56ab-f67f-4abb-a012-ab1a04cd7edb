import Foundation
import CacheCore
import NetworkCore

/// 角色数据仓库，负责角色相关数据的本地缓存与网络请求
public class CharacterRepository {
    private let cache: CacheStorage
    
    public init() {
        self.cache = CacheStorageManager.shared.storage(for: "character")
    }
    /// 获取角色标签
    public func tags(policy: CacheStorage.CachePolicy = .networkThenCache) async -> CharacterTagsResponse? {
        let cacheKey = "tags"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.tags { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 获取角色列表
    public func list(params: [String: Any], policy: CacheStorage.CachePolicy = .networkThenCache) async -> CharacterListResponse? {
        let pageNum = params["pageNum"] as? String ?? ""
        let pageSize = params["pageSize"] as? String ?? ""
        let cacheKey = "list_\(pageNum)_\(pageSize)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.list(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 获取角色详情
    public func detail(cid: String, policy: CacheStorage.CachePolicy = .networkThenCache) async -> CharacterDetailResponse? {
        let cacheKey = "detail_\(cid)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.detail(cid: cid) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 获取角色所有帖子
    public func posts(cid: String, policy: CacheStorage.CachePolicy = .networkThenCache) async -> CharacterPostListResponse? {
        let cacheKey = "posts_\(cid)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.posts(cid: cid) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 获取用户创建的角色列表
    public func userList(params: [String: Any], policy: CacheStorage.CachePolicy = .networkThenCache) async -> CharacterUserListResponse? {
        let pageNum = params["pageNum"] as? String ?? ""
        let pageSize = params["pageSize"] as? String ?? ""
        let cacheKey = "userList_\(pageNum)_\(pageSize)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.userList(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 搜索角色
    public func searchTag(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> CharacterSearchResponse? {
        let keyword = params["keyword"] as? String ?? ""
        let page = params["page"] as? String ?? ""
        let tags = params["tags"] as? String ?? ""
        let cacheKey = "search_tag_\(keyword)_\(page)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.searchTag(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 搜索帖子
    public func searchKeyword(params: [String: Any], policy: CacheStorage.CachePolicy = .networkThenCache) async -> PostSearchResponse? {
        let keyword = params["keyword"] as? String ?? ""
        let page = params["page"] as? String ?? ""
        let cacheKey = "search_keyword_\(keyword)_\(page)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.searchKeyword(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 获取角色搜索词
    public func searchWords(num: Int, policy: CacheStorage.CachePolicy = .networkThenCache) async -> CharacterSearchWordsResponse? {
        let cacheKey = "searchWords_\(num)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.searchWords(num: num) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 创建角色
    public func create(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> CharacterCreateResponse? {
        let nickName = params["nickName"] as? String ?? ""
        let cacheKey = "create_\(nickName)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.create(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 修改角色
    public func edit(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> CharacterEditResponse? {
        let cid = params["cid"] as? String ?? ""
        let cacheKey = "edit_\(cid)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.edit(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 收藏/取消收藏角色
    public func collect(cid: String, isCollect: Bool, policy: CacheStorage.CachePolicy = .networkOnly) async -> CharacterCollectResponse? {
        let cacheKey = "collect_\(cid)_\(isCollect)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CharacterRequest.collect(cid: cid, isCollect: isCollect) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
} 
