import Foundation
import CacheCore
import NetworkCore

/// 推荐卡片数据模型
public struct RecommendCardItem: Identifiable, Equatable, Sendable {
    public let id: UUID
    public let coverURL: String
    public let title: String
    public let avatarURL: String
    public let subtitle: String
    public let likeCount: Int
    public let isLiked: Bool

    public init(
        id: UUID,
        coverURL: String,
        title: String,
        avatarURL: String,
        subtitle: String,
        likeCount: Int,
        isLiked: Bool
    ) {
        self.id = id
        self.coverURL = coverURL
        self.title = title
        self.avatarURL = avatarURL
        self.subtitle = subtitle
        self.likeCount = likeCount
        self.isLiked = isLiked
    }
}

/// 帖子数据仓库，负责帖子相关数据的本地缓存与网络请求
public class PostRepository {
    private let cache: CacheStorage
    
    public init() {
        self.cache = CacheStorageManager.shared.storage(for: "post")
    }
    /// 获取帖子列表
    public func list(params: [String: Any], policy: CacheStorage.CachePolicy = .networkThenCache) async -> PostListResponse? {
        let pageNum = params["pageNum"] as? String ?? ""
        let pageSize = params["pageSize"] as? String ?? ""
        let cacheKey = "list_\(pageNum)_\(pageSize)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                PostRequest.list(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 获取帖子详情
    public func detail(pid: Int, policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> PostDetailResponse? {
        let cacheKey = "detail_\(pid)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                PostRequest.detail(pid: pid) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 搜索帖子
    public func search(params: [String: Any], policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> PostSearchResponse? {
        let keyword = params["keyword"] as? String ?? ""
        let page = params["page"] as? String ?? ""
        let cacheKey = "search_\(keyword)_\(page)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                PostRequest.search(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 发布帖子
    public func publish(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> PostPublishResponse? {
        let cid = params["cid"] as? String ?? ""
        let prompt = params["prompt"] as? String ?? ""
        let cacheKey = "publish_\(cid)_\(prompt)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                PostRequest.publish(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }

    // MARK: - 推荐卡片数据转换方法

    /// 获取推荐卡片列表
    public func getRecommendCardItems(
        pageNum: Int,
        pageSize: Int,
        policy: CacheStorage.CachePolicy = .networkThenCache
    ) async -> [RecommendCardItem]? {
        let params: [String: Any] = [
            "pageNum": "\(pageNum)",
            "pageSize": "\(pageSize)"
        ]

        guard let response = await list(params: params, policy: policy) else {
            return nil
        }

        return response.rows.map(convertPostToRecommendCard)
    }

    /// 搜索推荐卡片列表
    public func searchRecommendCardItems(
        pageNum: Int,
        pageSize: Int,
        keyword: String,
        policy: CacheStorage.CachePolicy = .cacheThenNetwork
    ) async -> [RecommendCardItem]? {
        let params: [String: Any] = [
            "pageNum": "\(pageNum)",
            "pageSize": "\(pageSize)",
            "keyword": keyword
        ]

        guard let response = await search(params: params, policy: policy) else {
            return nil
        }

        return response.map(convertPostToRecommendCard)
    }

    /// 将PostInfo转换为RecommendCardItem（内部方法，实际实现在扩展中）
    internal func convertPostToRecommendCard(_ post: PostInfo) -> RecommendCardItem {
        return convertPostToRecommendCard(post, config: .default)
    }
}
