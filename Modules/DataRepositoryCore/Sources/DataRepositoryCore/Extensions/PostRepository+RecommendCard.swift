import Foundation
import NetworkCore
import CacheCore

// MARK: - PostRepository推荐卡片扩展
extension PostRepository {
    
    /// 推荐卡片获取结果
    public enum RecommendCardResult {
        case success([RecommendCardItem])
        case failure(RecommendCardError)
    }
    
    /// 推荐卡片相关错误
    public enum RecommendCardError: Error, LocalizedError {
        case networkError
        case dataParsingError
        case invalidParameters
        case noData
        
        public var errorDescription: String? {
            switch self {
            case .networkError:
                return "网络请求失败"
            case .dataParsingError:
                return "数据解析失败"
            case .invalidParameters:
                return "参数无效"
            case .noData:
                return "暂无数据"
            }
        }
    }
    
    // MARK: - 推荐卡片获取方法（带错误处理）
    
    /// 获取推荐卡片列表（带详细错误信息）
    public func getRecommendCardItemsWithResult(
        pageNum: Int,
        pageSize: Int,
        policy: CacheStorage.CachePolicy = .networkThenCache
    ) async -> RecommendCardResult {
        // 参数验证
        guard pageNum > 0, pageSize > 0 else {
            return .failure(.invalidParameters)
        }
        
        let params: [String: Any] = [
            "pageNum": "\(pageNum)",
            "pageSize": "\(pageSize)"
        ]
        
        guard let response = await list(params: params, policy: policy) else {
            return .failure(.networkError)
        }
        
        let items = response.rows.map(convertPostToRecommendCard)
        return items.isEmpty ? .failure(.noData) : .success(items)
    }
    
    /// 搜索推荐卡片列表（带详细错误信息）
    public func searchRecommendCardItemsWithResult(
        pageNum: Int,
        pageSize: Int,
        keyword: String,
        policy: CacheStorage.CachePolicy = .cacheThenNetwork
    ) async -> RecommendCardResult {
        // 参数验证
        guard pageNum > 0, pageSize > 0, !keyword.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return .failure(.invalidParameters)
        }
        
        let params: [String: Any] = [
            "pageNum": "\(pageNum)",
            "pageSize": "\(pageSize)",
            "keyword": keyword
        ]
        
        guard let response = await search(params: params, policy: policy) else {
            return .failure(.networkError)
        }
        
        let items = response.map(convertPostToRecommendCard)
        return items.isEmpty ? .failure(.noData) : .success(items)
    }
    
    // MARK: - 数据转换辅助方法
    
    /// 批量转换PostInfo为RecommendCardItem
    internal func convertPostsToRecommendCards(_ posts: [PostInfo]) -> [RecommendCardItem] {
        return posts.map(convertPostToRecommendCard)
    }
    
    /// 验证PostInfo数据完整性
    internal func validatePostInfo(_ post: PostInfo) -> Bool {
        // 基本字段验证
        guard let pid = post.pid, !pid.isEmpty else { return false }
        guard let title = post.title, !title.isEmpty else { return false }
        return true
    }
    
    /// 安全的图片URL提取
    internal func safeExtractImageURL(from gallery: String?) -> String {
        guard let gallery = gallery?.trimmingCharacters(in: .whitespacesAndNewlines),
              !gallery.isEmpty else {
            return ""
        }
        
        let urls = gallery.split(separator: ",")
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        
        return urls.first ?? ""
    }
    
    /// 生成稳定的UUID
    internal func generateStableUUID(from pid: String) -> UUID {
        // 使用pid生成稳定的UUID，确保相同的pid总是生成相同的UUID
        let data = "post-\(pid)".data(using: .utf8)!

        // 简单的UUID生成策略，实际项目中可以使用更复杂的算法
        if let uuid = UUID(uuidString: "post-\(pid)") {
            return uuid
        } else {
            // 如果无法直接转换，使用hash生成
            let hash = data.hashValue
            let uuidString = String(format: "%08x-0000-0000-0000-%012x",
                                  abs(hash) % 0x100000000,
                                  abs(hash) % 0x1000000000000)
            return UUID(uuidString: uuidString) ?? UUID()
        }
    }
}

// MARK: - 数据转换配置
extension PostRepository {
    
    /// 推荐卡片转换配置
    public struct RecommendCardConfig: Sendable {
        public let defaultLikeCount: Int
        public let enableImageValidation: Bool
        public let maxTitleLength: Int
        public let maxSubtitleLength: Int

        public init(
            defaultLikeCount: Int = 0,
            enableImageValidation: Bool = false,
            maxTitleLength: Int = 100,
            maxSubtitleLength: Int = 50
        ) {
            self.defaultLikeCount = defaultLikeCount
            self.enableImageValidation = enableImageValidation
            self.maxTitleLength = maxTitleLength
            self.maxSubtitleLength = maxSubtitleLength
        }

        public static let `default`: RecommendCardConfig = RecommendCardConfig()
    }
    
    /// 使用配置进行数据转换
    internal func convertPostToRecommendCard(
        _ post: PostInfo, 
        config: RecommendCardConfig = .default
    ) -> RecommendCardItem {
        let id = generateStableUUID(from: post.pid ?? "")
        let coverURL = safeExtractImageURL(from: post.gallery)
        
        // 应用长度限制
        let title = String((post.title ?? "").prefix(config.maxTitleLength))
        let subtitle = String((post.name ?? "").prefix(config.maxSubtitleLength))
        
        let avatarURL = post.avatar ?? ""
        let likeCount = config.defaultLikeCount
        let isLiked = (post.userIsCollect ?? 0) == 1
        
        return RecommendCardItem(
            id: id,
            coverURL: coverURL,
            title: title,
            avatarURL: avatarURL,
            subtitle: subtitle,
            likeCount: likeCount,
            isLiked: isLiked
        )
    }
}
