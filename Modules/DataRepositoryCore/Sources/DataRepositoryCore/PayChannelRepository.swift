import Foundation
import CacheCore
import NetworkCore

/// 支付通道数据仓库，负责支付通道相关数据的本地缓存与网络请求
public class PayChannelRepository {
    private let cache: CacheStorage
    
    public init() {
        self.cache = CacheStorageManager.shared.storage(for: "payChannel")
    }
    /// 获取支付通道列表
    public func list(params: [String: Any], policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> PayChannelListResponse? {
        let cacheKey = "list"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                PayChannelRequest.list(params: params) { result in
                    switch result {
                    case .success(let response):
                        continuation.resume(returning: response)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 新增支付通道
    public func add(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> PayChannelResponse? {
        let cacheKey = "add"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                PayChannelRequest.add(params: params) { result in
                    switch result {
                    case .success(let response):
                        continuation.resume(returning: response)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 修改支付通道
    public func update(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> PayChannelResponse? {
        let cacheKey = "update"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                PayChannelRequest.update(params: params) { result in
                    switch result {
                    case .success(let response):
                        continuation.resume(returning: response)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 删除支付通道
    public func delete(ids: [Int], policy: CacheStorage.CachePolicy = .networkOnly) async -> PayChannelResponse? {
        let cacheKey = "delete_\(ids)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                PayChannelRequest.delete(ids: ids) { result in
                    switch result {
                    case .success(let response):
                        continuation.resume(returning: response)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 获取支付方式
    public func paymentMethod(policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> PayChannelPaymentMethodResponse? {
        let cacheKey = "paymentMethod"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                PayChannelRequest.paymentMethod { result in
                    switch result {
                    case .success(let response):
                        continuation.resume(returning: response)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
} 
