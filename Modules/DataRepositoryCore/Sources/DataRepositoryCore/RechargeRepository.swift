import Foundation
import CacheCore
import NetworkCore

/// 充值数据仓库，负责充值相关数据的本地缓存与网络请求
public class RechargeRepository {
    private let cache: CacheStorage
    
    public init() {
        self.cache = CacheStorageManager.shared.storage(for: "recharge")
    }
    /// 用户端充值
    public func userPay(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> RechargeResponse? {
        let coinPackageId = params["coinPackageId"] as? String ?? ""
        let paymentMethod = params["paymentMethod"] as? String ?? ""
        let cacheKey = "userPay_\(coinPackageId)_\(paymentMethod)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                RechargeRequest.userPay(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 管理端充值
    public func adminPay(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> RechargeResponse? {
        let userId = params["userId"] as? String ?? ""
        let paymentMethod = params["paymentMethod"] as? String ?? ""
        let cacheKey = "adminPay_\(userId)_\(paymentMethod)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                RechargeRequest.adminPay(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 检查订单状态
    public func checkOrderStatus(orderNo: String, policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> RechargeOrderStatusResponse? {
        let cacheKey = "checkOrderStatus_\(orderNo)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                RechargeRequest.checkOrderStatus(orderNo: orderNo) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 管理端充值记录
    public func adminRecordList(params: [String: Any], policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> RechargeRecordResponse? {
        let cacheKey = "adminRecordList"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                RechargeRequest.adminRecordList(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 用户端充值记录
    public func userRecordList(params: [String: Any], policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> RechargeRecordResponse? {
        let cacheKey = "userRecordList"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                RechargeRequest.userRecordList(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
} 