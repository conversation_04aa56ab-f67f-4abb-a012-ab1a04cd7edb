import Foundation
import CacheCore
import NetworkCore

/// 文件上传数据仓库，负责文件上传相关数据的本地缓存与网络请求
public class UploadRepository {
    private let cache: CacheStorage
    
    public init() {
        self.cache = CacheStorageManager.shared.storage(for: "upload")
    }
    /// 上传文件
    public func upload(file: Data, fileName: String, policy: CacheStorage.CachePolicy = .networkOnly) async -> UploadResponse? {
        let cacheKey = "upload_\(fileName)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                UploadRequest.upload(file: file, fileName: fileName) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
} 