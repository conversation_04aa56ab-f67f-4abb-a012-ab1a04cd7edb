import Foundation
import CacheCore
import NetworkCore

/// 金币套餐数据仓库，负责金币套餐相关数据的本地缓存与网络请求
public class CoinPackageRepository {
    private let cache: CacheStorage
    
    public init() {
        self.cache = CacheStorageManager.shared.storage(for: "coinPackage")
    }
    /// 获取金币套餐列表
    public func list(params: [String: Any], policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> CoinPackageListResponse? {
        let cacheKey = "list"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CoinPackageRequest.list(params: params) { result in
                    switch result {
                    case .success(let data):
                        let safeData = data
                        continuation.resume(returning: safeData)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 新增金币套餐
    public func add(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> CoinPackageResponse? {
        let cacheKey = "add"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CoinPackageRequest.add(params: params) { result in
                    switch result {
                    case .success(let data):
                        let safeData = data
                        continuation.resume(returning: safeData)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 修改金币套餐
    public func update(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> CoinPackageResponse? {
        let cacheKey = "update"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CoinPackageRequest.update(params: params) { result in
                    switch result {
                    case .success(let data):
                        let safeData = data
                        continuation.resume(returning: safeData)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    /// 删除金币套餐
    public func delete(ids: [Int], policy: CacheStorage.CachePolicy = .networkOnly) async -> CoinPackageResponse? {
        let cacheKey = "delete_\(ids)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                CoinPackageRequest.delete(ids: ids) { result in
                    switch result {
                    case .success(let data):
                        let safeData = data
                        continuation.resume(returning: safeData)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
} 
