import Foundation
import CacheCore
import NetworkCore

/// 聊天数据仓库，负责聊天相关数据的本地缓存与网络请求
public class ChatRepository {
    private let cache: CacheStorage
    
    public init() {
        self.cache = CacheStorageManager.shared.storage(for: "chat")
    }
    
    /// 获取 clientId（打招呼）
    public func greet(cid: String, policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> ChatGreetResponse? {
        let cacheKey = "greet_\(cid)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                ChatRequest.greet(cid: cid) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    
    /// 发送聊天内容
    public func send(params: [String: Any], policy: CacheStorage.CachePolicy = .networkOnly) async -> ChatSendResponse? {
        let cid = params["cid"] as? String ?? ""
        let message = params["message"] as? String ?? ""
        let cacheKey = "send_\(cid)_\(message)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                ChatRequest.send(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    
    /// 获取聊天记录
    public func messageList(params: [String: Any], policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> ChatMessageListResponse? {
        let cid = params["cid"] as? String ?? ""
        let anchorId = params["anchorId"] as? String ?? ""
        let cacheKey = "messageList_\(cid)_\(anchorId)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                ChatRequest.messageList(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    
    /// 获取最近聊天记录
    public func lastRecord(params: [String: Any], policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> ChatLastRecordResponse? {
        let pageNum = params["pageNum"] as? String ?? ""
        let pageSize = params["pageSize"] as? String ?? ""
        let cacheKey = "lastRecord_\(pageNum)_\(pageSize)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                ChatRequest.lastRecord(params: params) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
    
    /// 文本转语音
    public func audio(id: String, policy: CacheStorage.CachePolicy = .cacheThenNetwork) async -> ChatAudioResponse? {
        let cacheKey = "audio_\(id)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                ChatRequest.audio(id: id) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
} 