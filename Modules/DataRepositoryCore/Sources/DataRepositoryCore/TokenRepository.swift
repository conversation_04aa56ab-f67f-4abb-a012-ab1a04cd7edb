import Foundation
import CacheCore
import NetworkCore

/// Token 数据仓库，负责 Token 相关数据的本地缓存与网络请求
public class TokenRepository {
    private let cache: CacheStorage
    
    public init() {
        self.cache = CacheStorageManager.shared.storage(for: "token")
    }
    /// 获取 Token
    public func getToken(username: String, password: String, policy: CacheStorage.CachePolicy = .networkOnly) async -> TokenModel? {
        let cacheKey = "getToken_\(username)"
        return await cache.fetch(key: cacheKey, policy: policy) {
            await withCheckedContinuation { continuation in
                TokenRequest.getToken(username: username, password: password) { result in
                    switch result {
                    case .success(let data):
                        continuation.resume(returning: data)
                    case .failure:
                        continuation.resume(returning: nil)
                    }
                }
            }
        }
    }
} 