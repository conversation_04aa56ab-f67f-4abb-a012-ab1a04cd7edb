import Foundation
import Logging

public enum XLog {
    /*
     使用方法:

     1. 在应用启动时进行配置 (例如, 在 AppDelegate 的 `didFinishLaunchingWithOptions` 中):

        // 默认只输出到控制台
        XLog.setup()

        // 同时输出到控制台和文件
        if let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first {
            let logFilePath = "\(documentsPath)/app.log"
            XLog.setup(destinations: [.console, .file(path: logFilePath)])
        }

        // 在开发环境启用日志，在生产环境禁用
        #if DEBUG
            XLog.setup(destinations: [.console])
        #else
            XLog.setup(isEnabled: false)
        #endif


     2. 在代码中的任何地方记录日志:

        XLog.i("用户登录成功")
        XLog.d("开始下载数据...")
        XLog.w("API 返回了一个不推荐使用的字段")
        // XLog.e("数据库连接失败: \(error.localizedDescription)")
        XLog.c("严重错误，应用即将崩溃")

    */
    nonisolated(unsafe)
    private static var _logger: Logger?
    nonisolated(unsafe)
    private static var _isEnabled: Bool = true
    
    // logger 访问器
    private static var logger: Logger {
        if _logger == nil {
            _logger = Logger(label: "com.xlog.default")
        }
        return _logger!
    }
    
    // isEnabled 访问器
    public static var isEnabled: Bool {
        get { _isEnabled }
        set { _isEnabled = newValue }
    }

    /// 预定义的日志输出目的地
    public enum LogDestination {
        case console
        case file(path: String)
    }

    /// 使用指定的目的地配置日志系统。
    /// 此方法应在应用启动时调用一次。
    ///
    /// - Parameters:
    ///   - destinations: 一个 `LogDestination` 数组，用于发送日志。默认为仅控制台。
    ///   - isEnabled: 用于全局启用或禁用日志的布尔值。
    public static func setup(destinations: [LogDestination] = [.console], isEnabled: Bool = true) {
        _isEnabled = isEnabled

        let factories: [(String) -> LogHandler] = destinations.compactMap { dest in
            switch dest {
            case .console:
                return StreamLogHandler.standardOutput
            case .file(let path):
                return { label in
                    if let fileHandler = FileLogHandler.file(label: label, path: path) {
                        return fileHandler
                    } else {
                        print("错误: 无法在路径初始化文件日志记录器: \(path)。")
                        return SwiftLogNoOpLogHandler()
                    }
                }
            }
        }

        // 捕获 isEnabled 的值以避免在 Sendable 闭包中引用 MainActor 隔离的属性
        let enabled = isEnabled
        
        LoggingSystem.bootstrap { label in
            guard enabled else {
                return SwiftLogNoOpLogHandler()
            }

            let handlers = factories.map { $0(label) }

            if handlers.isEmpty {
                return SwiftLogNoOpLogHandler()
            } else if handlers.count == 1 {
                return handlers[0]
            } else {
                return MultiplexLogHandler(handlers)
            }
        }

        // 引导后，重新初始化我们的静态日志记录器
        // 以确保它使用新的处理程序。
        _logger = Logger(label: "com.xlog.default")
    }

    // MARK: - 静态日志方法

    /// 记录一条信息性消息。
    public static func i(_ message: @autoclosure () -> Logger.Message, file: String = #file, function: String = #function, line: UInt = #line) {
        let messageValue = message()  // 立即执行
        guard _isEnabled else { return }
        logger.error(messageValue, file: file, function: function, line: line)
    }

    /// 记录一条调试消息。
    public static func d(_ message: @autoclosure () -> Logger.Message, file: String = #file, function: String = #function, line: UInt = #line) {
        let messageValue = message()  // 立即执行
        guard _isEnabled else { return }
        logger.debug(messageValue, file: file, function: function, line: line)
    }

    /// 记录一条警告消息。
    public static func w(_ message: @autoclosure () -> Logger.Message, file: String = #file, function: String = #function, line: UInt = #line) {
        let messageValue = message()  // 立即执行
        guard _isEnabled else { return }
        logger.warning(messageValue, file: file, function: function, line: line)
    }

    /// 记录一条错误消息。
    public static func e(_ message: @autoclosure () -> Logger.Message, file: String = #file, function: String = #function, line: UInt = #line) {
        let messageValue = message()  // 立即执行
        guard _isEnabled else { return }
        logger.error(messageValue, file: file, function: function, line: line)
    }

    /// 记录一条严重错误消息。
    public static func c(_ message: @autoclosure () -> Logger.Message, file: String = #file, function: String = #function, line: UInt = #line) {
        let messageValue = message()  // 立即执行
        guard _isEnabled else { return }
        logger.critical(messageValue, file: file, function: function, line: line)
    }
} 
