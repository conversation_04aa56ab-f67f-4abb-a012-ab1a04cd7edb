import Foundation
import Logging

public class FileLogHandler: LogHandler, @unchecked Sendable {
    private let fileHandle: FileHandle
    private let queue = DispatchQueue(label: "com.xlog.fileloghandler")
    private var label: String

    public var metadata: Logger.Metadata = [:]
    public var logLevel: Logger.Level = .info

    public subscript(metadataKey metadataKey: String) -> Logger.Metadata.Value? {
        get { metadata[metadataKey] }
        set { metadata[metadataKey] = newValue }
    }

    public init(label: String, fileHandle: FileHandle) {
        self.label = label
        self.fileHandle = fileHandle
    }

    public func log(level: Logger.Level,
                    message: Logger.Message,
                    metadata: Logger.Metadata?,
                    source: String,
                    file: String,
                    function: String,
                    line: UInt) {
        let logString = "[\(level)] [\(source)] [\(file):\(line)] - \(message)\n"
        if let data = logString.data(using: .utf8) {
            queue.async {
                self.fileHandle.write(data)
            }
        }
    }
}

public extension FileLogHandler {
    static func file(label: String, path: String, fileManager: FileManager = .default) -> FileLogHandler? {
        if !fileManager.fileExists(atPath: path) {
            fileManager.createFile(atPath: path, contents: nil, attributes: nil)
        }
        guard let fileHandle = FileHandle(forWritingAtPath: path) else {
            return nil
        }
        fileHandle.seekToEndOfFile()
        return FileLogHandler(label: label, fileHandle: fileHandle)
    }
} 
