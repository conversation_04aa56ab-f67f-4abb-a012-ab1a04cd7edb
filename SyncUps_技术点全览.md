# SyncUps 技术点全览

---

# 01 架构与基础约定

## 1.1 项目架构总览

SyncUps 工程采用 [Composable Architecture (TCA)](https://github.com/pointfreeco/swift-composable-architecture) 作为核心架构，结合 SwiftUI 实现高内聚、强类型、易测试的现代 iOS 应用。

- 主要采用 TCA 的 State/Action/Reducer 组合模式，所有业务逻辑均以 Reducer 为核心单元。
- 导航、状态管理、依赖注入、数据持久化等均通过 TCA 体系实现。
- 视图层采用 SwiftUI，所有页面均为声明式 View，状态通过 Store 传递。

## 1.2 TCA 选型与优势

- **统一的状态管理**：所有页面和业务模块的状态集中管理，便于调试和追踪。
- **强类型安全**：State/Action/Reducer 均为强类型，编译期即可发现大部分错误。
- **易于测试**：Reducer 纯函数特性，便于单元测试和集成测试。
- **导航与路由解耦**：通过 StackState 和 Path 枚举实现导航栈，页面跳转与业务逻辑解耦。
- **依赖注入灵活**：通过 @Dependency 注入依赖，便于 mock 和测试。

## 1.3 代码风格与命名规范

- 结构体、枚举、变量、方法均采用 Swift 官方推荐的驼峰命名法。
- 每个 Feature（功能模块）单独一个文件，包含 State、Action、Reducer、View。
- Reducer 统一使用 `@Reducer` 属性宏声明，简化模板代码。
- Action 命名采用"动词+对象"风格，如 `addAttendeeButtonTapped`、`deleteMeetings`。
- 重要业务流程、结构体、方法均有详细中文注释。

## 1.4 模块划分与文件结构

- 每个业务模块（如 AppFeature、SyncUpsList、SyncUpDetail、RecordMeeting、SyncUpForm 等）单独一个 Swift 文件。
- Models.swift 统一管理 SyncUp、Attendee、Meeting、Theme 等核心数据结构。
- Dependencies 目录下存放依赖相关代码（如 SpeechRecognizer、OpenSettings）。
- 资源文件（如音频、图片、主题色）集中在 Resources 和 Assets.xcassets 目录。
- 测试代码与业务代码分离，分别在 SyncUpsTests 和 SyncUpsUITests 目录。

## 1.5 依赖库与工具链

- 主要依赖：
  - ComposableArchitecture（TCA）
  - SwiftUI
  - IdentifiedCollections
  - Tagged
- 依赖管理方式：**Swift Package Manager (SPM)**
- 其他工具链配置等暂未在代码中体现，后续可补充。

---

# 02 状态与Action管理

## 2.1 State 设计原则
- 每个功能模块（Feature）都有独立的 State 结构体，负责描述该模块的所有状态数据。
- State 结构体需实现 Equatable，便于状态对比和调试。
- 复杂页面的 State 可包含子模块 State（如 AppFeature.State 包含 SyncUpsList.State）。
- 路由相关状态统一用 `var path = StackState<Path.State>()` 管理。

### 代码举例

#### AppFeature.State
```swift
struct State: Equatable {
    var path = StackState<Path.State>() // 路由栈
    var syncUpsList = SyncUpsList.State() // 列表页状态
}
```
- 顶层 State 组合了路由栈和子模块 State，便于统一管理。

#### SyncUpsList.State
```swift
struct State: Equatable {
    @Presents var destination: Destination.State? // 当前弹窗/新增页
    @Shared(.syncUps) var syncUps // 共享 SyncUp 列表
}
```
- 使用 @Presents/@Shared 属性管理弹窗和全局数据。

#### SyncUpDetail.State
```swift
struct State: Equatable {
    @Presents var destination: Destination.State? // 当前弹窗/编辑页
    @Shared var syncUp: SyncUp // 共享的 SyncUp 数据
}
```
- 详情页 State 关注弹窗和当前 SyncUp。

#### RecordMeeting.State
```swift
struct State: Equatable {
    @Presents var alert: AlertState<Action.Alert>? // 弹窗状态
    var secondsElapsed = 0 // 已用秒数
    var speakerIndex = 0 // 当前发言人索引
    @Shared var syncUp: SyncUp // 共享的 SyncUp 数据
    var transcript = "" // 语音转录文本
    var durationRemaining: Duration { ... }
}
```
- 会议记录 State 关注计时、发言人、转录、弹窗等。

#### SyncUpForm.State
```swift
struct State: Equatable, Sendable {
    var focus: Field? = .title // 当前焦点
    var syncUp: SyncUp // 编辑中的 SyncUp
    enum Field: Hashable { case attendee(Attendee.ID); case title }
}
```
- 表单 State 关注输入焦点和编辑对象。

---

## 2.x TCA 常用宏与宏定义

### @Reducer
- 用于声明一个 Reducer，自动生成 TCA 所需协议实现，简化模板代码。
- 支持嵌套 Reducer，便于路由和子模块管理。
- 例：
  ```swift
  @Reducer
  struct AppFeature { ... }
  ```

### @ObservableState
- 用于标记 State 结构体，使其支持 TCA 的状态变更监听和绑定。
- 例：
  ```swift
  @ObservableState
  struct State: Equatable { ... }
  ```

### @Presents
- 用于声明可选的弹窗、sheet、子页面等状态，配合 PresentationAction 使用。
- 例：
  ```swift
  @Presents var destination: Destination.State?
  @Presents var alert: AlertState<Action.Alert>?
  ```

### @Shared
- 用于声明全局共享的状态（如全局数据存储、跨页面共享数据等）。
- 例：
  ```swift
  @Shared(.syncUps) var syncUps
  @Shared var syncUp: SyncUp
  ```

### @CasePathable
- 用于枚举，自动生成 case 路径，便于 Action 的模式匹配和路由。
- 例：
  ```swift
  @CasePathable
  enum Alert { ... }
  ```

### @Dependency
- 用于依赖注入，将外部依赖（如时间、UUID、服务等）注入到 Reducer 中。
- 例：
  ```swift
  @Dependency(\.date.now) var now
  @Dependency(\.uuid) var uuid
  @Dependency(\.speechClient) var speechClient
  ```

### @Bindable
- 用于 View 层 Store 的声明，支持与 State 的双向绑定。
- 例：
  ```swift
  @Bindable var store: StoreOf<AppFeature>
  ```

---

## 2.2 Action 设计原则
- 每个 Feature 都有独立的 Action 枚举，定义所有用户操作、系统事件、子模块事件等。
- Action 命名采用"动词+对象"风格，便于理解和追踪。
- 上层 Action 通过 case 组合子模块 Action（如 `case syncUpsList(SyncUpsList.Action)`）。
- 路由相关 Action 统一用 `case path(StackActionOf<Path>)` 管理。

### 代码举例

#### AppFeature.Action
```swift
enum Action {
    case path(StackActionOf<Path>) // 路由相关动作
    case syncUpsList(SyncUpsList.Action) // 列表相关动作
}
```
- 顶层 Action 组合子模块和路由相关事件。

#### SyncUpsList.Action
```swift
enum Action {
    case addSyncUpButtonTapped // 新增按钮点击
    case confirmAddSyncUpButtonTapped // 确认新增
    case destination(PresentationAction<Destination.Action>) // 弹窗/新增页相关动作
    case dismissAddSyncUpButtonTapped // 关闭新增页
    case onDelete(IndexSet) // 删除 SyncUp
}
```
- 列表页 Action 关注新增、删除、弹窗等。

#### SyncUpDetail.Action
```swift
enum Action: Sendable {
    case cancelEditButtonTapped // 取消编辑
    case delegate(Delegate) // 委托事件
    case deleteButtonTapped // 删除
    case deleteMeetings(atOffsets: IndexSet) // 删除会议记录
    case destination(PresentationAction<Destination.Action>) // 弹窗/编辑页相关动作
    case doneEditingButtonTapped // 完成编辑
    case editButtonTapped // 编辑
    case startMeetingButtonTapped // 开始会议
    enum Delegate { case startMeeting(Shared<SyncUp>) }
}
```
- 详情页 Action 关注编辑、删除、弹窗、会议启动等。

#### RecordMeeting.Action
```swift
enum Action {
    case alert(PresentationAction<Alert>) // 弹窗相关
    case endMeetingButtonTapped // 结束会议
    case nextButtonTapped // 下一个发言人
    case onTask // 启动任务
    case timerTick // 定时器触发
    case speechFailure // 语音识别失败
    case speechResult(SpeechRecognitionResult) // 语音识别结果
    enum Alert { case confirmDiscard; case confirmSave }
}
```
- 会议记录 Action 关注定时、语音、弹窗等。

#### SyncUpForm.Action
```swift
enum Action: BindableAction, Equatable, Sendable {
    case addAttendeeButtonTapped // 新增参会人
    case binding(BindingAction<State>) // 绑定动作
    case deleteAttendees(atOffsets: IndexSet) // 删除参会人
}
```
- 表单 Action 关注动态增删、输入绑定。

---

## 2.3 Reducer 组合与拆分
- 每个 Feature 都有独立的 Reducer，负责处理本模块的所有 Action。
- 通过 `Scope` 组合子模块 Reducer，实现模块解耦和独立开发。
- 通过 `.forEach(\.path, action: \.path)` 注册路由栈 Reducer，自动管理导航页面的生命周期和 Action 分发。

## 2.4 全局与局部 Action 区分
- SyncUps 工程以局部 Action 为主，每个模块只处理自身相关事件。
- 如需全局事件（如全局弹窗），可在顶层 State/Action 声明并在 Reducer 处理。
- 当前工程未见典型全局 Action，后续可根据需求补充。

## 2.5 @Reducer 宏的用法
- 所有 Reducer 统一使用 `@Reducer` 属性宏声明，自动生成协议实现，简化模板代码。
- 支持嵌套 Reducer，便于路由和子模块管理。
- 例：
  ```swift
  @Reducer
  struct AppFeature { ... }
  ```

## 2.6 StackAction 与 Path.Action
- 路由相关 Action 统一用 `case path(StackActionOf<Path>)` 管理。
- Path 枚举定义所有可导航页面，每个页面有独立 State/Action。
- 通过 StackAction 实现导航栈中每个页面的 Action 分发和状态同步。
- 例：
  ```swift
  enum Action {
    case path(StackActionOf<Path>)
    ...
  }
  ```

## 2.7 状态变更与不可变性
- 所有 State 均为 struct，遵循不可变性原则。
- 状态变更只能通过 Reducer 内部处理 Action 实现，禁止在 View 层直接修改 State。
- 通过 Store 发送 Action，Reducer 处理后返回新 State，保证数据流单向、可追踪。

---

# 03 路由与导航

## 3.1 路由栈 StackState 声明
- 顶层 State 通过 `var path = StackState<Path.State>()` 声明导航栈，统一管理所有可导航页面的状态。
- 例：
  ```swift
  struct State: Equatable {
      var path = StackState<Path.State>() // 路由栈
      ...
  }
  ```

## 3.2 Path 枚举与页面注册
- 使用 Path 枚举集中声明所有可导航页面，每个 case 对应一个页面 Feature。
- 例：
  ```swift
  @Reducer
  enum Path {
      case detail(SyncUpDetail) // 详情页
      case meeting(Meeting, syncUp: SyncUp) // 会议页
      case record(RecordMeeting) // 记录会议页
  }
  ```

## 3.3 NavigationStack 用法
- 在主视图中使用 `NavigationStack(path: $store.scope(state: \.path, action: \.path))` 绑定导航栈，实现页面跳转与状态同步。
- 例：
  ```swift
  NavigationStack(path: $store.scope(state: \.path, action: \.path)) {
      ...
  }
  ```

## 3.4 NavigationLink 跳转
- 页面跳转通过 `NavigationLink(state: ...)` 实现，目标页面由 Path 枚举 case 决定。
- 例：
  ```swift
  NavigationLink(state: AppFeature.Path.State.detail(SyncUpDetail.State(syncUp: $syncUp))) {
      CardView(syncUp: syncUp)
  }
  ```

## 3.5 forEach 注册与生命周期
- Reducer 中通过 `.forEach(\.path, action: \.path)` 注册路由栈，自动管理导航页面的生命周期和 Action 分发。
- 例：
  ```swift
  .forEach(\.path, action: \.path)
  ```

## 3.6 scope 的初始化与拆分
- 使用 `scope(state:action:)` 方法将 Store 拆分为子 Store，便于子页面独立管理自己的状态和 Action。
- 例：
  ```swift
  SyncUpsListView(store: store.scope(state: \.syncUpsList, action: \.syncUpsList))
  ```

## 3.7 子页面 Store 传递
- 在 NavigationStack 的 destination 闭包中，根据 Path.case 传递对应子页面的 Store。
- 例：
  ```swift
  destination: { store in
      switch store.case {
      case let .detail(store):
          SyncUpDetailView(store: store)
      case let .meeting(meeting, syncUp):
          MeetingView(meeting: meeting, syncUp: syncUp)
      case let .record(store):
          RecordMeetingView(store: store)
      }
  }
  ```

## 3.8 路由与状态同步机制
- 所有页面跳转、返回、状态变更均通过 Action 触发，Reducer 处理，保证路由与状态同步、数据流单向。
- 路由栈的每个页面 State/Action 都自动注册到主导航栈，生命周期与导航同步。

---

# 06 主题色与动态UI

## 6.1 Theme 枚举设计
- 主题色通过 Theme 枚举集中管理，包含多种配色方案。
- 每个主题包含主色（mainColor）、强调色（accentColor）、名称（name）等属性。
- 例：
  ```swift
  enum Theme: String, CaseIterable, Equatable, Identifiable, Codable {
      case bubblegum, buttercup, indigo, lavender, magenta, navy, orange, oxblood, periwinkle, poppy, purple, seafoam, sky, tan, teal, yellow
      var id: Self { self }
      var accentColor: Color { ... }
      var mainColor: Color { Color(rawValue) }
      var name: String { rawValue.capitalized }
  }
  ```

## 6.2 主题色在 UI 中的应用
- SyncUp、会议等数据结构均有 theme 字段，页面渲染时动态读取主题色。
- 例：
  ```swift
  RoundedRectangle(cornerRadius: 16)
      .fill(store.syncUp.theme.mainColor)
  .foregroundColor(store.syncUp.theme.accentColor)
  ```
- 列表卡片、表单、会议详情等均根据 theme 渲染不同配色。

## 6.3 动态响应主题变化
- 主题色可在表单中通过 ThemePicker 组件动态切换，UI 实时响应。
- 例：
  ```swift
  ThemePicker(selection: $store.syncUp.theme)
  ```
- 主题切换后，相关 UI 组件会自动刷新，保持风格一致。

## 6.4 主题扩展与维护
- 新增主题只需在 Theme 枚举中添加 case，并配置主色、强调色。
- 主题色资源集中在 Assets.xcassets/Themes 目录，便于统一管理和扩展。

---

# 08 表单体验

## 8.1 动态增删表单项
- SyncUpForm 支持动态增删参会人，保证至少有一位参会人。
- 新增参会人通过 `addAttendeeButtonTapped` Action 实现，删除参会人通过 `deleteAttendees(atOffsets:)` Action 实现。
- 例：
  ```swift
  case .addAttendeeButtonTapped:
      let attendee = Attendee(id: Attendee.ID(uuid()))
      state.syncUp.attendees.append(attendee)
      state.focus = .attendee(attendee.id)
      return .none

  case let .deleteAttendees(atOffsets: indices):
      state.syncUp.attendees.remove(atOffsets: indices)
      if state.syncUp.attendees.isEmpty {
          state.syncUp.attendees.append(Attendee(id: Attendee.ID(uuid())))
      }
      ...
  ```

## 8.2 表单聚焦与交互体验
- 使用 State 的 focus 字段和 SwiftUI 的 @FocusState 实现输入框自动聚焦。
- 新增参会人后自动聚焦到新输入框，提升用户体验。
- 例：
  ```swift
  var focus: Field? = .title // State 字段
  .focused($focus, equals: .attendee(attendee.id))
  .bind($store.focus, to: $focus)
  ```

## 8.3 表单校验与默认值
- 删除所有参会人后自动补充一个空参会人，保证表单始终有效。
- 表单初始时若无参会人，自动添加一个默认参会人。
- 例：
  ```swift
  if self.syncUp.attendees.isEmpty {
      @Dependency(\.uuid) var uuid
      self.syncUp.attendees.append(Attendee(id: Attendee.ID(uuid())))
  }
  ```

## 8.4 表单与 State 绑定
- 表单所有输入项均与 State 绑定，采用 TCA 的双向绑定机制（如 $store.syncUp.title）。
- 例：
  ```swift
  TextField("Title", text: $store.syncUp.title)
  Slider(value: $store.syncUp.duration.minutes, in: 5...30, step: 1)
  ForEach($store.syncUp.attendees) { $attendee in
      TextField("Name", text: $attendee.name)
  }
  ```

---

# 09 权限与弹窗

## 9.1 权限检测流程
- 语音识别等功能在使用前会检测权限，通过依赖注入的方式调用权限检测方法。
- 例：
  ```swift
  @Dependency(\.speechClient.authorizationStatus) var authorizationStatus
  ...
  switch authorizationStatus() {
      case .notDetermined, .authorized:
          ...
      case .denied:
          state.destination = .alert(.speechRecognitionDenied)
          return .none
      case .restricted:
          state.destination = .alert(.speechRecognitionRestricted)
          return .none
      @unknown default:
          return .none
  }
  ```

## 9.2 弹窗状态管理
- 弹窗状态统一用 `@Presents var destination: Destination.State?` 或 `@Presents var alert: AlertState<Action.Alert>?` 管理。
- 弹窗内容和按钮通过 AlertState 配置，支持多按钮、消息、回调等。
- 例：
  ```swift
  @Presents var alert: AlertState<Action.Alert>?
  ...
  state.alert = .endMeeting(isDiscardable: true)
  ...
  .alert($store.scope(state: \.alert, action: \.alert))
  ```

## 9.3 全局与局部弹窗区分
- 当前工程所有弹窗均为局部弹窗，绑定在具体页面的 State 上。
- 如需全局弹窗，可在顶层 State/Action 增加全局 alert 字段。

## 9.4 弹窗与 Action 交互
- 弹窗按钮点击后会触发对应的 Action，Reducer 根据 Action 处理业务逻辑。
- 例：
  ```swift
  case .alert(.presented(.confirmSave)):
      state.$syncUp.withLock { $0.insert(transcript: state.transcript) }
      return .run { _ in await dismiss() }
  ```
- 弹窗的 Action 通过 PresentationAction/Alert 枚举细分，便于管理。

---

# 10 依赖注入与测试

## 10.1 @Dependency 注入用法
- 工程大量使用 TCA 的 @Dependency 属性进行依赖注入，便于解耦和测试。
- 常见依赖如：时间（\.date.now）、UUID（\.uuid）、语音识别（\.speechClient）、页面关闭（\.dismiss）、打开设置（\.openSettings）等。
- 例：
  ```swift
  @Dependency(\.date.now) var now
  @Dependency(\.uuid) var uuid
  @Dependency(\.speechClient) var speechClient
  @Dependency(\.dismiss) var dismiss
  @Dependency(\.openSettings) var openSettings
  ```

## 10.2 依赖的 mock 与测试
- 依赖注入机制便于在单元测试和 UI 测试中注入 mock 实现。
- 例如，测试时可将文件存储替换为内存存储，或将语音识别替换为模拟实现。
- 具体 mock 实现未在当前工程代码中详细体现，后续可补充。

## 10.3 UI 测试与 inMemory 存储
- 工程支持 UI 测试时切换为 inMemory 存储，避免文件读写干扰。
- 例：
  ```swift
  if ProcessInfo.processInfo.environment["UITesting"] == "true" {
      $0.defaultFileStorage = .inMemory
  }
  ```
- 这样可保证 UI 测试的独立性和可重复性。

## 10.4 依赖注入的扩展性
- 新增依赖只需在 Reducer 中通过 @Dependency 声明即可，无需修改全局配置。
- 依赖注入机制支持多环境切换（如生产、测试、mock），便于扩展和维护。

---

# 11 代码注释与文档

## 11.1 注释风格与规范
- 工程采用中文注释，注释风格为上方或旁注，覆盖结构体、变量、方法和主要业务逻辑。
- 注释内容详细，说明每个字段、方法、业务流程的作用和设计思路。
- 例：
  ```swift
  // 应用主 Reducer，负责整体业务逻辑
  @Reducer
  struct AppFeature { ... }

  // 路由路径枚举，管理页面跳转
  @Reducer
  enum Path { ... }

  // 详情页状态
  struct State: Equatable { ... }
  ```

## 11.2 关键业务流程注释
- 复杂业务流程、Reducer 主体、核心分支等均有详细注释，便于理解和维护。
- 例：
  ```swift
  // Reducer 主体
  var body: some ReducerOf<Self> {
      // 列表页作用域
      Scope(state: \.syncUpsList, action: \.syncUpsList) {
          SyncUpsList()
      }
      // 处理主业务逻辑
      Reduce { state, action in
          ...
      }
      // 路由栈 forEach 处理
      .forEach(\.path, action: \.path)
  }
  ```

## 11.3 工程内文档管理
- 工程根目录和技术点参考目录下有《工程阅读总结.md》《技术点参考/*.md》等文档，系统梳理架构、导航、状态管理、表单体验等关键技术点。
- 文档内容均基于实际代码提取，便于新成员查阅和团队知识沉淀。

