// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		F06CB8342E0BD35800FABF9F /* ComposableArchitecture in Frameworks */ = {isa = PBXBuildFile; productRef = F06CB8332E0BD35800FABF9F /* ComposableArchitecture */; };
		F084A0E42E0D361400E037AF /* LogCore in Frameworks */ = {isa = PBXBuildFile; productRef = F084A0E32E0D361400E037AF /* LogCore */; };
		F084A0E62E0D362200E037AF /* CacheCore in Frameworks */ = {isa = PBXBuildFile; productRef = F084A0E52E0D362200E037AF /* CacheCore */; };
		F084A0E82E0D362600E037AF /* DataRepositoryCore in Frameworks */ = {isa = PBXBuildFile; productRef = F084A0E72E0D362600E037AF /* DataRepositoryCore */; };
		F084A0EA2E0D362B00E037AF /* ErrorCore in Frameworks */ = {isa = PBXBuildFile; productRef = F084A0E92E0D362B00E037AF /* ErrorCore */; };
		F084A0EC2E0D362F00E037AF /* ImageCore in Frameworks */ = {isa = PBXBuildFile; productRef = F084A0EB2E0D362F00E037AF /* ImageCore */; };
		F084A0EE2E0D363400E037AF /* ModelCore in Frameworks */ = {isa = PBXBuildFile; productRef = F084A0ED2E0D363400E037AF /* ModelCore */; };
		F084A0F02E0D363800E037AF /* NetworkCore in Frameworks */ = {isa = PBXBuildFile; productRef = F084A0EF2E0D363800E037AF /* NetworkCore */; };
		F084A0F22E0D363E00E037AF /* PermissionCore in Frameworks */ = {isa = PBXBuildFile; productRef = F084A0F12E0D363E00E037AF /* PermissionCore */; };
		F084A10D2E14DCBA00E037AF /* SwiftUIPager in Frameworks */ = {isa = PBXBuildFile; productRef = F084A10C2E14DCBA00E037AF /* SwiftUIPager */; };
		F0F5E29A2E16843A0023DB7C /* VisualEffectView in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2992E16843A0023DB7C /* VisualEffectView */; };
		F0F5E2AA2E20A0630023DB7C /* BluetoothPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2A92E20A0630023DB7C /* BluetoothPermission */; };
		F0F5E2AC2E20A0630023DB7C /* CalendarPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2AB2E20A0630023DB7C /* CalendarPermission */; };
		F0F5E2AE2E20A0630023DB7C /* CameraPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2AD2E20A0630023DB7C /* CameraPermission */; };
		F0F5E2B02E20A0630023DB7C /* ContactsPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2AF2E20A0630023DB7C /* ContactsPermission */; };
		F0F5E2B22E20A0630023DB7C /* FaceIDPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2B12E20A0630023DB7C /* FaceIDPermission */; };
		F0F5E2B42E20A0630023DB7C /* HealthPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2B32E20A0630023DB7C /* HealthPermission */; };
		F0F5E2B62E20A0630023DB7C /* LocationPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2B52E20A0630023DB7C /* LocationPermission */; };
		F0F5E2B82E20A0630023DB7C /* MediaLibraryPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2B72E20A0630023DB7C /* MediaLibraryPermission */; };
		F0F5E2BA2E20A0630023DB7C /* MicrophonePermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2B92E20A0630023DB7C /* MicrophonePermission */; };
		F0F5E2BC2E20A0630023DB7C /* MotionPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2BB2E20A0630023DB7C /* MotionPermission */; };
		F0F5E2BE2E20A0630023DB7C /* NotificationPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2BD2E20A0630023DB7C /* NotificationPermission */; };
		F0F5E2C02E20A0630023DB7C /* PhotoLibraryPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2BF2E20A0630023DB7C /* PhotoLibraryPermission */; };
		F0F5E2C22E20A0630023DB7C /* RemindersPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2C12E20A0630023DB7C /* RemindersPermission */; };
		F0F5E2C42E20A0630023DB7C /* SiriPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2C32E20A0630023DB7C /* SiriPermission */; };
		F0F5E2C62E20A0630023DB7C /* SpeechRecognizerPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2C52E20A0630023DB7C /* SpeechRecognizerPermission */; };
		F0F5E2C82E20A0630023DB7C /* TrackingPermission in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2C72E20A0630023DB7C /* TrackingPermission */; };
		F0F5E2DD2E24E1E90023DB7C /* Refresh in Frameworks */ = {isa = PBXBuildFile; productRef = F0F5E2DC2E24E1E90023DB7C /* Refresh */; };
		F0F5E2FD2E2A45310023DB7C /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F0F5E2FC2E2A45310023DB7C /* Launch Screen.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		F029A9272DFFE79100C03157 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F029A9112DFFE79100C03157 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F029A9182DFFE79100C03157;
			remoteInfo = gouqi;
		};
		F029A9312DFFE79100C03157 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F029A9112DFFE79100C03157 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F029A9182DFFE79100C03157;
			remoteInfo = gouqi;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		F029A9192DFFE79100C03157 /* gouqi.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = gouqi.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F029A9262DFFE79100C03157 /* gouqiTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = gouqiTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F029A9302DFFE79100C03157 /* gouqiUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = gouqiUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F0F5E2FC2E2A45310023DB7C /* Launch Screen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "Launch Screen.storyboard"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		F0F5E2A42E1FB28B0023DB7C /* Exceptions for "gouqi" folder in "gouqi" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = F029A9182DFFE79100C03157 /* gouqi */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		F029A91B2DFFE79100C03157 /* gouqi */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				F0F5E2A42E1FB28B0023DB7C /* Exceptions for "gouqi" folder in "gouqi" target */,
			);
			path = gouqi;
			sourceTree = "<group>";
		};
		F029A9292DFFE79100C03157 /* gouqiTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = gouqiTests;
			sourceTree = "<group>";
		};
		F029A9332DFFE79100C03157 /* gouqiUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = gouqiUITests;
			sourceTree = "<group>";
		};
		F029A94A2DFFE92E00C03157 /* Modules */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Modules;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		F029A9162DFFE79100C03157 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				F0F5E2B82E20A0630023DB7C /* MediaLibraryPermission in Frameworks */,
				F084A0EE2E0D363400E037AF /* ModelCore in Frameworks */,
				F0F5E2AC2E20A0630023DB7C /* CalendarPermission in Frameworks */,
				F0F5E2C62E20A0630023DB7C /* SpeechRecognizerPermission in Frameworks */,
				F0F5E2B62E20A0630023DB7C /* LocationPermission in Frameworks */,
				F084A0F22E0D363E00E037AF /* PermissionCore in Frameworks */,
				F0F5E2BC2E20A0630023DB7C /* MotionPermission in Frameworks */,
				F084A10D2E14DCBA00E037AF /* SwiftUIPager in Frameworks */,
				F084A0E42E0D361400E037AF /* LogCore in Frameworks */,
				F0F5E29A2E16843A0023DB7C /* VisualEffectView in Frameworks */,
				F0F5E2B22E20A0630023DB7C /* FaceIDPermission in Frameworks */,
				F0F5E2AA2E20A0630023DB7C /* BluetoothPermission in Frameworks */,
				F084A0E82E0D362600E037AF /* DataRepositoryCore in Frameworks */,
				F0F5E2AE2E20A0630023DB7C /* CameraPermission in Frameworks */,
				F06CB8342E0BD35800FABF9F /* ComposableArchitecture in Frameworks */,
				F0F5E2C02E20A0630023DB7C /* PhotoLibraryPermission in Frameworks */,
				F0F5E2B02E20A0630023DB7C /* ContactsPermission in Frameworks */,
				F084A0EC2E0D362F00E037AF /* ImageCore in Frameworks */,
				F084A0EA2E0D362B00E037AF /* ErrorCore in Frameworks */,
				F0F5E2C22E20A0630023DB7C /* RemindersPermission in Frameworks */,
				F084A0F02E0D363800E037AF /* NetworkCore in Frameworks */,
				F084A0E62E0D362200E037AF /* CacheCore in Frameworks */,
				F0F5E2BA2E20A0630023DB7C /* MicrophonePermission in Frameworks */,
				F0F5E2DD2E24E1E90023DB7C /* Refresh in Frameworks */,
				F0F5E2BE2E20A0630023DB7C /* NotificationPermission in Frameworks */,
				F0F5E2C42E20A0630023DB7C /* SiriPermission in Frameworks */,
				F0F5E2C82E20A0630023DB7C /* TrackingPermission in Frameworks */,
				F0F5E2B42E20A0630023DB7C /* HealthPermission in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F029A9232DFFE79100C03157 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F029A92D2DFFE79100C03157 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		F029A9102DFFE79100C03157 = {
			isa = PBXGroup;
			children = (
				F0F5E2FC2E2A45310023DB7C /* Launch Screen.storyboard */,
				F029A94A2DFFE92E00C03157 /* Modules */,
				F029A91B2DFFE79100C03157 /* gouqi */,
				F029A9292DFFE79100C03157 /* gouqiTests */,
				F029A9332DFFE79100C03157 /* gouqiUITests */,
				F084A0E22E0D361400E037AF /* Frameworks */,
				F029A91A2DFFE79100C03157 /* Products */,
			);
			sourceTree = "<group>";
		};
		F029A91A2DFFE79100C03157 /* Products */ = {
			isa = PBXGroup;
			children = (
				F029A9192DFFE79100C03157 /* gouqi.app */,
				F029A9262DFFE79100C03157 /* gouqiTests.xctest */,
				F029A9302DFFE79100C03157 /* gouqiUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F084A0E22E0D361400E037AF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F029A9182DFFE79100C03157 /* gouqi */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F029A93A2DFFE79100C03157 /* Build configuration list for PBXNativeTarget "gouqi" */;
			buildPhases = (
				F029A9152DFFE79100C03157 /* Sources */,
				F029A9162DFFE79100C03157 /* Frameworks */,
				F029A9172DFFE79100C03157 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				F029A91B2DFFE79100C03157 /* gouqi */,
			);
			name = gouqi;
			packageProductDependencies = (
				F06CB8332E0BD35800FABF9F /* ComposableArchitecture */,
				F084A0E32E0D361400E037AF /* LogCore */,
				F084A0E52E0D362200E037AF /* CacheCore */,
				F084A0E72E0D362600E037AF /* DataRepositoryCore */,
				F084A0E92E0D362B00E037AF /* ErrorCore */,
				F084A0EB2E0D362F00E037AF /* ImageCore */,
				F084A0ED2E0D363400E037AF /* ModelCore */,
				F084A0EF2E0D363800E037AF /* NetworkCore */,
				F084A0F12E0D363E00E037AF /* PermissionCore */,
				F084A10C2E14DCBA00E037AF /* SwiftUIPager */,
				F0F5E2992E16843A0023DB7C /* VisualEffectView */,
				F0F5E2A92E20A0630023DB7C /* BluetoothPermission */,
				F0F5E2AB2E20A0630023DB7C /* CalendarPermission */,
				F0F5E2AD2E20A0630023DB7C /* CameraPermission */,
				F0F5E2AF2E20A0630023DB7C /* ContactsPermission */,
				F0F5E2B12E20A0630023DB7C /* FaceIDPermission */,
				F0F5E2B32E20A0630023DB7C /* HealthPermission */,
				F0F5E2B52E20A0630023DB7C /* LocationPermission */,
				F0F5E2B72E20A0630023DB7C /* MediaLibraryPermission */,
				F0F5E2B92E20A0630023DB7C /* MicrophonePermission */,
				F0F5E2BB2E20A0630023DB7C /* MotionPermission */,
				F0F5E2BD2E20A0630023DB7C /* NotificationPermission */,
				F0F5E2BF2E20A0630023DB7C /* PhotoLibraryPermission */,
				F0F5E2C12E20A0630023DB7C /* RemindersPermission */,
				F0F5E2C32E20A0630023DB7C /* SiriPermission */,
				F0F5E2C52E20A0630023DB7C /* SpeechRecognizerPermission */,
				F0F5E2C72E20A0630023DB7C /* TrackingPermission */,
				F0F5E2DC2E24E1E90023DB7C /* Refresh */,
			);
			productName = gouqi;
			productReference = F029A9192DFFE79100C03157 /* gouqi.app */;
			productType = "com.apple.product-type.application";
		};
		F029A9252DFFE79100C03157 /* gouqiTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F029A93D2DFFE79100C03157 /* Build configuration list for PBXNativeTarget "gouqiTests" */;
			buildPhases = (
				F029A9222DFFE79100C03157 /* Sources */,
				F029A9232DFFE79100C03157 /* Frameworks */,
				F029A9242DFFE79100C03157 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F029A9282DFFE79100C03157 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				F029A9292DFFE79100C03157 /* gouqiTests */,
			);
			name = gouqiTests;
			packageProductDependencies = (
			);
			productName = gouqiTests;
			productReference = F029A9262DFFE79100C03157 /* gouqiTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		F029A92F2DFFE79100C03157 /* gouqiUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F029A9402DFFE79100C03157 /* Build configuration list for PBXNativeTarget "gouqiUITests" */;
			buildPhases = (
				F029A92C2DFFE79100C03157 /* Sources */,
				F029A92D2DFFE79100C03157 /* Frameworks */,
				F029A92E2DFFE79100C03157 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F029A9322DFFE79100C03157 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				F029A9332DFFE79100C03157 /* gouqiUITests */,
			);
			name = gouqiUITests;
			packageProductDependencies = (
			);
			productName = gouqiUITests;
			productReference = F029A9302DFFE79100C03157 /* gouqiUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F029A9112DFFE79100C03157 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					F029A9182DFFE79100C03157 = {
						CreatedOnToolsVersion = 16.4;
					};
					F029A9252DFFE79100C03157 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = F029A9182DFFE79100C03157;
					};
					F029A92F2DFFE79100C03157 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = F029A9182DFFE79100C03157;
					};
				};
			};
			buildConfigurationList = F029A9142DFFE79100C03157 /* Build configuration list for PBXProject "gouqi" */;
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				ja,
				"zh-Hant",
				"zh-Hans",
				Base,
			);
			mainGroup = F029A9102DFFE79100C03157;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				F0C701472E02A03F0025ADF3 /* XCLocalSwiftPackageReference "Modules/NetworkCore" */,
				F0C701482E02A07B0025ADF3 /* XCLocalSwiftPackageReference "Modules/LogCore" */,
				F0C701492E02A0B80025ADF3 /* XCLocalSwiftPackageReference "Modules/CacheCore" */,
				F0C7014A2E02A0CE0025ADF3 /* XCLocalSwiftPackageReference "Modules/ErrorCore" */,
				F0C7014B2E02A0E50025ADF3 /* XCLocalSwiftPackageReference "Modules/ImageCore" */,
				F0C7014C2E02A1020025ADF3 /* XCLocalSwiftPackageReference "Modules/ModelCore" */,
				F0C7014D2E02A1180025ADF3 /* XCLocalSwiftPackageReference "Modules/PermissionCore" */,
				F06CB82B2E0BCC2900FABF9F /* XCRemoteSwiftPackageReference "swift-composable-architecture" */,
				F084A10B2E14D72200E037AF /* XCRemoteSwiftPackageReference "SwiftUIPager" */,
				F0F5E2982E16842F0023DB7C /* XCRemoteSwiftPackageReference "VisualEffectView" */,
				F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */,
				F0F5E2DB2E24E1DE0023DB7C /* XCRemoteSwiftPackageReference "Refresh" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = F029A91A2DFFE79100C03157 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F029A9182DFFE79100C03157 /* gouqi */,
				F029A9252DFFE79100C03157 /* gouqiTests */,
				F029A92F2DFFE79100C03157 /* gouqiUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F029A9172DFFE79100C03157 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F0F5E2FD2E2A45310023DB7C /* Launch Screen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F029A9242DFFE79100C03157 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F029A92E2DFFE79100C03157 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F029A9152DFFE79100C03157 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F029A9222DFFE79100C03157 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F029A92C2DFFE79100C03157 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		F029A9282DFFE79100C03157 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F029A9182DFFE79100C03157 /* gouqi */;
			targetProxy = F029A9272DFFE79100C03157 /* PBXContainerItemProxy */;
		};
		F029A9322DFFE79100C03157 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F029A9182DFFE79100C03157 /* gouqi */;
			targetProxy = F029A9312DFFE79100C03157 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		F029A9382DFFE79100C03157 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F029A9392DFFE79100C03157 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F029A93B2DFFE79100C03157 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = gouqi/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ubest.sz.gouqi;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		F029A93C2DFFE79100C03157 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = gouqi/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ubest.sz.gouqi;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		F029A93E2DFFE79100C03157 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ubest.sz.gouqiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/gouqi.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/gouqi";
			};
			name = Debug;
		};
		F029A93F2DFFE79100C03157 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ubest.sz.gouqiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/gouqi.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/gouqi";
			};
			name = Release;
		};
		F029A9412DFFE79100C03157 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ubest.sz.gouqiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = gouqi;
			};
			name = Debug;
		};
		F029A9422DFFE79100C03157 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ubest.sz.gouqiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = gouqi;
			};
			name = Release;
		};
		F029A9592DFFF18D00C03157 /* Debug-Testing */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited) TESTING";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = "Debug-Testing";
		};
		F029A95A2DFFF18D00C03157 /* Debug-Testing */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = gouqi/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = UIInterfaceOrientationPortrait;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ubest.sz.gouqi;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug-Testing";
		};
		F029A95B2DFFF18D00C03157 /* Debug-Testing */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ubest.sz.gouqiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/gouqi.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/gouqi";
			};
			name = "Debug-Testing";
		};
		F029A95C2DFFF18D00C03157 /* Debug-Testing */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PSDUZFY2Y5;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ubest.sz.gouqiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = gouqi;
			};
			name = "Debug-Testing";
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F029A9142DFFE79100C03157 /* Build configuration list for PBXProject "gouqi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F029A9382DFFE79100C03157 /* Debug */,
				F029A9592DFFF18D00C03157 /* Debug-Testing */,
				F029A9392DFFE79100C03157 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = "Debug-Testing";
		};
		F029A93A2DFFE79100C03157 /* Build configuration list for PBXNativeTarget "gouqi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F029A93B2DFFE79100C03157 /* Debug */,
				F029A95A2DFFF18D00C03157 /* Debug-Testing */,
				F029A93C2DFFE79100C03157 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = "Debug-Testing";
		};
		F029A93D2DFFE79100C03157 /* Build configuration list for PBXNativeTarget "gouqiTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F029A93E2DFFE79100C03157 /* Debug */,
				F029A95B2DFFF18D00C03157 /* Debug-Testing */,
				F029A93F2DFFE79100C03157 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = "Debug-Testing";
		};
		F029A9402DFFE79100C03157 /* Build configuration list for PBXNativeTarget "gouqiUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F029A9412DFFE79100C03157 /* Debug */,
				F029A95C2DFFF18D00C03157 /* Debug-Testing */,
				F029A9422DFFE79100C03157 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = "Debug-Testing";
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		F0C701472E02A03F0025ADF3 /* XCLocalSwiftPackageReference "Modules/NetworkCore" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = Modules/NetworkCore;
		};
		F0C701482E02A07B0025ADF3 /* XCLocalSwiftPackageReference "Modules/LogCore" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = Modules/LogCore;
		};
		F0C701492E02A0B80025ADF3 /* XCLocalSwiftPackageReference "Modules/CacheCore" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = Modules/CacheCore;
		};
		F0C7014A2E02A0CE0025ADF3 /* XCLocalSwiftPackageReference "Modules/ErrorCore" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = Modules/ErrorCore;
		};
		F0C7014B2E02A0E50025ADF3 /* XCLocalSwiftPackageReference "Modules/ImageCore" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = Modules/ImageCore;
		};
		F0C7014C2E02A1020025ADF3 /* XCLocalSwiftPackageReference "Modules/ModelCore" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = Modules/ModelCore;
		};
		F0C7014D2E02A1180025ADF3 /* XCLocalSwiftPackageReference "Modules/PermissionCore" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = Modules/PermissionCore;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		F06CB82B2E0BCC2900FABF9F /* XCRemoteSwiftPackageReference "swift-composable-architecture" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/pointfreeco/swift-composable-architecture.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.20.2;
			};
		};
		F084A10B2E14D72200E037AF /* XCRemoteSwiftPackageReference "SwiftUIPager" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/fermoya/SwiftUIPager.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.0;
			};
		};
		F0F5E2982E16842F0023DB7C /* XCRemoteSwiftPackageReference "VisualEffectView" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/efremidze/VisualEffectView.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.3;
			};
		};
		F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sparrowcode/PermissionsKit.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.1.0;
			};
		};
		F0F5E2DB2E24E1DE0023DB7C /* XCRemoteSwiftPackageReference "Refresh" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/wxxsw/Refresh";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.2.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		F06CB8332E0BD35800FABF9F /* ComposableArchitecture */ = {
			isa = XCSwiftPackageProductDependency;
			package = F06CB82B2E0BCC2900FABF9F /* XCRemoteSwiftPackageReference "swift-composable-architecture" */;
			productName = ComposableArchitecture;
		};
		F084A0E32E0D361400E037AF /* LogCore */ = {
			isa = XCSwiftPackageProductDependency;
			productName = LogCore;
		};
		F084A0E52E0D362200E037AF /* CacheCore */ = {
			isa = XCSwiftPackageProductDependency;
			productName = CacheCore;
		};
		F084A0E72E0D362600E037AF /* DataRepositoryCore */ = {
			isa = XCSwiftPackageProductDependency;
			productName = DataRepositoryCore;
		};
		F084A0E92E0D362B00E037AF /* ErrorCore */ = {
			isa = XCSwiftPackageProductDependency;
			productName = ErrorCore;
		};
		F084A0EB2E0D362F00E037AF /* ImageCore */ = {
			isa = XCSwiftPackageProductDependency;
			productName = ImageCore;
		};
		F084A0ED2E0D363400E037AF /* ModelCore */ = {
			isa = XCSwiftPackageProductDependency;
			productName = ModelCore;
		};
		F084A0EF2E0D363800E037AF /* NetworkCore */ = {
			isa = XCSwiftPackageProductDependency;
			productName = NetworkCore;
		};
		F084A0F12E0D363E00E037AF /* PermissionCore */ = {
			isa = XCSwiftPackageProductDependency;
			productName = PermissionCore;
		};
		F084A10C2E14DCBA00E037AF /* SwiftUIPager */ = {
			isa = XCSwiftPackageProductDependency;
			package = F084A10B2E14D72200E037AF /* XCRemoteSwiftPackageReference "SwiftUIPager" */;
			productName = SwiftUIPager;
		};
		F0F5E2992E16843A0023DB7C /* VisualEffectView */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2982E16842F0023DB7C /* XCRemoteSwiftPackageReference "VisualEffectView" */;
			productName = VisualEffectView;
		};
		F0F5E2A92E20A0630023DB7C /* BluetoothPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = BluetoothPermission;
		};
		F0F5E2AB2E20A0630023DB7C /* CalendarPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = CalendarPermission;
		};
		F0F5E2AD2E20A0630023DB7C /* CameraPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = CameraPermission;
		};
		F0F5E2AF2E20A0630023DB7C /* ContactsPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = ContactsPermission;
		};
		F0F5E2B12E20A0630023DB7C /* FaceIDPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = FaceIDPermission;
		};
		F0F5E2B32E20A0630023DB7C /* HealthPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = HealthPermission;
		};
		F0F5E2B52E20A0630023DB7C /* LocationPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = LocationPermission;
		};
		F0F5E2B72E20A0630023DB7C /* MediaLibraryPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = MediaLibraryPermission;
		};
		F0F5E2B92E20A0630023DB7C /* MicrophonePermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = MicrophonePermission;
		};
		F0F5E2BB2E20A0630023DB7C /* MotionPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = MotionPermission;
		};
		F0F5E2BD2E20A0630023DB7C /* NotificationPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = NotificationPermission;
		};
		F0F5E2BF2E20A0630023DB7C /* PhotoLibraryPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = PhotoLibraryPermission;
		};
		F0F5E2C12E20A0630023DB7C /* RemindersPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = RemindersPermission;
		};
		F0F5E2C32E20A0630023DB7C /* SiriPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = SiriPermission;
		};
		F0F5E2C52E20A0630023DB7C /* SpeechRecognizerPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = SpeechRecognizerPermission;
		};
		F0F5E2C72E20A0630023DB7C /* TrackingPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2A82E20A0480023DB7C /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = TrackingPermission;
		};
		F0F5E2DC2E24E1E90023DB7C /* Refresh */ = {
			isa = XCSwiftPackageProductDependency;
			package = F0F5E2DB2E24E1DE0023DB7C /* XCRemoteSwiftPackageReference "Refresh" */;
			productName = Refresh;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = F029A9112DFFE79100C03157 /* Project object */;
}
