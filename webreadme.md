## 页面文件说明（src/pages）

以下为本项目主要页面文件的功能与界面简要说明：

### 1. [Index.vue](src/pages/Index.vue)
- **作用**：主页面布局，包含左侧导航栏（SideBar）和右侧主内容区。主内容区通过 `<router-view>` 动态渲染各个子页面，并用 `<keep-alive>` 缓存部分组件，提升切换体验。
- **界面**：左侧为导航栏，右侧为各功能页面的主内容。

### 2. [GoogleCallback.vue](src/pages/GoogleCallback.vue)
- **作用**：Google OAuth 登录回调处理页。负责处理 Google 登录授权后的回调逻辑，包括处理中、成功、失败三种状态，并根据结果跳转或提示。
- **界面**：显示"正在处理登录"、"登录成功"或"登录失败"等状态提示，带有返回和重试按钮。

### 3. [NotFound.vue](src/pages/NotFound.vue)
- **作用**：404 未找到页面。当用户访问不存在的路由时显示。
- **界面**：展示一张 404 图片和"返回上一页"按钮。

### 4. [character/CharacterPage.vue](src/pages/character/CharacterPage.vue)
- **作用**：角色详情页。展示单个角色的详细信息，包括头像、昵称、签名、帖子数、粉丝数等，并可进行消息、语音、收藏等操作。
- **界面**：上方为角色信息和操作按钮，下方为该角色相关的帖子瀑布流。

### 5. [chat/ChatPage.vue](src/pages/chat/ChatPage.vue)
- **作用**：聊天页面。与某个角色进行文字或语音聊天，顶部显示角色信息，可切换聊天模式。
- **界面**：左侧为角色信息，右侧为聊天内容区（支持语音和文字消息）。

### 6. [coin/BuyCoinView.vue](src/pages/coin/BuyCoinView.vue)
- **作用**：金币购买主页面。左侧为金币套餐选择，右侧为购买记录。
- **界面**：分为左右两栏，左侧选择金币套餐，右侧查看购买记录。

### 7. [collection/CollectionPage.vue](src/pages/collection/CollectionPage.vue)
- **作用**：收藏夹页面。展示用户收藏的角色列表，支持滚动加载、点击角色查看详情、发消息、语音等操作。
- **界面**：左侧为收藏角色列表，右侧/弹窗为角色详细信息。

### 8. [recommend/RecommendPage.vue](src/pages/recommend/RecommendPage.vue)
- **作用**：推荐页。展示推荐的帖子列表，支持搜索功能。
- **界面**：顶部为搜索栏，主区域为帖子瀑布流列表。

### 9. [featured/FeaturedPage.vue](src/pages/featured/FeaturedPage.vue)
- **作用**：精选页。展示精选角色列表，支持标签筛选、上拉加载更多、点击角色查看详情等功能。
- **界面**：顶部为搜索栏和标签筛选，主区域为角色卡片列表。

### 10. [create-character/CreateCharacterPage.vue](src/pages/create-character/CreateCharacterPage.vue)
- **作用**：创建角色页面。用户可填写昵称、性别、简介、风格、上传图片等信息，提交后创建新角色。
- **界面**：表单式页面，包含输入框、风格选择、图片上传、提交按钮等。

## 前端页面与接口功能对照表

以下为本项目各主要页面和功能所用到的API接口、请求方式、参数及其用途说明：

### 首页-推荐标签
- **功能**：推荐帖子列表、搜索帖子
- **页面/组件**：RecommendPage.vue / PostWaterfall.vue
- **接口**：
  - `/api/post/list` (GET) —— 推荐帖子分页列表
    - 参数：`page`，`pageSize`
  - `/api/post/search` (POST) —— 搜索帖子
    - 参数：`page`，body: `{ searchText }`
  - `/api/character/post/list` (GET) —— 角色下的帖子
    - 参数：`cid`

### 帖子详情
- **功能**：获取单个帖子详情
- **页面/组件**：PostDetail.vue
- **接口**：
  - `/api/post/info` (GET) —— 获取帖子详情
    - 参数：`pid`

### 角色详情
- **功能**：获取角色信息、角色下的帖子、收藏角色
- **页面/组件**：CharacterPage.vue
- **接口**：
  - `/api/character/info` (GET) —— 获取角色信息
    - 参数：`cid`
  - `/api/character/post/list` (GET) —— 角色下的帖子
    - 参数：`cid`
  - `/api/character/collect` (POST) —— 收藏/取消收藏角色
    - 参数：`cid`，`collect`（是否收藏）

### 收藏夹
- **功能**：获取收藏角色列表
- **页面/组件**：CollectionPage.vue
- **接口**：
  - `/api/character/collect/list` (GET) —— 收藏角色分页列表
    - 参数：`page`，`pageSize`

### 聊天
- **功能**：获取会话列表、获取聊天历史、发送消息、获取clientId
- **页面/组件**：SessionListPage.vue、ChatMsgView.vue
- **接口**：
  - `/api/session/list` (GET) —— 会话分页列表
    - 参数：`page`，`pageSize`
  - `/api/chat/history` (GET) —— 获取聊天历史
    - 参数：`cid`，`anchorId`（可选）
  - `/api/chat/send` (POST) —— 发送消息
    - 参数：`cid`，`content`，`msgType`，`replyType`
  - `/api/chat/clientId` (GET) —— 获取clientId
    - 参数：`cid`

### 购买金币/支付
- **功能**：获取支付方式、发起支付、查询订单状态
- **页面/组件**：PayWayView.vue
- **接口**：
  - `/api/pay/method` (GET) —— 获取支付方式
  - `/api/pay/request` (POST) —— 发起支付
    - 参数：`payChannel`，`packAgeId`
  - `/api/pay/order/status` (GET) —— 查询订单状态
    - 参数：`orderNo`

### 创建角色/帖子
- **功能**：创建角色、创建帖子、获取消耗金币价格
- **页面/组件**：CreateCharacterPage.vue、CreatePostPage.vue
- **接口**：
  - `/api/character/create` (POST) —— 创建角色
    - 参数：`nickName`，`gender`，`introduction`，`aiStyle`，`personalImageurl`
  - `/api/character/consumePrice` (GET) —— 获取创建角色消耗金币
  - `/api/post/create` (POST) —— 创建帖子
    - 参数：`cid`，`content`

### Google 登录
- **功能**：Google OAuth 登录回调
- **页面/组件**：GoogleCallback.vue
- **接口**：
  - `/api/user/login/google` (POST) —— Google登录
    - 参数：`code`（Google回调code）

> 以上接口路径和参数以实际后端实现为准，部分接口可能通过userService等封装调用。具体细节可查阅各页面源码。

> 如需详细了解某个页面的具体实现或交互逻辑，可查阅对应 `.vue` 文件源码。