# gouqi 项目根界面（App 入口）TCA 架构说明

## 1. 设计目标
- 统一管理 App 的全局状态、路由、全局弹窗等。
- 采用 TCA（The Composable Architecture）分层、可组合的架构思想。
- 便于后续扩展和维护。

---

## 2. 路由与页面结构

### 2.1 路由枚举
用枚举管理一级页面（如启动页、登录页、主 Tab 页）：

```swift
enum AppRoute: Equatable {
    case launch
    case login
    case main
}
```

### 2.2 State 结构

```swift
struct AppState: Equatable {
    var route: AppRoute = .launch
    var launch: LaunchState = .init()
    var login: LoginState = .init()
    var main: MainState = .init()
    var globalAlert: AlertState<AppAction>?
    // 其他全局状态...
}
```

### 2.3 Action 结构

```swift
enum AppAction: Equatable {
    case routeChanged(AppRoute)
    case launch(LaunchAction)
    case login(LoginAction)
    case main(MainAction)
    case showAlert(AlertState<AppAction>)
    case dismissAlert
    // 其他全局 Action...
}
```

### 2.4 Reducer 结构

```swift
let appReducer = Reducer<AppState, AppAction, AppEnvironment>.combine(
    launchReducer.pullback(
        state: \.launch,
        action: /AppAction.launch,
        environment: { $0 }
    ),
    loginReducer.pullback(
        state: \.login,
        action: /AppAction.login,
        environment: { $0 }
    ),
    mainReducer.pullback(
        state: \.main,
        action: /AppAction.main,
        environment: { $0 }
    ),
    Reducer { state, action, environment in
        switch action {
        case .routeChanged(let route):
            state.route = route
            return .none
        case .showAlert(let alert):
            state.globalAlert = alert
            return .none
        case .dismissAlert:
            state.globalAlert = nil
            return .none
        default:
            return .none
        }
    }
)
```

### 2.5 View 结构

```swift
struct AppView: View {
    let store: Store<AppState, AppAction>
    var body: some View {
        WithViewStore(self.store) { viewStore in
            Group {
                switch viewStore.route {
                case .launch:
                    LaunchView(store: self.store.scope(state: \.launch, action: AppAction.launch))
                case .login:
                    LoginView(store: self.store.scope(state: \.login, action: AppAction.login))
                case .main:
                    MainView(store: self.store.scope(state: \.main, action: AppAction.main))
                }
            }
            .alert(
                self.store.scope(state: \.globalAlert),
                dismiss: .dismissAlert
            )
        }
    }
}
```

### 2.6 App 入口

```swift
@main
struct gouqiApp: App {
    var body: some Scene {
        WindowGroup {
            AppView(
                store: Store(
                    initialState: AppState(),
                    reducer: appReducer,
                    environment: AppEnvironment()
                )
            )
        }
    }
}
```

---

## 3. 架构结构图

```mermaid
graph TD
    A[AppState] --> B[route: AppRoute]
    A --> C[launch: LaunchState]
    A --> D[login: LoginState]
    A --> E[main: MainState]
    A --> F[globalAlert: AlertState]
    B --> B1[.launch]
    B --> B2[.login]
    B --> B3[.main]
```

---

## 4. 说明
- 根界面（AppView）根据 route 切换不同一级页面。
- 每个一级页面有自己的 State/Action/Reducer/View，互不干扰。
- 全局弹窗、Sheet 等也在 AppState 统一管理。
- 后续如需扩展（如加新页面、全局功能），只需在 AppState/AppAction 里加字段即可。

---

## 5. 扩展建议
- 各页面/模块的 State/Action/Reducer/View 可独立开发、测试、维护。
- 路由、弹窗、Sheet 等全局功能建议统一在 AppState/AppAction 管理。
- 采用 TCA 组合思想，便于后续扩展和团队协作。 