import ComposableArchitecture
import DataRepositoryCore
import LogCore
import PermissionCore

@Reducer
struct LaunchFeature {
    @ObservableState
    struct State: Equatable {
        var isLoading = true
        var permissionChecked = false // 新增字段，标记权限检测完成
    }
    

    enum Action {
        case onAppear
        case checkPermissions
        case permissionsChecked([AppPermission: PermissionStatus])
        case checkLoginStatus
        case delegate(Delegate)
        
        @CasePathable
        enum Delegate {
            case navigateToLogin
            case navigateToMain
        }
    }
    
    @Dependency(\.userRepository) var userRepository
    
    var body: some ReducerOf<Self> {
        Reduce { state, action in
            switch action {
            case .onAppear:
                // 直接进入后续流程，无需网络权限检测
                return .run { send in
                    await send(.checkLoginStatus)
                }
            case .checkPermissions:
                // 检查并请求所有权限
                return .run { send in
                    await withCheckedContinuation { continuation in
                        PermissionCore.checkAndRequestAllPermissions { results in
                            continuation.resume()
                            Task { await send(.permissionsChecked(results)) }
                        }
                    }
                }
            case let .permissionsChecked(results):
                // 权限检测完成后，继续登录状态检测
                state.permissionChecked = true
                return .run { send in
                    try await Task.sleep(for: .seconds(1)) // 可选延迟
                    await send(.checkLoginStatus)
                }
            case .checkLoginStatus:
                // 立即检测本地 token
                let isLoggedIn = userRepository.isLoggedIn()
                if isLoggedIn {
                    return .send(.delegate(.navigateToMain))
                } else {
                    return .send(.delegate(.navigateToMain))
                }
            case .delegate:
                return .none
            }
        }
    }
} 
