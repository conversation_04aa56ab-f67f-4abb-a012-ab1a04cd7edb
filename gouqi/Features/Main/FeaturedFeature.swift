import ComposableArchitecture
import SwiftUI
import LogCore
import DataRepositoryCore
import NetworkCore

// 导入导航上下文（从 AppFeature 中）


@Reducer
struct FeaturedFeature {
    public static var pageSize = 30 // 每页加载数量
    
    /// 标签项模型
    struct FeaturedTabItem: Identifiable, Equatable, Hashable {
        var id = UUID()
        let name: String
        let code: String
        
        static func == (lhs: FeaturedTabItem, rhs: FeaturedTabItem) -> Bool {
            lhs.code == rhs.code
        }
        
        func hash(into hasher: inout Hasher) {
            hasher.combine(code)
        }
    }
    
    @ObservableState
    struct State: Equatable {
        // 基本状态
        var searchText: String = ""
        var isRefreshing: Bool = false
        var isLoadingMore: Bool = false
        var hasMore: Bool = true
        var loadMoreFailed: Bool = false
        var items: [FeaturedCardItem] = []
        var pageState: PageState = .loading
        var toastMessage: String? = nil
        var currentPage: Int = 0

        // 内容加载状态（独立于PageStateView的loading状态）
        var isLoadingContent: Bool = false

        // 标签相关 - 修改为多选模式
        var tabs: [FeaturedTabItem] = []
        var isLoadingTabs: Bool = false
        var selectedTabIndices: Set<Int> = [0] // 改为多选，默认选中第一个（全部）
        var selectedTabCodes: Set<String> = ["all"] // 选中的标签代码集合

        // 标签懒加载相关
        var isLoadingMoreTabs: Bool = false
        var hasMoreTabs: Bool = true
        var tabsPage: Int = 0

        // 用户状态
        var isLoggedIn: Bool = false
        var userAvatarUrl: URL? = nil

        // 搜索框状态
        var isSearchBarFocused: Bool = false
        
        static func == (lhs: FeaturedFeature.State, rhs: FeaturedFeature.State) -> Bool {
            lhs.searchText == rhs.searchText &&
            lhs.isRefreshing == rhs.isRefreshing &&
            lhs.isLoadingMore == rhs.isLoadingMore &&
            lhs.hasMore == rhs.hasMore &&
            lhs.items == rhs.items &&
            lhs.pageState == rhs.pageState &&
            lhs.toastMessage == rhs.toastMessage &&
            lhs.loadMoreFailed == rhs.loadMoreFailed &&
            lhs.isLoggedIn == rhs.isLoggedIn &&
            lhs.userAvatarUrl == rhs.userAvatarUrl &&
            lhs.tabs == rhs.tabs &&
            lhs.selectedTabIndices == rhs.selectedTabIndices &&
            lhs.selectedTabCodes == rhs.selectedTabCodes &&
            lhs.isLoadingMoreTabs == rhs.isLoadingMoreTabs &&
            lhs.hasMoreTabs == rhs.hasMoreTabs &&
            lhs.tabsPage == rhs.tabsPage &&
            lhs.isSearchBarFocused == rhs.isSearchBarFocused &&
            lhs.isLoadingContent == rhs.isLoadingContent
        }
    }
    
    @Dependency(\.postRepository) var postRepository
    @Dependency(\.userRepository) var userRepository
    @Dependency(\.characterRepository) var characterRepository
    
    enum Action: BindableAction, Equatable {
        case binding(BindingAction<State>)
        
        case refresh(completion: (RefreshResult) -> Void)
        case loadMore(completion: (LoadMoreResult) -> Void)
        case cardTapped(FeaturedCardItem)
        case onAppear
        case onDisappear
        case setTabActive(Bool)
        case dismissToast
        
        // 标签相关动作
        case loadTabs
        case loadMoreTabs
        case tabToggled(Int) // 改为切换选中状态
        case tabSelected(Int) // 保留兼容性
        
        // 用户相关动作
        case loginButtonTapped
        case profileButtonTapped
        
        // 添加delegate用于向父组件通知
        case delegate(Delegate)
        
        // 私有 Action，仅在 Reducer 内部使用
        case _handleTabsLoaded([FeaturedTabItem])
        case _handleTabsLoadFailed(String)
        case _handleMoreTabsLoaded([FeaturedTabItem])
        case _handleRefreshResult(Result, completion: (RefreshResult) -> Void)
        case _handleLoadMoreResult(Result, completion: (LoadMoreResult) -> Void)
        
        @CasePathable
        enum Delegate: Equatable {
            case loginRequested(context: NavigationContext)
            case navigateToProfile
        }
        
        static func == (lhs: Action, rhs: Action) -> Bool {
            switch (lhs, rhs) {
            case (.binding(let a), .binding(let b)):
                return a == b
            case (.refresh, .refresh):
                return true
            case (.loadMore, .loadMore):
                return false
            case (.cardTapped(let a), .cardTapped(let b)):
                return a == b
            case (.onAppear, .onAppear):
                return true
            case (.onDisappear, .onDisappear):
                return true
            case (.setTabActive(let a), .setTabActive(let b)):
                return a == b
            case (.dismissToast, .dismissToast):
                return true
            case (.loadTabs, .loadTabs):
                return true
            case (.loadMoreTabs, .loadMoreTabs):
                return true
            case (.tabToggled(let a), .tabToggled(let b)):
                return a == b
            case (.tabSelected(let a), .tabSelected(let b)):
                return a == b
            case (.loginButtonTapped, .loginButtonTapped):
                return true
            case (.profileButtonTapped, .profileButtonTapped):
                return true
            case (.delegate(let a), .delegate(let b)):
                return a == b
            case (._handleTabsLoaded(let a), ._handleTabsLoaded(let b)):
                return a == b
            case (._handleTabsLoadFailed(let a), ._handleTabsLoadFailed(let b)):
                return a == b
            case (._handleMoreTabsLoaded(let a), ._handleMoreTabsLoaded(let b)):
                return a == b
            case (._handleRefreshResult, ._handleRefreshResult),
                 (._handleLoadMoreResult, ._handleLoadMoreResult):
                return false
            default:
                return false
            }
        }
    }
    
    enum Result: Equatable {
        case success([FeaturedCardItem])
        case failure(String)
    }
    
    // 获取标签列表（支持分页）
    private func fetchTabs(page: Int = 0, pageSize: Int = 10) async -> Swift.Result<[FeaturedTabItem], Error> {
        // 调用CharacterRepository获取真实标签数据
        guard let tagsResponse = await characterRepository.tags() else {
            // 如果API调用失败，返回错误
            return .failure(NSError(domain: "网络错误", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取标签列表失败"]))
        }

        // 将CharacterTag转换为FeaturedTabItem
        var allTabs = [FeaturedTabItem(name: "全部", code: "all")] // 只有在API成功时才添加"全部"标签
        let apiTabs = tagsResponse.map { tag in
            FeaturedTabItem(name: tag.name, code: tag.id)
        }
        allTabs.append(contentsOf: apiTabs)

        // 实现分页逻辑
        let startIndex = page * pageSize
        let endIndex = min(startIndex + pageSize, allTabs.count)

        if startIndex >= allTabs.count {
            return .success([])
        }

        return .success(Array(allTabs[startIndex..<endIndex]))
    }

    // 将Character模型转换为FeaturedCardItem
    private func convertToFeaturedCardItem(_ character: Character) -> FeaturedCardItem {
        return FeaturedCardItem(
            id: UUID(),
            coverURL: character.coverUrl ?? "",
            title: character.name ?? "未知角色",
            avatarURL: character.avatar ?? "",
            subtitle: "作者 \(character.createByUid ?? 0)",
            likeCount: character.collect ?? 0,
            isLiked: (character.userIsCollect ?? 0) == 1,
            commentCount: character.chat ?? 0,
            tags: character.tags?.components(separatedBy: ",") ?? []
        )
    }

    // 将PostInfo模型转换为FeaturedCardItem
    private func convertPostToFeaturedCardItem(_ post: PostInfo) -> FeaturedCardItem {
        // 从gallery字段提取封面图片URL
        let coverURL = extractImageURL(from: post.gallery) ?? ""

        return FeaturedCardItem(
            id: UUID(),
            coverURL: coverURL,
            title: post.title ?? "未知帖子",
            avatarURL: post.avatar ?? "",
            subtitle: post.name ?? "未知作者",
            likeCount: 0, // PostInfo中没有点赞数，设为0
            isLiked: (post.userIsCollect ?? 0) == 1,
            commentCount: 0, // PostInfo中没有评论数，设为0
            tags: []
        )
    }

    // 从gallery字符串中提取第一张图片URL
    private func extractImageURL(from gallery: String?) -> String? {
        guard let gallery = gallery, !gallery.isEmpty else { return nil }

        // 尝试解析JSON格式的gallery
        if let data = gallery.data(using: .utf8),
           let json = try? JSONSerialization.jsonObject(with: data) as? [String],
           let firstImage = json.first {
            return firstImage
        }

        // 如果不是JSON格式，直接返回原字符串
        return gallery
    }

    // 📋 统一的数据获取方法
    // 根据搜索文本和标签自动选择合适的API
    private func fetchData(pageNum: Int, searchText: String, tabCodes: Set<String>) async throws -> [FeaturedCardItem] {
        if searchText.isEmpty {
            return try await fetchFeaturedList(pageNum: pageNum, tabCodes: tabCodes)
        } else {
            return try await fetchSearchList(pageNum: pageNum, keyword: searchText)
        }
    }

    // 📋 获取精选列表数据（支持多标签选择）
    private func fetchFeaturedList(pageNum: Int, tabCodes: Set<String>) async throws -> [FeaturedCardItem] {
        // 🔀 API路由逻辑：根据选中的标签决定调用哪个API接口
        if tabCodes.contains("all") {
            // 📊 场景1：选中"全部"标签 -> 调用角色列表API
            // API: CharacterRepository.list()
            // 用途：获取所有角色的分页列表
            let params: [String: Any] = [
                "pageNum": "\(pageNum + 1)", // 转换：UI页码0 -> API页码1
                "pageSize": "\(Self.pageSize)" // 每页数量：30
            ]

            guard let response = await characterRepository.list(params: params) else {
                throw NSError(domain: "网络错误", code: -1, userInfo: [NSLocalizedDescriptionKey: "获取角色列表失败"])
            }

            // 📦 数据转换：Character -> FeaturedCardItem
            let characters = response.rows ?? []
            return characters.map { convertToFeaturedCardItem($0) }
        } else {
            // 🏷️ 场景2：选中具体标签 -> 调用标签搜索API
            // API: CharacterRepository.searchTag()
            // 用途：根据标签筛选角色，支持多标签组合
            let tags = Array(tabCodes).joined(separator: ",") // 多标签用逗号分隔，如："OC,Gay,Hot"
            let params: [String: Any] = [
                "page": "\(pageNum + 1)", // 转换：UI页码0 -> API页码1
                "tags": tags, // 标签字符串，如："OC,Gay,Hot"
                "keyword": "" // 空关键词，纯标签筛选
            ]
            guard let characters = await characterRepository.searchTag(params: params) else {
                throw NSError(domain: "网络错误", code: -1, userInfo: [NSLocalizedDescriptionKey: "搜索角色失败"])
            }

            // 📦 数据转换：Character -> FeaturedCardItem
            return characters.map { convertToFeaturedCardItem($0) }
        }
    }
    
    // 🔍 获取搜索列表数据（关键词搜索）
    // 参数：
    // - pageNum: 页码（从0开始，函数内部转换为API的1开始页码）
    // - keyword: 搜索关键词，用户在搜索框输入的文本
    // 返回：转换后的FeaturedCardItem数组
    // 进度：✅ 已完成真实API集成
    private func fetchSearchList(pageNum: Int, keyword: String) async throws -> [FeaturedCardItem] {
        // 📝 API参数构建
        let params: [String: Any] = [
            "page": "\(pageNum + 1)", // 转换：UI页码0 -> API页码1
            "keyword": keyword // 用户输入的搜索关键词
        ]

        // 🔍 API调用：搜索帖子内容
        // API: CharacterRepository.searchKeyword()
        // 用途：根据关键词搜索帖子标题和内容
        // 返回：PostInfo数组（帖子信息）
        guard let posts = await characterRepository.searchKeyword(params: params) else {
            throw NSError(domain: "网络错误", code: -1, userInfo: [NSLocalizedDescriptionKey: "搜索失败"])
        }

        // 📦 数据转换：PostInfo -> FeaturedCardItem
        // 注意：搜索返回的是帖子数据，不是角色数据
        return posts.map { convertPostToFeaturedCardItem($0) }
    }
    
    var body: some ReducerOf<Self> {
        BindingReducer()
        
        Reduce<State, Action> { state, action in
            switch action {
            case .binding:
                return .none
                
            case .onAppear:
                // 检查用户登录状态并获取头像
                state.isLoggedIn = userRepository.isLoggedIn()
                
                var effects: [Effect<Action>] = []
                
                // 如果已登录，获取用户头像
                if state.isLoggedIn {
                    effects.append(.run { send in
                        if let avatarURL = await userRepository.getUserAvatarURL() {
                            await send(.binding(.set(\.userAvatarUrl, avatarURL)))
                        }
                    })
                }
                
                // 加载标签列表
                if state.tabs.isEmpty {
                    effects.append(.send(.loadTabs))
                }
                
                return .merge(effects)
                
            case .onDisappear:
                return .none

            case .setTabActive(let active):
                // Handle tab activation/deactivation
                // This can be used to pause/resume operations when tab becomes inactive/active
                return .none

            case .loadTabs:
                state.isLoadingTabs = true
                state.tabsPage = 0
                state.pageState = .loading // 设置页面加载状态

                return .run { send in
                    let result = await fetchTabs(page: 0)
                    switch result {
                    case .success(let tabs):
                        await send(._handleTabsLoaded(tabs))
                    case .failure(let error):
                        await send(._handleTabsLoadFailed(error.localizedDescription))
                    }
                }

            case .loadMoreTabs:
                guard state.hasMoreTabs && !state.isLoadingMoreTabs else { return .none }

                state.isLoadingMoreTabs = true
                let nextPage = state.tabsPage + 1

                return .run { send in
                    let result = await fetchTabs(page: nextPage)
                    switch result {
                    case .success(let tabs):
                        await send(._handleMoreTabsLoaded(tabs))
                    case .failure:
                        // 加载更多标签失败时，只是重置加载状态，不影响页面状态
                        await send(._handleMoreTabsLoaded([]))
                    }
                }

            case ._handleTabsLoaded(let tabs):
                state.isLoadingTabs = false
                state.tabs = tabs
                state.tabsPage = 0
                state.hasMoreTabs = tabs.count >= 10 // 假设每页10个

                // 如果有标签，选择第一个标签（全部）并加载数据
                if !tabs.isEmpty {
                    state.selectedTabIndices = [0]
                    state.selectedTabCodes = [tabs[0].code]

                    return .run { send in
                        await send(.refresh(completion: { _ in }))
                    }
                }

                return .none

            case ._handleTabsLoadFailed(let errorMessage):
                state.isLoadingTabs = false
                state.pageState = .failed(error: errorMessage)
                return .none

            case ._handleMoreTabsLoaded(let newTabs):
                state.isLoadingMoreTabs = false
                state.tabs.append(contentsOf: newTabs)
                state.tabsPage += 1
                state.hasMoreTabs = newTabs.count >= 10 // 假设每页10个

                return .none
                
            case .tabToggled(let index):
                guard index >= 0 && index < state.tabs.count else {
                    return .none
                }

                let tab = state.tabs[index]
                let isAllTab = tab.code == "all"
                let isCurrentlySelected = state.selectedTabIndices.contains(index)

                if isAllTab {
                    // 点击"全部"标签
                    if isCurrentlySelected {
                        // 如果"全部"已选中，不允许取消（至少要有一个标签选中）
                        return .none
                    } else {
                        // 选中"全部"，取消其他所有标签
                        state.selectedTabIndices = [index]
                        state.selectedTabCodes = [tab.code]
                    }
                } else {
                    // 点击其他标签
                    if isCurrentlySelected {
                        // 取消选中该标签
                        state.selectedTabIndices.remove(index)
                        state.selectedTabCodes.remove(tab.code)

                        // 如果没有标签选中，自动选中"全部"
                        if state.selectedTabIndices.isEmpty {
                            state.selectedTabIndices = [0]
                            state.selectedTabCodes = ["all"]
                        }
                    } else {
                        // 选中该标签
                        // 如果当前选中的是"全部"，先取消"全部"
                        if state.selectedTabCodes.contains("all") {
                            state.selectedTabIndices.remove(0)
                            state.selectedTabCodes.remove("all")
                        }
                        state.selectedTabIndices.insert(index)
                        state.selectedTabCodes.insert(tab.code)
                    }
                }

                // 清空当前列表并设置内容加载状态
                state.items = []
                state.isLoadingContent = true

                return .run { send in
                    await send(.refresh(completion: { _ in }))
                }

            case .tabSelected(let index):
                // 保留兼容性，转发到tabToggled
                return .send(.tabToggled(index))
                
            case let .refresh(completion):
                state.isRefreshing = true

                // 只有在列表为空时才显示加载状态
                if state.items.isEmpty {
                    state.isLoadingContent = true
                }

                state.hasMore = true
                state.loadMoreFailed = false
                state.currentPage = 0 // 重置页码
                
                return .run { [searchText = state.searchText, tabCodes = state.selectedTabCodes] send in
                    do {
                        XLog.i("� 开始获取数据，搜索文本：'\(searchText)'，标签：\(Array(tabCodes))")
                        let items = try await fetchData(pageNum: 0, searchText: searchText, tabCodes: tabCodes)
                        XLog.i("✅ 获取数据成功，数量：\(items.count)")
                        await send(._handleRefreshResult(.success(items), completion: completion))
                    } catch {
                        await send(._handleRefreshResult(.failure("网络错误"), completion: completion))
                    }
                }
                .cancellable(id: "featured-network", cancelInFlight: true)
                
            case let ._handleRefreshResult(result, completion):
                state.isRefreshing = false
                state.isLoadingContent = false // 清除内容加载状态
                switch result {
                case .success(let items):

                    state.items = items

                    if state.pageState == .loading {
                        state.pageState = .success
                    }

                    state.hasMore = items.count == Self.pageSize
                    completion(.success)
                case .failure(let error):
                    if !state.items.isEmpty {
                        state.toastMessage = "刷新失败：\(error)"
                        // 3秒后自动关闭toast
                        return .run { send in
                            try await Task.sleep(for: .seconds(3))
                            await send(.dismissToast)
                        }
                    } else {
                        state.pageState = .failed(error: error)
                        state.hasMore = false
                    }
                    completion(.failure)
                }
                return .none
                
            case .dismissToast:
                state.toastMessage = nil
                return .none
                
            case let .loadMore(completion):
                XLog.i("加载更多卡片")
                guard !state.isLoadingMore else {
                    completion(.failure)
                    return .none
                }

                state.isLoadingMore = true
                state.loadMoreFailed = false

                let nextPage = state.currentPage + 1

                return .run { [searchText = state.searchText, tabCodes = state.selectedTabCodes] send in
                    do {
                        let items = try await fetchData(pageNum: nextPage, searchText: searchText, tabCodes: tabCodes)
                        await send(._handleLoadMoreResult(.success(items), completion: completion))
                    } catch {
                        await send(._handleLoadMoreResult(.failure("网络错误"), completion: completion))
                    }
                }
                .cancellable(id: "featured-network-loadmore", cancelInFlight: true)
                
            case let ._handleLoadMoreResult(result, completion):
                state.isLoadingMore = false
                switch result {
                case .success(let items):
                    XLog.i("[分页] 加载更多返回 items: \(items.count)")
                    if items.isEmpty {
                        XLog.i("[分页] items 为空，hasMore = false")
                        state.hasMore = false
                        completion(.noMore)
                    } else {
                        XLog.i("📊 FeaturedFeature: 加载更多成功，追加items - 原数量=\(state.items.count), 新增=\(items.count)")
                        state.items += items
                        let hasNext = items.count == Self.pageSize
//                        XLog.i("[分页] items.count = \(items.count), pageSize = \(Self.pageSize), hasMore = \(hasNext)")
                        state.hasMore = hasNext
                        
                        // 加载成功时更新当前页码
                        state.currentPage += 1
                        
                        completion(hasNext ? .success : .noMore)
                    }
                case .failure(let error):
                    XLog.i("[分页] 加载失败: \(error)")
                    state.loadMoreFailed = true
                    state.toastMessage = "加载更多失败：\(error)"
                    completion(.failure)
                    
                    // 3秒后自动关闭toast
                    return .run { send in
                        try await Task.sleep(for: .seconds(3))
                        await send(.dismissToast)
                    }
                }
                return .none
                
            case .cardTapped:
                return .none
                
            case .loginButtonTapped:
                // 处理登录按钮点击，请求跳转到登录页面
                XLog.i("请求跳转到登录页面")
                return .send(.delegate(.loginRequested(context: .guestLogin)))
                
            case .profileButtonTapped:
                // 处理个人资料按钮点击，请求跳转到个人信息页面
                XLog.i("请求跳转到个人信息页面")
                return .send(.delegate(.navigateToProfile))
                
            case .delegate:
                // 委托动作由父组件处理
                return .none
            }
        }
    }
}

