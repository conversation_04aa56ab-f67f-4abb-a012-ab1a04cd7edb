import SwiftUI
import LogCore
import ComposableArchitecture
import SwiftUIPager

struct FeaturedView: View {
    @Bindable var store: StoreOf<FeaturedFeature>

    // MARK: - 子视图组件

    /// 头部搜索栏和头像
    private var headerView: some View {
        HStack(spacing: 12) {
            SearchBarView(
                text: $store.searchText,
                placeholder: NSLocalizedString("搜索角色", comment: "精选页面搜索框占位符")
            )
            .frame(height: 40)

            UserAvatarButton(
                isLoggedIn: store.isLoggedIn,
                avatarUrl: store.userAvatarUrl,
                size: 40,
                onLoginTapped: {
                    DispatchQueue.main.async {
                        store.send(.loginButtonTapped)
                    }
                },
                onProfileTapped: {
                    DispatchQueue.main.async {
                        store.send(.profileButtonTapped)
                    }
                }
            )
        }
        .padding(.horizontal, 16)
        .padding(.top, 12)
        .padding(.bottom, 12)
    }

    /// 标签栏组件
    @ViewBuilder
    private var tabsView: some View {
        if !store.tabs.isEmpty {
            ScrollView(.horizontal, showsIndicators: false) {
                LazyHStack(spacing: 16) {
                    ForEach(store.tabs.indices, id: \.self) { index in
                        let tab = store.tabs[index]
                        let isSelected = store.selectedTabIndices.contains(index)

                        Button(action: {
                            DispatchQueue.main.async {
                                store.send(.tabToggled(index))
                            }
                        }) {
                            HStack(spacing: 4) {
                                Text(tab.name)
                                    .font(.system(size: 16))
                                    .fontWeight(isSelected ? .semibold : .regular)
                                    .foregroundColor(isSelected ? .white.opacity(0.9) : .white.opacity(0.6))

                                // 多选状态指示器
                                if isSelected && tab.code != "all" {
                                    Image(systemName: "checkmark.circle.fill")
                                        .font(.system(size: 12))
                                        .foregroundColor(.white.opacity(0.9))
                                }
                            }
                            .padding(.vertical, 8)
                            .padding(.horizontal, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(isSelected ? Color("color-red700") : Color.clear)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 16)
                                            .stroke(isSelected ? Color.clear : Color.white.opacity(0.2), lineWidth: 1)
                                    )
                            )
                        }
                        .onAppear {
                            // 懒加载：当滚动到倒数第3个标签时，加载更多
                            Task {
                                if index == store.tabs.count - 3 && store.hasMoreTabs && !store.isLoadingMoreTabs {
                                    await store.send(.loadMoreTabs).finish()
                                }
                            }
                        }
                    }

                    // 加载更多标签的指示器
                    if store.isLoadingMoreTabs {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                            .padding(.horizontal, 8)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
            }
            // 只在水平滑动时拦截手势，允许标签栏正常滚动
            .simultaneousGesture(
                DragGesture()
                    .onChanged { value in
                        // 只有当水平滑动距离大于垂直滑动时才拦截
                        if abs(value.translation.width) > abs(value.translation.height) {
                            // 拦截水平滑动手势，阻止传播到 SwiftUIPager
                        }
                    }
            )
            .frame(height: 48)
            .background(Color("surface-background"))
        }
    }

    /// 内容列表组件
    private var contentListView: some View {
        let items = store.items
        let hasMore = store.hasMore
        let loadMoreFailed = store.loadMoreFailed
        let onLoadMore: (@escaping (LoadMoreResult) -> Void) -> Void = { completion in
            DispatchQueue.main.async {
                store.send(.loadMore(completion: completion))
            }
        }
        let onRefresh: (@escaping (RefreshResult) -> Void) -> Void = { completion in
            DispatchQueue.main.async {
                store.send(.refresh(completion: completion))
            }
        }



        return WaterfallGridListView(
            items: items,
            hasMore: hasMore,
            loadMoreFailed: loadMoreFailed,
            isLoadingContent: store.isLoadingContent,
            onLoadMore: onLoadMore,
            onRefresh: onRefresh,
            cardView: cardView
        )
        .padding(.top, 12)
    }

    /// 卡片视图工厂方法
    @ViewBuilder
    private func cardView(item: FeaturedCardItem, cardWidth: CGFloat) -> some View {
        FeaturedCardView(item: item, cardWidth: cardWidth)
            .onTapGesture {
                DispatchQueue.main.async {
                    store.send(.cardTapped(item))
                }
            }
            .id("featured-card-wrapper-\(item.id)")
    }
    
    var body: some View {
        PageStateView(
            state: store.pageState,
            retryAction: {
                // 使用DispatchQueue.main.async避免在视图更新期间修改状态
                DispatchQueue.main.async {
                    store.send(.loadTabs)
                }
            }
        ) {
            ZStack {
                VStack(spacing: 0) {
                    // 顶部搜索栏和头像
                    headerView

                    // 标签栏
                    tabsView

                    // 内容列表
                    contentListView
                }
                
                // Toast消息
                if let toastMessage = store.toastMessage {
                    VStack {
                        Spacer()
                        Text(toastMessage)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.black.opacity(0.7))
                            )
                            .padding(.bottom, 130)
                            .transition(.opacity)
                            .onTapGesture {
                                // 使用DispatchQueue.main.async避免在视图更新期间修改状态
                                DispatchQueue.main.async {
                                    store.send(.dismissToast)
                                }
                            }
                    }
                    .animation(.easeInOut, value: store.toastMessage)
                    .zIndex(100) // 确保toast显示在最上层
                }
            }
        }
        .onAppear {
            DispatchQueue.main.async {
                store.send(.onAppear)
            }
        }
        .onDisappear {
            Task {
                await store.send(.onDisappear).finish()
            }
        }
    }
}

#if DEBUG
struct FeaturedView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建模拟标签数据
        let tabs = [
            FeaturedFeature.FeaturedTabItem(name: "全部", code: "all"),
            FeaturedFeature.FeaturedTabItem(name: "古风", code: "traditional"),
            FeaturedFeature.FeaturedTabItem(name: "科幻", code: "scifi"),
            FeaturedFeature.FeaturedTabItem(name: "动漫", code: "anime")
        ]
        
        // 创建模拟卡片数据
        let mockItems: [FeaturedCardItem] = (0..<10).map { i in
            FeaturedCardItem(
                id: UUID(),
                coverURL: "https://picsum.photos/300/\(400 + i * 10)?random=\(i)",
                title: "精选角色 \(i + 1)",
                avatarURL: "https://i.pravatar.cc/150?img=\(i % 70)",
                subtitle: "作者 \(i % 10)",
                likeCount: 1000 + i * 100,
                isLiked: i % 3 == 0,
                commentCount: 500 + i * 50,
                tags: ["Tab3", "动漫", "科幻"]
            )
        }
        
        // 创建多种状态的预览
        Group {
            // 1. 加载完成状态
            FeaturedView(
                store: Store(
                    initialState: {
                        var state = FeaturedFeature.State()
                        state.items = mockItems
                        state.pageState = .success
                        state.tabs = tabs
                        state.selectedTabIndices = [0]
                        state.selectedTabCodes = ["all"]
                        state.isLoggedIn = true
                        state.userAvatarUrl = URL(string: "https://i.pravatar.cc/150")
                        return state
                    }()
                ) {
                    FeaturedFeature()
                }
            )
            .previewDisplayName("加载完成状态")
            .background(Color("color-background"))
            
            // 2. 加载中状态
            FeaturedView(
                store: Store(
                    initialState: {
                        var state = FeaturedFeature.State()
                        state.pageState = .loading
                        state.tabs = tabs
                        return state
                    }()
                ) {
                    FeaturedFeature()
                }
            )
            .previewDisplayName("加载中状态")
            .background(Color("color-background"))

            // 3. 多选标签状态
            FeaturedView(
                store: Store(
                    initialState: {
                        var state = FeaturedFeature.State()
                        state.items = mockItems
                        state.pageState = .success
                        state.tabs = tabs
                        state.selectedTabIndices = [1, 2, 3] // 选中古风、科幻、动漫
                        state.selectedTabCodes = ["traditional", "scifi", "anime"]
                        state.isLoggedIn = true
                        state.userAvatarUrl = URL(string: "https://i.pravatar.cc/150")
                        return state
                    }()
                ) {
                    FeaturedFeature()
                }
            )
            .previewDisplayName("多选标签状态")
            .background(Color("color-background"))
            
            // 3. 加载失败状态
            FeaturedView(
                store: Store(
                    initialState: {
                        var state = FeaturedFeature.State()
                        state.pageState = .failed(error: "网络错误，请重试")
                        state.tabs = tabs
                        return state
                    }()
                ) {
                    FeaturedFeature()
                }
            )
            .previewDisplayName("加载失败状态")
            .background(Color("color-background"))
            
            // 4. 显示Toast状态
            FeaturedView(
                store: Store(
                    initialState: {
                        var state = FeaturedFeature.State()
                        state.items = mockItems
                        state.pageState = .success
                        state.toastMessage = "加载更多失败，请重试"
                        state.tabs = tabs
                        state.isLoggedIn = false
                        return state
                    }()
                ) {
                    FeaturedFeature()
                }
            )
            .previewDisplayName("Toast提示状态")
            .background(Color("color-background"))
        }
    }
}
#endif

