import SwiftUI
import LogCore
import ComposableArchitecture

struct RecommendView: View {
    @Bindable var store: StoreOf<RecommendFeature>
    let selectedTab: Int
    

    @ViewBuilder
    private func cardView(item: RecommendCardItem, cardWidth: CGFloat) -> some View {
        RecommendCardView(item: item, cardWidth: cardWidth)
            .onTapGesture {
                DispatchQueue.main.async {
                    store.send(.cardTapped(item))
                }
            }
            .id("recommend-card-wrapper-\(item.id)")
    }

    var body: some View {
        // 先将所有参数变量提取到 body 外部
        let items = store.content.items
        let hasMore = store.content.hasMore
        let loadMoreFailed = store.content.loadMoreFailed
        let onLoadMore: (@escaping (LoadMoreResult) -> Void) -> Void = { completion in
            // 使用DispatchQueue.main.async避免在视图更新期间修改状态
            DispatchQueue.main.async {
                store.send(.loadMore(completion: completion))
            }
        }
        let onRefresh: (@escaping (RefreshResult) -> Void) -> Void = { completion in
            // 使用DispatchQueue.main.async避免在视图更新期间修改状态
            DispatchQueue.main.async {
                store.send(.refresh(completion: completion))
            }
        }

        return PageStateView(
            state: store.content.pageState,
            retryAction: {
                // 使用DispatchQueue.main.async避免在视图更新期间修改状态
                DispatchQueue.main.async {
                    store.send(.refresh(completion: { _ in }))
                }
            }
        ) {
            ZStack {
                VStack(spacing: 0) {
                    HStack(spacing: 12) {
                        SearchBarView(
                            text: $store.search.searchText,
                            placeholder: store.search.currentPlaceholder,
                            onCommit: {
                                DispatchQueue.main.async {
                                    store.send(.searchButtonTapped)
                                }
                            },
                            isFocused: $store.search.isFocused
                        )
                        .onChange(of: store.search.currentPlaceholder) { _, _ in }
                        .frame(height: 40)
                        UserAvatarButton(
                            isLoggedIn: store.user.isLoggedIn,
                            avatarUrl: store.user.avatarUrl,
                            size: 40,
                            onLoginTapped: {
                                DispatchQueue.main.async {
                                    store.send(.loginButtonTapped)
                                }
                            },
                            onProfileTapped: {
                                DispatchQueue.main.async {
                                    store.send(.profileButtonTapped)
                                }
                            }
                        )
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 12)
                    .padding(.bottom, 12)

                    WaterfallGridListView(
                        items: items,
                        hasMore: hasMore,
                        loadMoreFailed: loadMoreFailed,
                        isLoadingContent: false, // RecommendFeature 暂时不需要独立的内容加载状态
                        onLoadMore: onLoadMore,
                        onRefresh: onRefresh,
                        cardView: cardView
                    )
                    .padding(.top, 12)
                    .background(
                        // 添加透明背景来捕获点击事件，用于隐藏键盘
                        Color.clear
                            .contentShape(Rectangle())
                            .onTapGesture {
                                // 点击列表区域时隐藏键盘
                                DispatchQueue.main.async {
                                    store.send(.binding(.set(\.search.isFocused, false)))
                                }
                            }
                    )
                }
                
                // 显示Toast消息
                if let toastMessage = store.ui.toastMessage {
                    VStack {
                        Spacer()
                        Text(toastMessage)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.black.opacity(0.7))
                            )
                            .padding(.bottom, 130)
                            .transition(.opacity)
                            .onTapGesture {
                                DispatchQueue.main.async {
                                    store.send(.dismissToast)
                                }
                            }
                    }
                    .animation(.easeInOut, value: store.ui.toastMessage)
                    .zIndex(100) // 确保toast显示在最上层
                }
            }
        }
        .onAppear {
            DispatchQueue.main.async {
                store.send(.onAppear)
            }
        }
        .onDisappear {
            DispatchQueue.main.async {
                store.send(.onDisappear)
            }
        }
        .onChange(of: selectedTab) { _, newValue in
            // 0 为 recommend tab
            // 使用DispatchQueue.main.async避免在视图更新期间修改状态
            DispatchQueue.main.async {
                store.send(.setTabActive(newValue == 0))
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // 从后台返回前台时刷新搜索词
            // 使用DispatchQueue.main.async避免在视图更新期间修改状态
            DispatchQueue.main.async {
                if selectedTab == 0 { // 只有在推荐页面时才刷新
                    store.send(.refreshSearchPlaceholder)
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
            // 应用变为活跃状态时刷新搜索词（例如从锁屏返回）
            // 使用DispatchQueue.main.async避免在视图更新期间修改状态
            DispatchQueue.main.async {
                if selectedTab == 0 { // 只有在推荐页面时才刷新
                    store.send(.refreshSearchPlaceholder)
                }
            }
        }
    }
}

#if DEBUG
import ComposableArchitecture

struct RecommendView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 正常内容 - 未登录
            RecommendView(
                store: StoreOf<RecommendFeature>(
                    initialState: {
                        var state = RecommendFeature.State()
                        state.content.pageState = .success
                        state.user.isLoggedIn = false
                        state.search.currentPlaceholder = "热门角色：小鸭鸭"
                        return state
                    }(),
                    reducer: { RecommendFeature() }
                ),
                selectedTab: 0
            )
            .previewDisplayName("正常内容-未登录")
            
            // 正常内容 - 已登录
            RecommendView(
                store: StoreOf<RecommendFeature>(
                    initialState: {
                        var state = RecommendFeature.State()
                        state.content.pageState = .success
                        state.user.isLoggedIn = true
                        state.user.avatarUrl = URL(string: "https://picsum.photos/200")
                        state.search.currentPlaceholder = "热门角色：小鸭鸭"
                        return state
                    }(),
                    reducer: { RecommendFeature() }
                ),
                selectedTab: 0
            )
            .previewDisplayName("正常内容-已登录")
            // 空界面
            RecommendView(
                store: StoreOf<RecommendFeature>(
                    initialState: {
                        var state = RecommendFeature.State()
                        state.content.items = []
                        state.content.pageState = .success
                        return state
                    }(),
                    reducer: { RecommendFeature() }
                ),
                selectedTab: 0
            )
            .previewDisplayName("空界面")
            // 加载中界面
            RecommendView(
                store: StoreOf<RecommendFeature>(
                    initialState: {
                        var state = RecommendFeature.State()
                        state.content.pageState = .loading
                        state.content.items = []
                        return state
                    }(),
                    reducer: { RecommendFeature() }
                ),
                selectedTab: 0
            )
            .previewDisplayName("加载中")
            // 错误界面
            RecommendView(
                store: StoreOf<RecommendFeature>(
                    initialState: {
                        var state = RecommendFeature.State()
                        state.content.pageState = .failed(error: "网络错误，请重试")
                        state.content.items = []
                        return state
                    }(),
                    reducer: { RecommendFeature() }
                ),
                selectedTab: 0
            )
            .previewDisplayName("错误界面")
            // Toast消息界面
            RecommendView(
                store: StoreOf<RecommendFeature>(
                    initialState: {
                        var state = RecommendFeature.State()
                        state.content.pageState = .success
                        state.ui.toastMessage = "刷新失败，请稍后再试"
                        return state
                    }(),
                    reducer: { RecommendFeature() }
                ),
                selectedTab: 0
            )
            .previewDisplayName("Toast消息")
            // 加载更多失败界面
            RecommendView(
                store: StoreOf<RecommendFeature>(
                    initialState: {
                        var state = RecommendFeature.State()
                        state.content.pageState = .success
                        state.content.loadMoreFailed = true
                        return state
                    }(),
                    reducer: { RecommendFeature() }
                ),
                selectedTab: 0
            )
            .previewDisplayName("加载更多失败")
            // 搜索提示词界面
            RecommendView(
                store: StoreOf<RecommendFeature>(
                    initialState: {
                        var state = RecommendFeature.State()
                        state.content.pageState = .success
                        state.search.currentPlaceholder = "热门角色：小鸭鸭"
                        state.search.suggestions = ["小鸭鸭", "机器人", "伙伴"]
                        return state
                    }(),
                    reducer: { RecommendFeature() }
                ),
                selectedTab: 0
            )
            .previewDisplayName("搜索提示词")
            // 不同推荐词界面
            RecommendView(
                store: StoreOf<RecommendFeature>(
                    initialState: {
                        var state = RecommendFeature.State()
                        state.content.pageState = .success
                        state.search.currentPlaceholder = "推荐角色：机器人"
                        state.search.suggestions = ["小鸭鸭", "机器人", "伙伴"]
                        return state
                    }(),
                    reducer: { RecommendFeature() }
                ),
                selectedTab: 0
            )
            .previewDisplayName("不同推荐词")
        }
    }
}
#endif 
