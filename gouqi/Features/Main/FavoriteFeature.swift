import ComposableArchitecture
import SwiftUI

@Reducer
struct FavoriteFeature {
    @ObservableState
    struct State: Equatable {}
    
    enum Action: Equatable {}
    
    var body: some ReducerOf<Self> {
        Reduce { state, action in
            .none
        }
    }
}

struct FavoriteView: View {
    let store: StoreOf<FavoriteFeature>
    var body: some View {
        Text("收藏")
            .font(.largeTitle)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
} 