import ComposableArchitecture
import <PERSON>UI

@Reducer
struct AddRoleFeature {
    @ObservableState
    struct State: Equatable {}
    
    enum Action: Equatable {}
    
    var body: some ReducerOf<Self> {
        Reduce { state, action in
            .none
        }
    }
}

struct AddRoleView: View {
    let store: StoreOf<AddRoleFeature>
    var body: some View {
        Text("添加角色")
            .font(.largeTitle)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
} 