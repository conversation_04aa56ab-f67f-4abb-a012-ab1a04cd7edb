import ComposableArchitecture
import SwiftUI

@Reducer
struct ChatFeature {
    @ObservableState
    struct State: Equatable {}
    
    enum Action: Equatable {}
    
    var body: some ReducerOf<Self> {
        Reduce { state, action in
            .none
        }
    }
}

struct ChatView: View {
    let store: StoreOf<ChatFeature>
    var body: some View {
        Text("聊天")
            .font(.largeTitle)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
} 