import SwiftUI
import ImageCore
import King<PERSON>er

/// 精选卡片数据模型
struct FeaturedCardItem: Identifiable, Equatable {
    let id: UUID
    let coverURL: String
    let title: String
    let avatarURL: String
    let subtitle: String
    let likeCount: Int
    var isLiked: Bool
    let commentCount: Int // 新增评论数量
    var tags: [String] = []
}

extension FeaturedCardItem {
    static func == (lhs: FeaturedCardItem, rhs: FeaturedCardItem) -> Bool {
        lhs.id == rhs.id &&
        lhs.coverURL == rhs.coverURL &&
        lhs.title == rhs.title &&
        lhs.avatarURL == rhs.avatarURL &&
        lhs.subtitle == rhs.subtitle &&
        lhs.likeCount == rhs.likeCount &&
        lhs.commentCount == rhs.commentCount &&
        lhs.isLiked == rhs.isLiked
    }
}

/// 精选卡片组件，固定宽度下按真实比例显示，支持平滑过渡
struct FeaturedCardView: View, Equatable {
    let item: FeaturedCardItem
    let cardWidth: CGFloat
    let maxWidth: CGFloat
    let defaultAspectRatio: CGFloat = 4.0 / 3.0 // 默认宽高比

    @State private var targetAspectRatio: CGFloat
    @State private var isImageLoaded: Bool = false

    init(item: FeaturedCardItem, cardWidth: CGFloat, maxWidth: CGFloat = .infinity) {
        self.item = item
        self.cardWidth = min(cardWidth, maxWidth)
        self.maxWidth = maxWidth
        self._targetAspectRatio = State(initialValue: defaultAspectRatio)


    }
    
    // 格式化数字显示（如1000显示为1k）
    private func formatCount(_ count: Int) -> String {
        if count >= 1000 {
            let formatted = Double(count) / 1000.0
            return String(format: "%.1fk", formatted)
        }
        return "\(count)"
    }

    var body: some View {
        let displayHeight: CGFloat = cardWidth * targetAspectRatio

        VStack(alignment: .leading, spacing: 8) {
            // 卡片图片及覆盖内容
            ZStack {
                // 背景图片
                KFImage(URL(string: item.coverURL))
                    .onSuccess { result in
                        let size = result.image.size
                        if size.width > 0 {
                            let imageRatio = size.height / size.width

                            // 使用DispatchQueue.main.async确保在下一个运行循环中修改状态
                            DispatchQueue.main.async {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    targetAspectRatio = imageRatio
                                    isImageLoaded = true
                                }
                            }
                        }
                    }
                    .placeholder {
                        Image("book_normal")
                            .resizable()
                            .scaledToFill()
                    }
                    .resizable()
                    .scaledToFill()
                    .frame(width: cardWidth, height: displayHeight)
                    .clipped()
                    .cornerRadius(8)
                
                // 右上角互动容器
                VStack {
                    HStack {
                        Spacer()
                        // 自适应宽度的毛玻璃容器
                        ZStack {
                            VisualEffectBlur(
                                colorTint: .white,
                                colorTintAlpha: 0.3,
                                blurRadius: 8
                            )
                            .cornerRadius(4)
                            
                            // 内容区域
                            HStack(spacing: 6) {
                                // 聊天数据
                                HStack(spacing: 2) {
                                    Image("chat_icon")
                                        .resizable()
                                        .frame(width: 12, height: 12)
                                    
                                    Text(formatCount(item.commentCount))
                                        .font(.system(size: 12, weight: .medium))
                                        .foregroundColor(.white)
                                        .lineLimit(1)
                                }
                                
                                Text("|")
                                    .font(.system(size: 10))
                                    .foregroundColor(.white.opacity(0.5))
                                
                                // 收藏数据
                                HStack(spacing: 2) {
                                    Image("favorite_icon")
                                        .resizable()
                                        .frame(width: 12, height: 12)
                                    
                                    Text(formatCount(item.likeCount))
                                        .font(.system(size: 12, weight: .medium))
                                        .foregroundColor(.white)
                                        .lineLimit(1)
                                }
                            }
                            .padding(.horizontal, 6)
                        }
                        .fixedSize(horizontal: true, vertical: false)
                        .frame(height: 20)
                        .padding(.top, 10)
                        .padding(.trailing, 10)
                    }
                    Spacer()
                }
                
                // 底部标签栏 - 直接覆盖在底部
                VStack {
                    Spacer() // 推到底部
                    VStack(alignment: .leading, spacing: 4) {
                        // 角色名称
                        Text(item.title)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .lineLimit(1)
                            .truncationMode(.tail)
                            .padding(.bottom, 6)
                            .padding(.top, 6)
                        
                        // 标签
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 6) {
                                ForEach(item.tags, id: \.self) { tag in
                                    Text("#\(tag)")
                                        .font(.system(size: 10))
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 6)
                                        .background(
                                            Capsule()
                                                .fill(Color("color-red700-tab"))
                                        )
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.bottom, 20)
                    .background(
                        Rectangle()
                            .fill(LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.black.opacity(0.0),
                                    Color.black.opacity(0.3),
                                    Color.black.opacity(0.8),
                                    Color.black.opacity(1),
                                    Color.black.opacity(1)
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            ))
                            .cornerRadius(8)
                    )
                }
                .frame(width: cardWidth)
            }
            .frame(width: cardWidth, height: displayHeight)
        }
        .frame(width: cardWidth)
        .id("featured-card-\(item.id)")
    }

    // MARK: - Equatable
    static func == (lhs: FeaturedCardView, rhs: FeaturedCardView) -> Bool {
        return lhs.item.id == rhs.item.id &&
               lhs.cardWidth == rhs.cardWidth &&
               lhs.item == rhs.item
    }
}

#if DEBUG
struct FeaturedCardView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color("color-background").ignoresSafeArea()
            
            VStack {
                let cardWidth: CGFloat = 180
                FeaturedCardView(item: FeaturedCardItem(
                    id: UUID(),
                    coverURL: "https://picsum.photos/300/400",
                    title: "角色名称",
                    avatarURL: "https://i.pravatar.cc/150",
                    subtitle: "作者名字",
                    likeCount: 4200,
                    isLiked: false,
                    commentCount: 4200,
                    tags: ["Tab3", "Tab3", "Tab3"]
                ), cardWidth: cardWidth)
                .padding()
                
                FeaturedCardView(item: FeaturedCardItem(
                    id: UUID(),
                    coverURL: "https://picsum.photos/300/400",
                    title: "角色名称",
                    avatarURL: "https://i.pravatar.cc/150",
                    subtitle: "作者名字",
                    likeCount: 4200,
                    isLiked: true,
                    commentCount: 4200,
                    tags: ["Tab3", "Tab3", "Tab3"]
                ), cardWidth: cardWidth)
                .padding()
            }
        }
        .previewLayout(.sizeThatFits)
    }
}
#endif 
