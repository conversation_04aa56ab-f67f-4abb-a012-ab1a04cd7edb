import SwiftUI

/// 通用错误状态视图，支持错误提示和重试按钮
public struct ErrorStateView: View {
    let error: String
    let retryAction: (() -> Void)?
    public init(error: String, retryAction: (() -> Void)? = nil) {
        self.error = error
        self.retryAction = retryAction
    }
    public var body: some View {
        VStack {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.white)
            Text(error)
                .foregroundColor(.white)
                .padding(.top, 8)
            if let retry = retryAction {
                Button(NSLocalizedString("点击重试", comment: "错误页面重试按钮"), action: retry)
                    .padding(.top, 12)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#if DEBUG
struct ErrorStateView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            ErrorStateView(error: "网络错误，请重试")
                .background(Color("color-background").ignoresSafeArea())
                .previewDisplayName("仅错误提示")
            ErrorStateView(error: "网络错误，请重试", retryAction: {})
                .background(Color("color-background").ignoresSafeArea())
                .previewDisplayName("带重试按钮")
        }
        .previewLayout(.sizeThatFits)
    }
}
#endif 
