import SwiftUI
import ComposableArchitecture

/// 用户头像/登录按钮组件
/// - 游客状态：显示登录按钮
/// - 登录状态：显示用户头像
public struct UserAvatarButton: View {
    // 用户状态
    let isLoggedIn: Bool
    
    // 头像URL
    let avatarUrl: URL?
    
    // 按钮尺寸
    let size: CGFloat
    
    // 点击事件
    let onLoginTapped: () -> Void
    let onProfileTapped: () -> Void
    
    public init(
        isLoggedIn: Bool,
        avatarUrl: URL? = nil,
        size: CGFloat = 40,
        onLoginTapped: @escaping () -> Void,
        onProfileTapped: @escaping () -> Void
    ) {
        self.isLoggedIn = isLoggedIn
        self.avatarUrl = avatarUrl
        self.size = size
        self.onLoginTapped = onLoginTapped
        self.onProfileTapped = onProfileTapped
    }
    
    public var body: some View {
        Button(action: {
            if isLoggedIn {
                onProfileTapped()
            } else {
                onLoginTapped()
            }
        }) {
            if isLoggedIn {
                // 已登录状态：显示头像
                if let avatarUrl = avatarUrl {
                    AsyncImage(url: avatarUrl) { phase in
                        switch phase {
                        case .empty:
                            ProgressView()
                        case .success(let image):
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        case .failure:
                            Image(systemName: "person.fill")
                                .foregroundColor(.white)
                        @unknown default:
                            Image(systemName: "person.fill")
                                .foregroundColor(.white)
                        }
                    }
                    .frame(width: size, height: size)
                    .clipShape(Circle())
                } else {
                    // 已登录但没有头像URL
                    Circle()
                        .fill(Color("color-red300"))
                        .frame(width: size, height: size)
                        .overlay(
                            Image(systemName: "person.fill")
                                .foregroundColor(.white)
                        )
                }
            } else {
                // 未登录状态：显示登录按钮
                ZStack {
                    Color("color-red300")
                        .frame(width: size+10, height: size)
                        .cornerRadius(size/5)
                    
                    Text("登入", comment: "登录按钮文本")
                        .font(.system(size: size * 0.35))
                        .foregroundColor(.white)
                }
            }
        }
    }
}

#if DEBUG
struct UserAvatarButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            // 未登录状态
            UserAvatarButton(
                isLoggedIn: false,
                onLoginTapped: {},
                onProfileTapped: {}
            )
            .previewDisplayName("游客状态")
            
            // 已登录状态 - 有头像
            UserAvatarButton(
                isLoggedIn: true,
                avatarUrl: URL(string: "https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"),
                onLoginTapped: {},
                onProfileTapped: {}
            )
            .previewDisplayName("已登录-有头像")
            
            // 已登录状态 - 无头像
            UserAvatarButton(
                isLoggedIn: true,
                avatarUrl: nil,
                onLoginTapped: {},
                onProfileTapped: {}
            )
            .previewDisplayName("已登录-无头像")
        }
        .padding()
        .background(Color("color-background").ignoresSafeArea())
    }
}
#endif 
