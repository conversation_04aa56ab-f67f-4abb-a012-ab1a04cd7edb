import SwiftUI
import VisualEffectView

struct VisualEffectBlur: View {
    var colorTint: Color? = nil
    var colorTintAlpha: CGFloat = 0
    var blurRadius: CGFloat = 10
    var scale: CGFloat = 1

    var body: some View {
        VisualEffect(
            colorTint: colorTint,
            colorTintAlpha: colorTintAlpha,
            blurRadius: blurRadius,
            scale: scale
        )
    }
} 
