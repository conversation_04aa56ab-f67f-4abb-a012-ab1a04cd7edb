import SwiftUI

/// 页面状态枚举，支持加载中、失败、成功
public enum PageState: Equatable {
    case loading
    case failed(error: String)
    case success
}

/// 通用页面状态容器组件，统一管理加载、失败、内容三种状态

public struct PageStateView<Content: View>: View {
    let state: PageState
    let retryAction: (() -> Void)?
    let content: () -> Content
    var background: Color

    public init(
        state: PageState,
        retryAction: (() -> Void)? = nil,
        background: Color = Color("color-background"),
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.state = state
        self.retryAction = retryAction
        self.background = background
        self.content = content
    }

    public var body: some View {
        ZStack {
            background.ignoresSafeArea()
            switch state {
            case .loading:
                ListLoadingView()
            case .failed(let error):
                ErrorStateView(error: error, retryAction: retryAction)
            case .success:
                content()
            }
        }
    }
}

#if DEBUG
struct PageStateView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 32) {
            PageStateView(state: .loading) {
                EmptyView()
            }
            .frame(height: 120)
            PageStateView(state: .failed(error: "网络错误"), retryAction: {}) {
                EmptyView()
            }
            .frame(height: 120)
            PageStateView(state: .success) {
                Text("内容区域", comment: "预览示例内容文本")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.green.opacity(0.2))
            }
            .frame(height: 120)
        }
        .padding()
        .background(Color("color-background").ignoresSafeArea())
        .previewLayout(.sizeThatFits)
    }
}
#endif 
