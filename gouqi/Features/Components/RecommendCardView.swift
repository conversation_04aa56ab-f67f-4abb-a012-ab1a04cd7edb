import SwiftUI
import ImageCore
import Kingfisher
import DataRepositoryCore

// RecommendCardItem现在从DataRepositoryCore导入
public typealias RecommendCardItem = DataRepositoryCore.RecommendCardItem

/// 推荐卡片组件，固定宽度下按真实比例显示，支持平滑过渡
struct RecommendCardView: View, Equatable {
    let item: RecommendCardItem
    let cardWidth: CGFloat
    let maxWidth: CGFloat
    let defaultAspectRatio: CGFloat = 4.0 / 3.0 // 默认宽高比

    @State private var targetAspectRatio: CGFloat
    @State private var isImageLoaded: Bool = false

    init(item: RecommendCardItem, cardWidth: CGFloat, maxWidth: CGFloat = .infinity) {
        self.item = item
        self.cardWidth = min(cardWidth, maxWidth)
        self.maxWidth = maxWidth
        self._targetAspectRatio = State(initialValue: defaultAspectRatio)


    }

    var body: some View {
        let displayHeight: CGFloat = cardWidth * targetAspectRatio

        VStack(alignment: .leading, spacing: 8) {
            KFImage(URL(string: item.coverURL))
                .onSuccess { result in
                    let size = result.image.size
                    if size.width > 0 {
                        let imageRatio = size.height / size.width

                        // 使用DispatchQueue.main.async确保在下一个运行循环中修改状态
                        DispatchQueue.main.async {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                targetAspectRatio = imageRatio
                                isImageLoaded = true
                            }
                        }
                    }
                }
                .placeholder {
                    Image("book_normal")
                        .resizable()
                        .scaledToFill()
                }
                .resizable()
                .scaledToFill()
                .frame(width: cardWidth, height: displayHeight)
                .clipped()
                .cornerRadius(8)

            // 标题
            Text(item.title)
                .font(.headline)
                .foregroundColor(.white)
                .lineLimit(2)
                .truncationMode(.tail)
            // 底部信息行
            HStack {
                // 左侧：头像+次级标题
                HStack(spacing: 6) {
                    KFImage(URL(string: item.avatarURL))
                        .placeholder {
                            Image("book_normal")
                                .resizable()
                                .scaledToFill()
                        }
                        .resizable()
                        .scaledToFill()
                        .frame(width: 20, height: 20)
                        .clipShape(Circle())
                    Text(item.subtitle)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .lineLimit(1)
                }
                Spacer()
                // 右侧：喜欢icon+数字
                HStack(spacing: 4) {
                    Image(systemName: item.isLiked ? "heart.fill" : "heart")
                        .foregroundColor(item.isLiked ? .pink : .gray)
                        .font(.system(size: 16))
                    Text("\(item.likeCount)",comment: "接口数据不用翻译")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
            }
        }
        .frame(width: cardWidth)
        .id("recommend-card-\(item.id)")

    }

    // MARK: - Equatable
    static func == (lhs: RecommendCardView, rhs: RecommendCardView) -> Bool {
        return lhs.item.id == rhs.item.id &&
               lhs.cardWidth == rhs.cardWidth &&
               lhs.item == rhs.item
    }
}

#if DEBUG
struct RecommendCardView_Previews: PreviewProvider {
    static var previews: some View {
        let cardWidth: CGFloat = 300
        let maxWidth: CGFloat = 180
        RecommendCardView(item: RecommendCardItem(
            id: UUID(),
            coverURL: "https://images.unsplash.com/photo-1519125323398-675f0ddb6308",
            title: "帖子的标题...帖子的标题...帖子的标题...帖子的标题...帖子的标题...",
            avatarURL: "https://randomuser.me/api/portraits/men/32.jpg",
            subtitle: "角色名字",
            likeCount: 4200,
            isLiked: false
        ), cardWidth: cardWidth, maxWidth: maxWidth)
        .background(Color("color-background").ignoresSafeArea())
        .previewLayout(.sizeThatFits)
    }
}
#endif 
