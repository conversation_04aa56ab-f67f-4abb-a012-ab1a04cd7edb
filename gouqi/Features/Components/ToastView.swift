import SwiftUI
import Combine

/// Toast消息级别
public enum ToastLevel {
    case info
    case warning
    case error
    
    var backgroundColor: Color {
        switch self {
        case .info:
            return Color(red: 51/255, green: 52/255, blue: 56/255) // #333438
        case .warning:
            return Color(red: 242/255, green: 153/255, blue: 74/255).opacity(0.9) // #F2994A
        case .error:
            return Color(red: 235/255, green: 87/255, blue: 87/255).opacity(0.9) // #EB5757
        }
    }
    
    var textColor: Color {
        switch self {
        case .info, .warning, .error:
            return .white
        }
    }
}

/// Toast控制器
public class ToastController: ObservableObject {
    @Published var message: String = ""
    @Published var isShowing: Bool = false
    @Published var level: ToastLevel = .info
    
    private var cancellable: AnyCancellable?
    
    public static let shared = ToastController()
    
    private init() {}
    
    public func show(message: String, level: ToastLevel = .info, duration: TimeInterval = 2.0) {
        self.message = message
        self.level = level
        
        withAnimation {
            self.isShowing = true
        }
        
        // 取消之前的定时器
        self.cancellable?.cancel()
        
        // 设置新的定时器
        self.cancellable = Timer.publish(every: duration, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                withAnimation {
                    self?.isShowing = false
                }
                self?.cancellable?.cancel()
            }
    }
}

/// Toast视图组件
public struct ToastView: View {
    @StateObject private var controller = ToastController.shared
    
    public init() {}
    
    public var body: some View {
        ZStack {
            if controller.isShowing {
                VStack {
                    Spacer()
                    Text(controller.message)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(controller.level.textColor)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(controller.level.backgroundColor)
                        .cornerRadius(8)
                        .shadow(radius: 5)
                        .padding(.bottom, 20)
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                .padding(.horizontal, 20)
                .zIndex(999) // 确保Toast显示在最上层
            }
        }
        .animation(.easeInOut(duration: 0.3), value: controller.isShowing)
    }
}

#if DEBUG
struct ToastView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.black.opacity(0.8).ignoresSafeArea()
            
            VStack(spacing: 20) {
                Button("显示信息Toast") {
                    ToastController.shared.show(message: "这是一条普通信息", level: .info)
                }
                .foregroundColor(.white)
                
                Button("显示警告Toast") {
                    ToastController.shared.show(message: "这是一条警告信息", level: .warning)
                }
                .foregroundColor(.white)
                
                Button("显示错误Toast") {
                    ToastController.shared.show(message: "这是一条错误信息", level: .error)
                }
                .foregroundColor(.white)
                
                Button("显示长文本Toast") {
                    ToastController.shared.show(message: "这是一条很长的错误信息，需要换行显示的那种，测试多行情况下的样式", level: .error)
                }
                .foregroundColor(.white)
            }
            
            ToastView()
        }
    }
}
#endif 