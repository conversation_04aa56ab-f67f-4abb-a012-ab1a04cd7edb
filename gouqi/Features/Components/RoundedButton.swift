import SwiftUI

/// 按钮样式枚举，支持普通纯色和线性渐变
public enum RoundedButtonStyle {
    case normal
    case linearPlan1
    // 可扩展更多样式
}

/// 通用圆角按钮组件，支持自定义内容、尺寸、内置多种背景样式
/// - Parameters:
///   - width: 按钮宽度，默认58
///   - height: 按钮高度，默认40
///   - cornerRadius: 圆角半径，默认20
///   - style: 按钮样式，默认normal
///   - action: 按钮点击事件回调
///   - content: 按钮内容（支持自定义View，如文字、图标等）
public struct RoundedButton<Content: View>: View {
    let width: CGFloat
    let height: CGFloat
    let cornerRadius: CGFloat
    let style: RoundedButtonStyle
    let action: () -> Void
    let content: () -> Content

    public init(
        width: CGFloat = 58,
        height: CGFloat = 40,
        cornerRadius: CGFloat = 20,
        style: RoundedButtonStyle = .normal,
        action: @escaping () -> Void,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.width = width
        self.height = height
        self.cornerRadius = cornerRadius
        self.style = style
        self.action = action
        self.content = content
    }

    @ViewBuilder
    private func backgroundView() -> some View {
        switch style {
        case .normal:
            Color("surface-Secondary-Button")
        case .linearPlan1:
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(.sRGB, red: 0.945, green: 0.404, blue: 0.475, opacity: 1), // #F16779
                    Color(.sRGB, red: 0.949, green: 0.255, blue: 0.533, opacity: 1)  // #F24188
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        }
    }

    public var body: some View {
        Button(action: action) {
            ZStack {
                backgroundView()
                    .frame(width: width, height: height)
                    .cornerRadius(cornerRadius)
                content()
                    .frame(width: width, height: height)
            }
        }
    }
}

#if DEBUG
struct RoundedButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // 纯色背景
            RoundedButton {
                print("按钮点击")
            } content: {
                Text("登录")
                    .foregroundColor(.white)
            }
            // 渐变背景
            RoundedButton(
                width: 120,
                height: 44,
                cornerRadius: 22,
                style: .linearPlan1,
                action: {},
                content: {
                    Text("渐变按钮")
                        .foregroundColor(.white)
                }
            )
        }
        .padding()
        .background(Color("color-background").ignoresSafeArea())
        .previewLayout(.sizeThatFits)
    }
}
#endif 