import SwiftUI

// 需要让 RoundedButton 和 Color(hex:) 可用
// RoundedButton.swift 里的 RoundedButton/Style 建议声明为 public，或同一 module 下直接用

/// 通用自定义弹窗组件，支持单按钮和双按钮模式
public struct CustomAlertView: View {
    let message: String
    let confirmText: String
    let cancelText: String?
    let onConfirm: () -> Void
    let onCancel: (() -> Void)?
    
    public init(
        message: String,
        confirmText: String = "确认",
        cancelText: String? = nil,
        onConfirm: @escaping () -> Void,
        onCancel: (() -> Void)? = nil
    ) {
        self.message = message
        self.confirmText = confirmText
        self.cancelText = cancelText
        self.onConfirm = onConfirm
        self.onCancel = onCancel
    }
    
    public var body: some View {
        VStack(spacing: 24) {
            Text(message)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .padding(.top, 8)
            HStack(spacing: 16) {
                if let cancel = cancelText, let onCancel = onCancel {
                    RoundedButton(
                        width: 120, height: 44, cornerRadius: 12, style: .normal,
                        action: onCancel
                    ) {
                        Text(cancel)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    }
                }
                RoundedButton(
                    width: 120, height: 44, cornerRadius: 12, style: .linearPlan1,
                    action: onConfirm
                ) {
                    Text(confirmText)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            .padding(.bottom, 8)
        }
        .padding(.vertical, 24)
        .padding(.horizontal, 20)
        .background(Color(hex: "#333438"))
        .cornerRadius(16)
        .shadow(radius: 16)
        .frame(maxWidth: 340)
    }
}

#if DEBUG
struct CustomAlertView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 40) {
            // 双按钮
            CustomAlertView(
                message: "确认消耗 XX 金幣創建該角色嗎？",
                confirmText: "确认支付",
                cancelText: "返回",
                onConfirm: {},
                onCancel: {}
            )
            // 单按钮
            CustomAlertView(
                message: "帖子發布成功！",
                confirmText: "确认",
                onConfirm: {}
            )
        }
        .padding()
        .background(Color.black.opacity(0.8).ignoresSafeArea())
        .previewLayout(.sizeThatFits)
    }
}
#endif 