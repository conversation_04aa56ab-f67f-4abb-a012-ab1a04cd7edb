import SwiftUI
import VisualEffectView

/// 圆角毛玻璃容器，支持自定义内容、尺寸和毛玻璃效果
/// - Parameters:
///   - width: 容器宽度
///   - height: 容器高度
///   - cornerRadius: 圆角半径，默认20
///   - colorTint: 毛玻璃的颜色色调，默认nil
///   - colorTintAlpha: 色调透明度，默认0
///   - blurRadius: 模糊半径，默认10
///   - scale: 缩放比例，默认1
public struct RoundedBlurView<Content: View>: View {
    let width: CGFloat
    let height: CGFloat
    let cornerRadius: CGFloat
    let colorTint: Color?
    let colorTintAlpha: CGFloat
    let blurRadius: CGFloat
    let scale: CGFloat
    let content: () -> Content

    public init(
        width: CGFloat,
        height: CGFloat,
        cornerRadius: CGFloat = 20,
        colorTint: Color? = nil,
        colorTintAlpha: CGFloat = 0,
        blurRadius: CGFloat = 10,
        scale: CGFloat = 1,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.width = width
        self.height = height
        self.cornerRadius = cornerRadius
        self.colorTint = colorTint
        self.colorTintAlpha = colorTintAlpha
        self.blurRadius = blurRadius
        self.scale = scale
        self.content = content
    }

    public var body: some View {
        ZStack {
            // 毛玻璃背景
            VisualEffectBlur(
                colorTint: colorTint,
                colorTintAlpha: colorTintAlpha,
                blurRadius: blurRadius,
                scale: scale
            )
            .frame(width: width, height: height)
            .cornerRadius(cornerRadius)
            
            // 内容层
            content()
                .frame(width: width, height: height)
        }
        .frame(width: width, height: height)
    }
}

#if DEBUG
struct RoundedBlurView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            // 背景图片以展示毛玻璃效果
            Image("book_normal")
                .resizable()
                .scaledToFill()
                .edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 20) {
                // 普通毛玻璃效果
                RoundedBlurView(
                    width: 300,
                    height: 100,
                    cornerRadius: 20
                ) {
                    Text("普通毛玻璃效果")
                        .foregroundColor(.white)
                }
                
                // 带色调的毛玻璃效果
                RoundedBlurView(
                    width: 300,
                    height: 100,
                    cornerRadius: 20,
                    colorTint: Color("color-red700"),
                    colorTintAlpha: 0.3,
                    blurRadius: 8
                ) {
                    Text("带色调的毛玻璃效果")
                        .foregroundColor(.white)
                }
            }
        }
        .previewLayout(.sizeThatFits)
    }
}
#endif 