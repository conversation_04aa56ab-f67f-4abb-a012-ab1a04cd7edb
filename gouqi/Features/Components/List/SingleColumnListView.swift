//import SwiftUI
//import Refresh
//
///// 单列列表组件，支持刷新、加载更多、空列表，卡片宽度自适应
//struct SingleColumnListView<Item: Identifiable>: View {
//    let items: [Item]
//    let isRefreshing: Bool
//    let isLoadingMore: Bool
//    let hasMore: Bool
//    let onRefresh: () -> Void
//    let onLoadMore: () -> Void
//    let cardView: (Item, CGFloat) -> AnyView
//
//    @State private var headerRefreshing = false
//    @State private var footerRefreshing = false
//
//    var body: some View {
//        GeometryReader { geometry in
//            let spacing: CGFloat = 12
//            let cardWidth = geometry.size.width - spacing * 2
//            ScrollView {
//                RefreshHeader(refreshing: $headerRefreshing, action: {
//                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
//                        onRefresh()
//                    }
//                }) { progress in
//                    VStack {
//                        if headerRefreshing {
//                            ProgressView()
//                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
//                                .frame(width: 32, height: 32)
//                            Text("正在刷新...")
//                                .font(.caption)
//                                .foregroundColor(.white)
//                        } else {
//                            Image(systemName: "arrow.down.circle")
//                                .resizable()
//                                .frame(width: 32, height: 32)
//                                .foregroundColor(.white)
//                            Text("下拉刷新")
//                                .font(.caption)
//                                .foregroundColor(.white)
//                        }
//                    }
//                    .frame(maxWidth: .infinity)
//                    .background(Color("color-background"))
//                }
//
//                LazyVStack(spacing: spacing) {
//                    ForEach(items) { item in
//                        cardView(item, cardWidth)
//                    }
//                }
//                .padding(.horizontal, spacing)
//                .overlay(
//                    items.isEmpty && !headerRefreshing ? AnyView(EmptyListView()) : AnyView(EmptyView())
//                )
//
//                RefreshFooter(refreshing: $footerRefreshing, action: {
//                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
//                        onLoadMore()
//                    }
//                }) {
//                    if !hasMore {
//                        Text("没有更多了")
//                            .font(.caption)
//                            .foregroundColor(.white)
//                    } else if footerRefreshing {
//                        ProgressView()
//                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
//                            .frame(width: 32, height: 32)
//                        Text("正在加载...")
//                            .font(.caption)
//                            .foregroundColor(.white)
//                    } else {
//                        Text("上拉加载更多")
//                            .font(.caption)
//                            .foregroundColor(.white)
//                    }
//                }
//                .noMore(!hasMore)
//                .preload(offset: 50)
//            }
//            .enableRefresh()
//        }
//    }
//}
//
//#if DEBUG
//struct SingleColumnListView_Previews: PreviewProvider {
//    struct DemoItem: Identifiable {
//        let id = UUID()
//        let title: String
//        let coverWidth: CGFloat
//        let coverHeight: CGFloat
//    }
//    static var previews: some View {
//        SingleColumnListView<DemoItem>(
//            items: [
//                DemoItem(title: "A", coverWidth: 800, coverHeight: 1200),
//                DemoItem(title: "B", coverWidth: 1200, coverHeight: 800),
//                DemoItem(title: "C", coverWidth: 800, coverHeight: 800)
//            ],
//            isRefreshing: false,
//            isLoadingMore: false,
//            hasMore: true,
//            onRefresh: {},
//            onLoadMore: {},
//            cardView: { item, cardWidth in
//                AnyView(
//                    RecommendCardView(
//                        item: RecommendCardItem(
//                            id: item.id,
//                            coverURL: "",
//                            title: item.title,
//                            avatarURL: "",
//                            subtitle: "",
//                            likeCount: 0,
//                            isLiked: false
//                        ),
//                        cardWidth: cardWidth,
//                        maxWidth: cardWidth
//                    )
//                )
//            }
//        )
//        .background(Color("color-background").ignoresSafeArea())
//        .previewLayout(.sizeThatFits)
//    }
//}
//#endif 
