import SwiftUI

/// 列表加载动画/提示组件
struct ListLoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(2.0)
            Text("加载中…", comment: "列表加载中提示文本")
                .foregroundColor(.white)
                .padding(.top, 8)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#if DEBUG
struct ListLoadingView_Previews: PreviewProvider {
    static var previews: some View {
        ListLoadingView()
            .background(Color("color-background").ignoresSafeArea())
            .previewLayout(.sizeThatFits)
    }
}
#endif 
