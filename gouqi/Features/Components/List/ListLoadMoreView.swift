import SwiftUI

/// 列表底部加载更多动画/提示组件
struct ListLoadMoreView: View {
    var body: some View {
        HStack {
            ProgressView()
            Text("正在加载更多…", comment: "加载更多提示文本")
                .foregroundColor(.white)
        }
        .padding()
        .frame(maxWidth: .infinity)
    }
}

#if DEBUG
struct ListLoadMoreView_Previews: PreviewProvider {
    static var previews: some View {
        ListLoadMoreView()
            .background(Color("color-background").ignoresSafeArea())
            .previewLayout(.sizeThatFits)
    }
}
#endif 
