import SwiftUI

/// 空列表提示组件
struct EmptyListView: View {
    var body: some View {
        VStack {
            Image(systemName: "tray")
                .font(.system(size: 48))
                .foregroundColor(.white)
            Text("暂无内容", comment: "空列表提示文本")
                .foregroundColor(.white)
                .padding(.top, 8)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
//        .background(Color.red) // 临时添加红色背景用于调试大小
    }
}

#if DEBUG
struct EmptyListView_Previews: PreviewProvider {
    static var previews: some View {
        EmptyListView()
            .background(Color("color-background").ignoresSafeArea())
            .previewLayout(.sizeThatFits)
    }
}
#endif 
