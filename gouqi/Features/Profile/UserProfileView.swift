import SwiftUI
import ComposableArchitecture

struct UserProfileView: View {
    @Bindable var store: StoreOf<UserProfileFeature>
    
    var body: some View {
        ZStack {
            // 背景色
            Color("surface-background")
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 移除自定义顶部导航栏
                
                // 用户信息区域
                VStack(spacing: 16) {
                    // 用户名
                    Text("你好! \(store.username)")
                        .foregroundStyle(Color("font-primary"))
                        .font(.system(size: 18, weight: .medium))
                    
                    // 用户头像
                    ZStack {
                        Circle()
                            .fill(Color("color-pink300"))
                            .frame(width: 72, height: 72)
                        
                        Text(store.avatarLetter)
                            .foregroundStyle(.white)
                            .font(.system(size: 28, weight: .semibold))
                    }
                    
                    // 用户ID
                    Text("ID: \(store.userId)")
                        .foregroundStyle(Color("font-secondary"))
                        .font(.system(size: 14))
                        .padding(.top, 8)
                }
                .padding(.vertical, 16)
                
                // 金币区域
                HStack {
                    HStack(spacing: 8) {
                        Image(systemName: "circle.fill")
                            .foregroundStyle(Color.yellow)
                        
                        Text("\(String(format: "%.1f", store.coins))k")
                            .foregroundStyle(Color("font-primary"))
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .padding(.leading, 16)
                    
                    Spacer()
                    
                    Button(action: {
                        Task {
                            await store.send(.purchaseButtonTapped).finish()
                        }
                    }) {
                        Text("購買")
                            .foregroundStyle(.white)
                            .font(.system(size: 14, weight: .semibold))
                            .frame(width: 76, height: 36)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color("color-red500"))
                            )
                    }
                    .padding(.trailing, 16)
                }
                .frame(height: 48)
                .padding(.top, 8)
                
                // 菜单选项
                VStack(spacing: 0) {
                    // 我的角色
                    Button(action: {
                        store.send(.myRolesButtonTapped)
                    }) {
                        HStack {
                            Image(systemName: "person.fill")
                                .foregroundStyle(Color("font-primary"))
                                .frame(width: 24, height: 24)
                            
                            Text("我的角色")
                                .foregroundStyle(Color("font-primary"))
                                .font(.system(size: 16))
                            
                            Spacer()
                            
                            Image(systemName: "chevron.right")
                                .foregroundStyle(Color("icon-secondary"))
                                .font(.system(size: 14))
                        }
                        .padding(.horizontal, 16)
                        .frame(height: 56)
                    }
                    
                    Rectangle()
                        .fill(Color.white.opacity(0.08)) // #FFFFFF14 = 8% opacity white
                        .frame(height: 8)
                        .padding(.horizontal, 16)
                    
                    // 登出
                    Button(action: {
                        Task {
                            await store.send(.logoutButtonTapped).finish()
                        }
                    }) {
                        HStack {
                            Image(systemName: "rectangle.portrait.and.arrow.forward")
                                .foregroundStyle(Color("font-primary"))
                                .frame(width: 24, height: 24)
                            
                            Text("登出")
                                .foregroundStyle(Color("font-primary"))
                                .font(.system(size: 16))
                            
                            Spacer()
                            
                            Image(systemName: "chevron.right")
                                .foregroundStyle(Color("icon-secondary"))
                                .font(.system(size: 14))
                        }
                        .padding(.horizontal, 16)
                        .frame(height: 56)
                    }
                }
                .padding(.top, 24)
                
                Spacer()
            }
            
            // 加载状态
            if store.isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: Color("color-red500")))
                    .scaleEffect(1.5)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.3))
            }
            
            // Toast提示
            if store.showToast, let message = store.toastMessage {
                VStack {
                    Spacer()
                    
                    Text(message)
                        .foregroundStyle(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.black.opacity(0.7))
                        )
                        .padding(.bottom, 32)
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                .frame(maxWidth: .infinity)
                .onAppear {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        store.send(.dismissToast)
                    }
                }
            }
        }
        // 移除这一行
        // .navigationBarBackButtonHidden()
        .onAppear {
            store.send(.onAppear)
        }
    }
} 