import SwiftUI
import NetworkCore
import ComposableArchitecture
import Foundation

// 导入导航上下文（从 AppFeature 中）

@Reducer
struct UserProfileFeature {
    @ObservableState
    struct State: Equatable {
        var username: String = ""
        var userId: String = ""
        var coins: Double = 0
        var avatarLetter: String = "U"
        var isLoading: Bool = false
        var toastMessage: String? = nil
        var showToast: Bool = false
    }
    
    @CasePathable
    enum Action: BindableAction {
        case binding(BindingAction<State>)
        case onAppear
        case myRolesButtonTapped
        case logoutButtonTapped
        case purchaseButtonTapped
        case userDataLoaded(username: String, userId: String, coins: Double)
        case logoutCompleted
        case logoutFailed(message:String)
        case delegate(Delegate)
        case loadingFailed(String)
        case dismissToast
        
        @CasePathable
        enum Delegate {
            case logoutRequested(context: NavigationContext)
            case navigateToRoles
            case navigateToPurchase
        }
    }
    
    @Dependency(\.userRepository) var userRepository
    
    var body: some ReducerOf<Self> {
        BindingReducer()
        Reduce { state, action in
            switch action {
            case .onAppear:
                state.isLoading = true
                  
                return .run { send in
                    if let userInfo:UserModel = await userRepository.getUserInfo() {
                        await send(.userDataLoaded(
                            username: userInfo.nickname,
                            userId: String(userInfo.uid),
                            coins: userInfo.coin
                        ))
                    } else {
                        await send(.loadingFailed("User info is nil"))
                    }
                }
                
            case let .userDataLoaded(username, userId, coins):
                state.isLoading = false
                state.username = username
                state.userId = userId
                state.coins = coins
                if let first = username.first {
                    state.avatarLetter = String(first)
                }
                return .none
                
            case let .loadingFailed(message):
                state.isLoading = false
                state.toastMessage = message
                state.showToast = true
                print(message)
                return .none
                
            case .dismissToast:
                state.showToast = false
                state.toastMessage = nil
                return .none
                
            case .myRolesButtonTapped:
                return .send(.delegate(.navigateToRoles))
                
            case .logoutButtonTapped:
                state.isLoading = true
                return .run { send in
                    if let reques:BaseResponseRaw = await userRepository.logout() {
                        await send(.logoutCompleted)
                    }else{
                        await send(.logoutCompleted)
                    }
                   
                }
                
            case .logoutCompleted:
                state.isLoading = false
                return .send(.delegate(.logoutRequested(context: .userAction)))
            case .logoutFailed(let message):    
                state.isLoading = false
                state.toastMessage = message
                state.showToast = true
                return .none
            case .purchaseButtonTapped:
                return .send(.delegate(.navigateToPurchase))
                
            case .binding(_), .delegate(_):
                return .none
            }
        }
    }
} 
