import ComposableArchitecture
import DataRepositoryCore
import LogCore
import Foundation
import NetworkCore
import SwiftUI

// 添加对ToastController的依赖
struct ToastClient {
    var show: (String, ToastLevel, TimeInterval) -> Void
    
    static let live = Self(
        show: { message, level, duration in
            ToastController.shared.show(message: message, level: level, duration: duration)
        }
    )
}

extension DependencyValues {
    var toastClient: ToastClient {
        get { self[ToastClient.self] }
        set { self[ToastClient.self] = newValue }
    }
}

extension ToastClient: DependencyKey {
    static let liveValue = ToastClient.live
}

@Reducer
struct LoginFeature {
    @ObservableState
    struct State: Equatable {
        var email = ""
        var verificationCode = ""
        var isEmailValid = false
        var isLoading = false
        var sendCodeInProgress = false
        var countdownSeconds = 0
        var errorMessage = ""
        var showAppleLogin = false
        var showGoogleLogin = false
    }
    
    @CasePathable
    enum Action {
        case emailChanged(String)
        case validateEmail
        case verificationCodeChanged(String)
        case sendVerificationCode
        case sendCodeResponse(TaskResult<BaseResponseRaw?>)
        case loginButtonTapped
        case loginResponse(TaskResult<UserLoginData?>)
        case googleLoginTapped
        case appleLoginTapped
        case thirdPartyLoginResponse(ThirdPartyType, TaskResult<Bool>)
        case timerTick
        case delegate(Delegate)
        
        @CasePathable
        enum Delegate {
            case loginSuccess
        }
    }
    
    enum ThirdPartyType {
        case google
        case apple
    }
    
    @Dependency(\.userRepository) var userRepository
    @Dependency(\.continuousClock) var clock
    @Dependency(\.toastClient) var toastClient
    
    var body: some ReducerOf<Self> {
        Reduce { state, action in
            switch action {
            case let .emailChanged(email):
                XLog.d("Email changed in state")
                state.email = email
                // 清除之前的错误信息
                state.errorMessage = ""
                return .none
                
            case .validateEmail:
                XLog.d("验证邮箱: \(state.email)")
                // 使用EmailValidator进行严格验证
                state.isEmailValid = EmailValidator.isValid(state.email)
                
                // 显示简化的错误信息，但记录详细日志
                if !state.email.isEmpty && !state.isEmailValid {
                    let detailedErrorMsg = EmailValidator.getErrorMessage(for: state.email)
                    // 记录详细错误到日志
                    XLog.d("邮箱验证失败: \(detailedErrorMsg)")
                    // 使用Toast显示简化错误
                    return .run { _ in
                        toastClient.show("電子郵件格式不正確", .error, 2.0)
                    }
                } else {
                    state.errorMessage = ""
                    if !state.email.isEmpty {
                        XLog.d("邮箱验证通过")
                    }
                }
                return .none
                
            case let .verificationCodeChanged(code):
                XLog.d("Verification code changed in state")
                state.verificationCode = code
                // 清除错误消息
                state.errorMessage = ""
                return .none
                
            case .sendVerificationCode:
                // 再次验证邮箱
                if !EmailValidator.isValid(state.email) {
                    let detailedErrorMsg = EmailValidator.getErrorMessage(for: state.email)
                    // 记录详细错误到日志
                    XLog.d("发送验证码前邮箱验证失败: \(detailedErrorMsg)")
                    // 使用Toast显示简化错误
                    return .run { _ in
                        toastClient.show("電子郵件格式不正確", .error, 2.0)
                    }
                }
                
                if state.countdownSeconds > 0 {
                    return .none
                }
                
                state.sendCodeInProgress = true
                state.errorMessage = ""
                
                return .run { [email = state.email] send in
                
                    XLog.d("Sending verification code to: \(email)")
                    // 使用真实API发送验证码
                    let response = await userRepository.sendVerificationCode(email: email)
                    await send(.sendCodeResponse(.success(response)))
                    
                }
                
            case .sendCodeResponse(.success(let response)):
                state.sendCodeInProgress = false
                if let response = response, response.code == 200 {
                    // 成功发送验证码
                    XLog.i("Verification code sent successfully")
                    state.countdownSeconds = 60
                    // 使用Toast显示成功信息
                    return .merge(
                        .run { _ in
                            toastClient.show("验证码已发送", .info, 2.0)
                        },
                        .run { send in
                            for await _ in self.clock.timer(interval: .seconds(1)) {
                                await send(.timerTick)
                            }
                        }
                        .cancellable(id: CancelID.timer)
                    )
                } else {
                    // API返回错误
                    let errorMsg = response?.message ?? "驗證碼發送失敗，請重試"
                    // 不再更新errorMessage，改用Toast显示
                    state.errorMessage = ""
                    XLog.w("Failed to send verification code: \(errorMsg)")
                    // 使用Toast显示错误
                    return .run { _ in
                        toastClient.show(errorMsg, .error, 2.0)
                    }
                }
                
            case .sendCodeResponse(.failure(let error)):
//                XLog.e("Send verification code error: \(error.localizedDescription)")
//                state.sendCodeInProgress = false
//                state.errorMessage = "發送失敗: \(error.localizedDescription)"
                return .none
                
            case .timerTick:
                if state.countdownSeconds > 0 {
                    state.countdownSeconds -= 1
                    return state.countdownSeconds == 0 ? .cancel(id: CancelID.timer) : .none
                }
                return .cancel(id: CancelID.timer)
                
            case .loginButtonTapped:
                // 再次验证邮箱
                if !EmailValidator.isValid(state.email) {
                    let detailedErrorMsg = EmailValidator.getErrorMessage(for: state.email)
                    // 记录详细错误到日志
                    XLog.d("登录前邮箱验证失败: \(detailedErrorMsg)")
                    // 使用Toast显示简化错误
                    return .run { _ in
                        toastClient.show("電子郵件格式不正確", .error, 2.0)
                    }
                }
                
                // 验证码为空时提示
                if state.verificationCode.isEmpty {
                    let errorMsg = "請輸入驗證碼"
                    // 不再更新errorMessage，改用Toast显示
                    state.errorMessage = ""
                    // 使用Toast显示错误
                    return .run { _ in
                        toastClient.show(errorMsg, .error, 2.0)
                    }
                }
                
                state.isLoading = true
                state.errorMessage = ""

                return .run { [email = state.email, code = state.verificationCode] send in
                        // 使用真实API进行登录
                        let result = await userRepository.verifyCode(email: email, code: code)
                        await send(.loginResponse(.success(result)))
                }
                
            case .googleLoginTapped:
                state.showGoogleLogin = true
                state.isLoading = true
                return .run { send in
//                    do {
                        // 模拟Google登录过程
//                        XLog.i("Google login initiated")
//                        try await Task.sleep(for: .seconds(1.5))
//                        // 这里应该调用实际的Google登录API
//                        let mockSuccess = true
//                        await send(.thirdPartyLoginResponse(.google, .success(mockSuccess)))
//                    } catch {
//                        await send(.thirdPartyLoginResponse(.google, .failure(error)))
//                    }
                }
                
            case .appleLoginTapped:
                state.showAppleLogin = true
                state.isLoading = true
                return .run { send in
//                    do {
//                        // 模拟Apple登录过程
//                        XLog.i("Apple login initiated")
//                        try await Task.sleep(for: .seconds(1.5))
//                        // 这里应该调用实际的Apple登录API
//                        let mockSuccess = true
//                        await send(.thirdPartyLoginResponse(.apple, .success(mockSuccess)))
//                    } catch {
//                        await send(.thirdPartyLoginResponse(.apple, .failure(error)))
//                    }
                }
                
            case .thirdPartyLoginResponse(.google, .success(let success)):
                state.showGoogleLogin = false
                state.isLoading = false
                if success {
                    XLog.i("Google login successful")
                    return .send(.delegate(.loginSuccess))
                } else {
                    state.errorMessage = "Google登錄失敗，請重試"
                    return .none
                }
                
            case .thirdPartyLoginResponse(.apple, .success(let success)):
                state.showAppleLogin = false
                state.isLoading = false
                if success {
                    XLog.i("Apple login successful")
                    return .send(.delegate(.loginSuccess))
                } else {
                    state.errorMessage = "Apple登錄失敗，請重試"
                    return .none
                }
                
            case .thirdPartyLoginResponse(.google, .failure(let error)):
                state.showGoogleLogin = false
                state.isLoading = false
                state.errorMessage = "Google登錄錯誤: \(error.localizedDescription)"
                return .none
                
            case .thirdPartyLoginResponse(.apple, .failure(let error)):
                state.showAppleLogin = false
                state.isLoading = false
                state.errorMessage = "Apple登錄錯誤: \(error.localizedDescription)"
                return .none
                
            case .loginResponse(.success(let userData)):
                state.isLoading = false
                if let userData = userData, !userData.token.isEmpty {
                    // 使用Toast显示成功信息
                    return .merge(
                        .run { _ in
                            toastClient.show("登录成功", .info, 2.0)
                        },
                        .send(.delegate(.loginSuccess))
                    )
                } else {
                    let errorMsg = "驗證碼不正確，請重新輸入"
                    // 不再更新errorMessage，改用Toast显示
                    state.errorMessage = ""
                    // 使用Toast显示错误
                    return .run { _ in
                        toastClient.show(errorMsg, .error, 2.0)
                    }
                }
                
            case .loginResponse(.failure(let error)):
//                XLog.e("Login response error: \(error.localizedDescription)")
//                state.isLoading = false
//                state.errorMessage = "登錄失敗: \(error.localizedDescription)"
                return .none
                
            case .delegate(.loginSuccess):
                XLog.i("Login success delegate action")
                return .none
            }
        }
    }
    
    enum CancelID {
        case timer
    }
}
