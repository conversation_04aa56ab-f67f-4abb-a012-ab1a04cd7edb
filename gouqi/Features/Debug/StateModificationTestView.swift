import SwiftUI
import ComposableArchitecture

// 简单的测试Reducer来隔离状态修改问题
@Reducer
struct StateModificationTestFeature {
    @ObservableState
    struct State: Equatable {
        var counter = 0
        var isLoading = false
        var message = ""
    }
    
    enum Action {
        case increment
        case decrement
        case setLoading(Bool)
        case setMessage(String)
        case onAppear
    }
    
    var body: some ReducerOf<Self> {
        Reduce<State, Action> { state, action in
            switch action {
            case .increment:
                state.counter += 1
                return .none
                
            case .decrement:
                state.counter -= 1
                return .none
                
            case .setLoading(let loading):
                state.isLoading = loading
                return .none
                
            case .setMessage(let message):
                state.message = message
                return .none
                
            case .onAppear:
                state.message = "视图已出现"
                return .none
            }
        }
    }
}

// 测试视图 - 用于验证状态修改问题
struct StateModificationTestView: View {
    @Bindable var store: StoreOf<StateModificationTestFeature>
    @State private var searchText = ""
    @State private var isFocused = false

    var body: some View {
        VStack(spacing: 20) {
            Text("状态修改测试")
                .font(.title)
                .foregroundColor(.white)

            Text("计数器: \(store.counter)")
                .font(.headline)
                .foregroundColor(.white)

            // 测试SearchBarView
            SearchBarView(
                text: $searchText,
                placeholder: "测试搜索框",
                onCommit: {
                    DispatchQueue.main.async {
                        store.send(.setMessage("搜索: \(searchText)"))
                    }
                },
                isFocused: $isFocused
            )
            .frame(height: 40)
            .padding(.horizontal)

            HStack(spacing: 20) {
                // 测试1: 直接同步调用
                Button("同步+1") {
                    store.send(.increment)
                }
                .foregroundColor(.white)
                .padding()
                .background(Color.blue)
                .cornerRadius(8)

                // 测试2: 异步调用
                Button("异步+1") {
                    DispatchQueue.main.async {
                        store.send(.increment)
                    }
                }
                .foregroundColor(.white)
                .padding()
                .background(Color.green)
                .cornerRadius(8)
            }

            HStack(spacing: 20) {
                // 测试3: Task异步调用
                Button("Task+1") {
                    Task { @MainActor in
                        store.send(.increment)
                    }
                }
                .foregroundColor(.white)
                .padding()
                .background(Color.orange)
                .cornerRadius(8)

                // 测试4: 重置按钮
                Button("重置") {
                    DispatchQueue.main.async {
                        store.send(.setMessage(""))
                        store.send(.decrement)
                    }
                }
                .foregroundColor(.white)
                .padding()
                .background(Color.red)
                .cornerRadius(8)
            }

            // 测试PageStateView
            PageStateView(
                state: store.isLoading ? .loading : .success,
                retryAction: {
                    DispatchQueue.main.async {
                        store.send(.setLoading(false))
                    }
                }
            ) {
                VStack {
                    Text("PageStateView内容区域")
                        .foregroundColor(.white)

                    Button("触发加载") {
                        DispatchQueue.main.async {
                            store.send(.setLoading(true))
                        }
                    }
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.purple)
                    .cornerRadius(8)
                }
            }
            .frame(height: 100)

            if !store.message.isEmpty {
                Text(store.message)
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.black.opacity(0.7))
                    .cornerRadius(8)
                    .onTapGesture {
                        DispatchQueue.main.async {
                            store.send(.setMessage(""))
                        }
                    }
            }

            Spacer()
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color("color-background").ignoresSafeArea())
        .onAppear {
            // 测试onAppear中的状态修改 - 使用异步方式
            DispatchQueue.main.async {
                store.send(.onAppear)
            }
        }
    }
}

#if DEBUG
struct StateModificationTestView_Previews: PreviewProvider {
    static var previews: some View {
        StateModificationTestView(
            store: Store(
                initialState: StateModificationTestFeature.State(),
                reducer: { StateModificationTestFeature() }
            )
        )
    }
}
#endif
