import SwiftUI
import ComposableArchitecture

// 测试用的简单数据模型
struct TestItem: Identifiable, Equatable {
    let id = UUID()
    let title: String
    let imageUrl: String

    static func mock(count: Int) -> [TestItem] {
        (1...count).map { index in
            TestItem(
                title: "测试项目 \(index)",
                imageUrl: "https://picsum.photos/200/\(200 + index * 50)"
            )
        }
    }
}

// 最小化的测试Reducer，逐步添加功能
@Reducer
struct MinimalTestFeature {
    @ObservableState
    struct State: Equatable {
        var text = "Hello World"
        var pageState: PageState = .success
        var isLoading = false
        var items: [TestItem] = []
        var hasMore = true
        var loadMoreFailed = false
    }

    enum Action: BindableAction {
        case binding(BindingAction<State>)
        case updateText(String)
        case setPageState(PageState)
        case setLoading(Bool)
        case retry
        case loadMore
        case refresh
        case addItems([TestItem])
        // 新增：测试网络请求相关的Action
        case fetchDataAsync
        case fetchDataResult([TestItem])
        case fetchDataFailed(String)
        case simulateMultipleEffects
        case _internalEffect1
        case _internalEffect2
        case _internalEffect3
        // 新增：测试TCA Binding
        case testBinding
        case testMultipleBindings
        case testAsyncBinding
    }

    var body: some ReducerOf<Self> {
        BindingReducer()

        Reduce<State, Action> { state, action in
            switch action {
            case .binding:
                return .none

            case .updateText(let newText):
                state.text = newText
                return .none

            case .setPageState(let pageState):
                state.pageState = pageState
                return .none

            case .setLoading(let loading):
                state.isLoading = loading
                return .none

            case .retry:
                state.text = "重试成功 \(Date())"
                state.pageState = .success
                return .none

            case .loadMore:
                state.text = "加载更多 \(Date())"
                let newItems = TestItem.mock(count: 5)
                state.items.append(contentsOf: newItems)
                if state.items.count > 20 {
                    state.hasMore = false
                }
                return .none

            case .refresh:
                state.text = "刷新 \(Date())"
                state.items = TestItem.mock(count: 10)
                state.hasMore = true
                state.loadMoreFailed = false
                return .none

            case .addItems(let items):
                state.items = items
                return .none

            // 新增：测试网络请求相关的处理
            case .fetchDataAsync:
                state.text = "开始异步获取数据..."
                state.isLoading = true

                // 模拟网络请求的Effect
                return .run { send in
                    try await Task.sleep(for: .seconds(2))
                    let mockData = TestItem.mock(count: 8)
                    await send(.fetchDataResult(mockData))
                }

            case .fetchDataResult(let items):
                state.text = "异步获取数据成功"
                state.isLoading = false
                state.items = items
                return .none

            case .fetchDataFailed(let error):
                state.text = "异步获取数据失败: \(error)"
                state.isLoading = false
                return .none

            case .simulateMultipleEffects:
                state.text = "触发多个Effect..."

                // 模拟同时触发多个Effect（类似真实应用中的情况）
                return .merge([
                    .run { send in
                        try await Task.sleep(for: .milliseconds(100))
                        await send(._internalEffect1)
                    },
                    .run { send in
                        try await Task.sleep(for: .milliseconds(200))
                        await send(._internalEffect2)
                    },
                    .run { send in
                        try await Task.sleep(for: .milliseconds(300))
                        await send(._internalEffect3)
                    }
                ])

            case ._internalEffect1:
                state.text = "Effect 1 完成"
                return .none

            case ._internalEffect2:
                state.text = "Effect 2 完成"
                return .none

            case ._internalEffect3:
                state.text = "Effect 3 完成 - 所有Effect完成"
                return .none

            // 新增：测试TCA Binding相关的处理
            case .testBinding:
                state.text = "测试单个Binding..."

                return .run { send in
                    try await Task.sleep(for: .milliseconds(100))
                    await send(.binding(.set(\.text, "Binding操作完成")))
                }

            case .testMultipleBindings:
                state.text = "测试多个Binding..."

                return .run { send in
                    await send(.binding(.set(\.text, "第一个Binding")))
                    try await Task.sleep(for: .milliseconds(50))
                    await send(.binding(.set(\.isLoading, true)))
                    try await Task.sleep(for: .milliseconds(50))
                    await send(.binding(.set(\.text, "第二个Binding")))
                    try await Task.sleep(for: .milliseconds(50))
                    await send(.binding(.set(\.isLoading, false)))
                    await send(.binding(.set(\.text, "多个Binding完成")))
                }

            case .testAsyncBinding:
                state.text = "测试异步Binding..."

                return .merge([
                    .run { send in
                        try await Task.sleep(for: .milliseconds(100))
                        await send(.binding(.set(\.text, "异步Binding 1")))
                    },
                    .run { send in
                        try await Task.sleep(for: .milliseconds(200))
                        await send(.binding(.set(\.isLoading, true)))
                    },
                    .run { send in
                        try await Task.sleep(for: .milliseconds(300))
                        await send(.binding(.set(\.text, "异步Binding完成")))
                        await send(.binding(.set(\.isLoading, false)))
                    }
                ])
            }
        }
    }
}

// 最小化的测试视图，逐步添加组件
struct MinimalTestView: View {
    @Bindable var store: StoreOf<MinimalTestFeature>
    @State private var searchText = ""
    @State private var isFocused = false

    var body: some View {
        VStack(spacing: 20) {
            Text("步骤7: 测试TCA Binding")
                .font(.title)
                .foregroundColor(.white)

            Text(store.text)
                .font(.headline)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)

            VStack(spacing: 10) {
                Button("测试Binding操作") {
                    store.send(.testBinding)
                }
                .foregroundColor(.white)
                .padding()
                .background(Color.purple)
                .cornerRadius(8)

                Button("测试多个Binding") {
                    store.send(.testMultipleBindings)
                }
                .foregroundColor(.white)
                .padding()
                .background(Color.orange)
                .cornerRadius(8)

                Button("测试异步Binding") {
                    store.send(.testAsyncBinding)
                }
                .foregroundColor(.white)
                .padding()
                .background(Color.red)
                .cornerRadius(8)
            }

            // 测试WaterfallGridListView
            WaterfallGridListView(
                items: store.items,
                hasMore: store.hasMore,
                loadMoreFailed: store.loadMoreFailed,
                isLoadingContent: store.isLoading,
                onLoadMore: { completion in
                    store.send(.loadMore)
                    completion(.success)
                },
                onRefresh: { completion in
                    store.send(.refresh)
                    completion(.success)
                },
                cardView: { item, width in
                    VStack {
                        AsyncImage(url: URL(string: item.imageUrl)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Rectangle()
                                .fill(Color.gray.opacity(0.3))
                                .aspectRatio(1, contentMode: .fit)
                        }
                        .frame(width: width, height: 150)
                        .clipped()
                        .cornerRadius(8)

                        Text(item.title)
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.top, 4)
                    }
                    .frame(width: width)
                    .padding(8)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(12)
                }
            )
            .frame(height: 300)

            VStack(spacing: 10) {
                HStack(spacing: 10) {
                    Button("异步获取数据") {
                        store.send(.fetchDataAsync)
                    }
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(8)

                    Button("多个Effect") {
                        store.send(.simulateMultipleEffects)
                    }
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.purple)
                    .cornerRadius(8)
                }

                HStack(spacing: 10) {
                    Button("初始化数据") {
                        store.send(.addItems(TestItem.mock(count: 10)))
                    }
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.green)
                    .cornerRadius(8)

                    Button("清空数据") {
                        store.send(.addItems([]))
                    }
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.red)
                    .cornerRadius(8)
                }
            }

            Spacer()
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.ignoresSafeArea())
    }
}

#if DEBUG
struct MinimalTestView_Previews: PreviewProvider {
    static var previews: some View {
        MinimalTestView(
            store: Store(
                initialState: MinimalTestFeature.State(),
                reducer: { MinimalTestFeature() }
            )
        )
    }
}
#endif
