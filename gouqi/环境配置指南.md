# 环境配置指南

本项目支持三种环境配置：
- 开发环境（Development）
- 测试环境（Testing）
- 生产环境（Production）

## 当前配置方式

环境配置通过编译标志（Compiler Flags）和构建配置（Build Configurations）来实现：

1. **开发环境**：Debug模式下的默认环境
2. **测试环境**：Debug模式 + TESTING标志
3. **生产环境**：Release模式

## 如何在Xcode中配置

### 步骤1：创建构建配置

1. 打开项目设置（点击项目名称 > Info标签）
2. 在"Configurations"部分，点击"+"按钮
3. 添加以下构建配置：
   - Debug-Testing（基于Debug）
   - Release-Testing（基于Release，如有需要）

### 步骤2：设置编译标志

1. 选择项目 > Build Settings
2. 搜索"Swift Compiler - Custom Flags"
3. 展开"Other Swift Flags"
4. 为Debug-Testing配置添加`-DTESTING`标志

### 步骤3：创建构建方案（Schemes）

1. 点击Xcode顶部的方案选择器，选择"Manage Schemes..."
2. 创建三个方案：
   - gouqi-Development（使用Debug配置）
   - gouqi-Testing（使用Debug-Testing配置）
   - gouqi-Production（使用Release配置）

### 步骤4：配置方案

对于每个方案，设置相应的构建配置：

1. 选择方案 > Edit Scheme
2. 在Run、Test、Profile、Analyze和Archive选项中，设置相应的构建配置

## 环境配置的使用

在代码中，您可以通过`Environment.shared`访问当前环境配置：

```swift
// 获取当前环境
let currentEnv = Environment.shared.current

// 获取API基础URL
let baseURL = Environment.shared.baseURL

// 检查是否应该记录网络调用
if Environment.shared.shouldLogNetworkCalls {
    print("网络请求：\(url)")
}
```

## 环境特定的条件编译

您可以使用条件编译来为不同环境编写特定代码：

```swift
#if DEBUG
    // 仅在Debug模式（开发和测试环境）执行
    print("调试模式")
    
    #if TESTING
        // 仅在测试环境执行
        print("测试环境特定代码")
    #else
        // 仅在开发环境执行
        print("开发环境特定代码")
    #endif
#else
    // 仅在Release模式（生产环境）执行
    print("生产环境特定代码")
#endif
```

## 测试环境配置

项目包含多种测试来验证环境配置的正确性：

### 单元测试

1. **EnvironmentTests**: 测试基本环境配置功能
   - `testDefaultEnvironment`: 验证默认环境设置
   - `testEnvironmentAPIURLs`: 测试不同环境的API URL
   - `testLoggingConfiguration`: 测试日志记录设置

2. **EnvironmentSwitchTests**: 测试环境切换功能
   - `testEnvironmentSwitching`: 验证环境切换逻辑
   - `testCommandLineArgumentHandling`: 测试命令行参数处理

### UI测试

**EnvironmentUITests**: 测试环境信息在UI中的显示
   - `testEnvironmentInfoDisplay`: 验证环境信息是否正确显示
   - `testEnvironmentSpecificUI`: 测试环境特定的UI元素

### 运行测试

要运行特定环境的测试：

1. 选择相应的方案（Development、Testing或Production）
2. 按下 Command+U 或选择 Product > Test

## 注意事项

- 确保在提交到版本控制系统之前，项目使用正确的环境配置
- 避免在生产构建中包含测试或开发环境的代码
- 敏感信息（如API密钥）应该根据环境配置进行管理
- 在运行UI测试时，可以通过启动参数模拟不同的环境 
