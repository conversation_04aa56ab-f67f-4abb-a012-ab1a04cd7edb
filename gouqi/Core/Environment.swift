//
//  Environment.swift
//  gouqi
//
//  Created on 2025/6/16.
//

import Foundation

// 环境类型枚举
enum EnvironmentType: String {
    case development = "开发环境"
    case testing = "测试环境"
    case production = "生产环境"
}

// 环境配置类
class Environment {
    // 单例模式，如果是UI测试则使用TestableEnvironment
    static let shared: Environment = {
        if CommandLine.arguments.contains("--UITesting") {
            return TestableEnvironment()
        } else {
            return Environment()
        }
    }()
    
    // 当前环境
    #if DEBUG
        #if TESTING
            var current: EnvironmentType = .testing
        #else
            var current: EnvironmentType = .development
        #endif
    #else
        var current: EnvironmentType = .production
    #endif
    
    // API基础URL
    var baseURL: String {
        switch current {
        case .development:
            return "https://api-dev.example.com"
        case .testing:
            return "https://api-test.example.com"
        case .production:
            return "https://api.example.com"
        }
    }
    
    // API版本
    var apiVersion: String {
        return "v1"
    }
    
    // 完整的API URL
    var apiURL: String {
        return "\(baseURL)/\(apiVersion)"
    }
    
    // 是否记录日志
    var shouldLogNetworkCalls: Bool {
        switch current {
        case .development, .testing:
            return true
        case .production:
            return false
        }
    }
    
    // 初始化方法改为internal，允许在测试中创建实例
    internal init() {
        print("当前环境: \(current.rawValue)")
        print("API URL: \(apiURL)")
    }
} 