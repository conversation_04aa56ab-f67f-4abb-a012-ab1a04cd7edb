//
//  TestableEnvironment.swift
//  gouqi
//
//  Created on 2025/6/16.
//

import Foundation

// 可测试的环境配置类，用于UI测试
class TestableEnvironment: Environment {
    // 用于在测试中覆盖当前环境
    var overrideEnvironment: EnvironmentType?
    
    // 覆盖当前环境属性
    override var current: EnvironmentType {
        get { return overrideEnvironment ?? super.current }
        set { overrideEnvironment = newValue }
    }
}      
