import SwiftUI

// 1. 主题协议
protocol FontTheme {
    var title: Font { get }
    var button: Font { get }
    var input: Font { get }
    var subtitle: Font { get }
    var p1Regular: Font { get }
    // 可根据需要扩展更多字体样式
}

// 2. 明亮主题实现
struct LightFontTheme: FontTheme {
    var title: Font { .custom("Chiron GoRound TC", size: 36).weight(.bold) }
    var button: Font { .custom("Chiron GoRound TC", size: 16).weight(.medium) }
    var input: Font { .custom("Noto Sans HK", size: 14).weight(.regular) }
    var subtitle: Font { .custom("Noto Sans HK", size: 14).weight(.medium) }
    var p1Regular: Font { .custom("Noto Sans TC", size: 12).weight(.regular) }
}

// 3. 夜间主题实现
struct DarkFontTheme: FontTheme {
    var title: Font { .custom("Chiron GoRound TC", size: 36).weight(.bold) }
    var button: Font { .custom("Chiron GoRound TC", size: 16).weight(.medium) }
    var input: Font { .custom("Noto Sans HK", size: 14).weight(.regular) }
    var subtitle: Font { .custom("Noto Sans HK", size: 14).weight(.medium) }
    var p1Regular: Font { .custom("Noto Sans TC", size: 12).weight(.regular) }
}

// 4. EnvironmentKey 扩展
private struct FontThemeKey: EnvironmentKey {
    static let defaultValue: FontTheme = LightFontTheme()
}

extension EnvironmentValues {
    var fontTheme: FontTheme {
        get { self[FontThemeKey.self] }
        set { self[FontThemeKey.self] = newValue }
    }
} 