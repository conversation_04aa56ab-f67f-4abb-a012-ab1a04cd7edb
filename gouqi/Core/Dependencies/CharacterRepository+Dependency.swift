import ComposableArchitecture
import DataRepositoryCore

// CharacterRepository Dependency 注册
extension CharacterRepository: DependencyKey {
    public static var liveValue: CharacterRepository = {
        let repository = CharacterRepository()
        return repository
    }()
}

extension DependencyValues {
    var characterRepository: CharacterRepository {
        get { self[CharacterRepository.self] }
        set { self[CharacterRepository.self] = newValue }
    }
} 