import ComposableArchitecture
import DataRepositoryCore
// 假设你有如下协议和实现：
// public protocol UserRepository { ... }
// public struct RealUserRepository: UserRepository { ... }

private enum UserRepositoryKey: DependencyKey {
    static let liveValue: UserRepository = UserRepository() 
    // 如有需要可添加 testValue、previewValue
}

extension DependencyValues {
    public  var userRepository: UserRepository {
        get { self[UserRepositoryKey.self] }
        set { self[UserRepositoryKey.self] = newValue }
    }
} 
