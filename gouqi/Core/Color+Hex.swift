import SwiftUI

@available(iOS 13.0, *)
public extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, (int >> 16) & 0xFF, (int >> 8) & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = ((int >> 24) & 0xFF, (int >> 16) & 0xFF, (int >> 8) & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// 添加渐变色支持
extension LinearGradient {
    /// 应用主题渐变色
    public static var primaryGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(hex: "#F16779"),
                Color(hex: "#F24188")
            ]),
            startPoint: .leading,
            endPoint: .trailing
        )
    }
    
    /// 根据指定的hex颜色创建渐变
    public static func fromHex(startColor: String, endColor: String, startPoint: UnitPoint = .leading, endPoint: UnitPoint = .trailing) -> LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(hex: startColor),
                Color(hex: endColor)
            ]),
            startPoint: startPoint,
            endPoint: endPoint
        )
    }
}

// 添加文本渐变色支持
extension View {
    /// 为视图添加渐变前景色
    public func gradientForeground(colors: [Color], startPoint: UnitPoint = .leading, endPoint: UnitPoint = .trailing) -> some View {
        self.overlay(
            LinearGradient(
                gradient: Gradient(colors: colors),
                startPoint: startPoint,
                endPoint: endPoint
            )
            .mask(self)
        )
    }
    
    /// 应用主题渐变前景色
    public func primaryGradientForeground() -> some View {
        self.gradientForeground(colors: [
            Color(hex: "#F16779"),
            Color(hex: "#F24188")
        ])
    }
} 