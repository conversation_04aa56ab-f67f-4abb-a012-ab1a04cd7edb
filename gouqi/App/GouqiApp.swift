//
//  gouqiApp.swift
//  gouqi
//
//  Created by heng chang on 2025/6/16.
//

import SwiftUI
import ComposableArchitecture
import LogCore
import NetworkCore
import CacheCore

@main
struct GouqiApp: App {
    init() {
        // 日志系统初始化
        #if DEBUG
        XLog.setup(destinations: [.console])
        #else
        XLog.setup(isEnabled: false)
        #endif

        // 环境配置（自动单例）
        _ = Environment.shared

        // 网络库全局配置
        NetWorkConfig.curEnvironment = .test // 或 .pro
        NetWorkConfig.connectTimeout = 10
        NetWorkConfig.readTimeout = 15
        NetWorkConfig.writeTimeout = 15

        // 缓存库预热（可选）
        let _ = CacheStorageManager.shared.storage(for: "user")
        let _ = CacheStorageManager.shared.storage(for: "post", expirySeconds: 600)

        // 其他三方库初始化（如有）
        // ...
    }
    var body: some Scene {
        WindowGroup {
            AppView(
                store: Store(
                    initialState: AppFeature.State()
                ) {
                    AppFeature()
                }
            )
        }
    }
}

struct MainView_Previews: PreviewProvider {
    static var previews: some View {
        AppView(
            store: Store(
                initialState: AppFeature.State()
            ) {
                AppFeature()
            }
        )
    }
}
