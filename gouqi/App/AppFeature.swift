import ComposableArchitecture
import LogCore
import Foundation

/// 导航上下文，用于区分不同的导航场景
public enum NavigationContext: Equatable {
    case initial        // 首次启动流程
    case reauth        // 重新认证（如token过期）
    case userAction    // 用户主动操作（如设置中退出登录）
    case guestLogin    // 游客登录（功能限制提示）
}

@Reducer
struct AppFeature {
 
    @ObservableState
    struct State: Equatable {
        var path = StackState<Path.State>()
        var root: Root = .launch(LaunchFeature.State())
        var globalError: String? = nil // 新增全局错误字段
        var launch: LaunchFeature.State = LaunchFeature.State()
        var login: LoginFeature.State = LoginFeature.State()
        var main: MainFeature.State = MainFeature.State()
        var userProfile: UserProfileFeature.State = UserProfileFeature.State() // 新增用户资料状态
        var requireLoginAlert: Bool = false // 新增：需要登陆弹窗
        
      
        @CasePathable
        enum Root: Equatable {
            case launch(LaunchFeature.State)
            case login(LoginFeature.State)
            case main(MainFeature.State)
        }
    }
    
    @CasePathable
    enum Action {
        case path(StackAction<Path.State, Path.Action>)

        // MARK: - 子 Feature Actions
        case launch(LaunchFeature.Action)
        case login(LoginFeature.Action)
        case main(MainFeature.Action)
        case userProfile(UserProfileFeature.Action)

        // MARK: - 导航 Actions
        case navigateToLogin(context: NavigationContext)
        case navigateToMain(context: NavigationContext)
        case logout(context: NavigationContext)

        // MARK: - 全局 Actions
        case showError(String)
        case dismissError

        // MARK: - 游客登录弹窗 Actions
        case requireLogin
        case confirmLogin
        case cancelLogin

        // MARK: - 内部 Actions（私有使用）
        case _setRoot(State.Root)
    }
    
    @Reducer
    enum Path {
        case login(LoginFeature)
        case userProfile(UserProfileFeature) // 新增用户资料路径
//        case detail(DetailFeature)
//        case settings(SettingsFeature)
    // 添加其他可能的导航页面
    }

    var body: some ReducerOf<Self> {
        Scope(state: \.launch, action: \.launch) { LaunchFeature() }
        Scope(state: \.login, action: \.login) { LoginFeature() }
        Scope(state: \.main, action: \.main) { MainFeature() }
        Scope(state: \.userProfile, action: \.userProfile) { UserProfileFeature() } // 注册用户资料
        Reduce { state, action in
//            XLog.i("[AppFeature] 收到 action: \(action)")
            switch action {

            // MARK: - 导航处理
            case let .navigateToLogin(context):
                return handleNavigateToLogin(&state, context: context)

            case let .navigateToMain(context):
                return handleNavigateToMain(&state, context: context)

            case let .logout(context):
                return handleLogout(&state, context: context)

            case let ._setRoot(root):
                state.root = root
                return .none

            // MARK: - 子 Feature 委托处理
            case .launch(.delegate(.navigateToLogin)):
                return .send(.navigateToLogin(context: .initial))

            case .launch(.delegate(.navigateToMain)):
                return .send(.navigateToMain(context: .initial))

            case .login(.delegate(.loginSuccess)):
                return .send(.navigateToMain(context: .initial))

            // MARK: - 委托方法处理
            case let .main(.delegate(.logoutRequested(context))):
                return .send(.logout(context: context))

            case let .main(.delegate(.loginRequested(context))):
                return .send(.navigateToLogin(context: context))

            case .main(.delegate(.requireLogin)):
                return .send(.requireLogin)

            case .main(.delegate(.navigateToProfile)):
                XLog.i("[AppFeature] Navigating to user profile")
                state.path.append(.userProfile(UserProfileFeature.State()))
                return .none

            case let .userProfile(.delegate(.logoutRequested(context))):
                return .send(.logout(context: context))

            case .path(.element(_, .login(.delegate(.loginSuccess)))):
                state.path.removeLast()
                return .none

            // MARK: - 全局错误处理
            case let .showError(message):
                state.globalError = message
                return .none

            case .dismissError:
                state.globalError = nil
                return .none

            // MARK: - 游客登录弹窗处理
            case .requireLogin:
                state.requireLoginAlert = true
                return .none

            case .confirmLogin:
                state.requireLoginAlert = false
                return .send(.navigateToLogin(context: .guestLogin))

            case .cancelLogin:
                state.requireLoginAlert = false
                return .none

            // MARK: - 其他 Actions
            default:
                return .none
            }
        }
        .forEach(\.path, action: \.path)
    }

    // MARK: - 私有导航处理方法

    /// 处理导航到登录页
    private func handleNavigateToLogin(_ state: inout State, context: NavigationContext) -> Effect<Action> {
//        XLog.i("[AppFeature] Navigate to login with context: \(context)")

        switch context {
        case .initial, .reauth:
            // 强制登录，替换根视图
            state.path.removeAll()
            return .send(._setRoot(.login(LoginFeature.State())))

        case .userAction:
            // 用户主动操作，替换根视图
            state.path.removeAll()
            return .send(._setRoot(.login(LoginFeature.State())))

        case .guestLogin:
            // 游客登录，使用栈导航（可返回）
            state.path.append(.login(LoginFeature.State()))
            return .none
        }
    }

    /// 处理导航到主页
    private func handleNavigateToMain(_ state: inout State, context: NavigationContext) -> Effect<Action> {
//        XLog.i("[AppFeature] Navigate to main with context: \(context)")

        switch context {
        case .initial:
            // 首次启动或登录成功，替换根视图
            state.path.removeAll()
            return .send(._setRoot(.main(MainFeature.State())))

        case .reauth, .userAction, .guestLogin:
            // 其他情况也替换根视图
            state.path.removeAll()
            return .send(._setRoot(.main(MainFeature.State())))
        }
    }

    /// 处理退出登录
    private func handleLogout(_ state: inout State, context: NavigationContext) -> Effect<Action> {
//        XLog.i("[AppFeature] Logout with context: \(context)")

        switch context {
        case .userAction:
            // 用户主动退出，完全清空状态并跳转到登录页
            state.path.removeAll()
            return .send(._setRoot(.login(LoginFeature.State())))

        case .reauth:
            // Token失效，触发重新认证
            return .send(.navigateToLogin(context: .reauth))

        case .initial, .guestLogin:
            // 这些场景下不应该有退出操作
            XLog.w("[AppFeature] Unexpected logout context: \(context)")
            return .none
        }
    }
}

extension AppFeature.Path.State: Equatable {}


