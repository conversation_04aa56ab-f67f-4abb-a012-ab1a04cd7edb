import SwiftUI

public enum NavigationBarStyle {
    case centerTitle(title: String)
    case workAndAuthor(work: String, author: String)
    case description(text: String)
}

public struct CustomNavigationBar: View {
    let style: NavigationBarStyle
    let onBack: () -> Void
    let onMenu: () -> Void

    public init(
        style: NavigationBarStyle,
        onBack: @escaping () -> Void,
        onMenu: @escaping () -> Void
    ) {
        self.style = style
        self.onBack = onBack
        self.onMenu = onMenu
    }

    public var body: some View {
        HStack {
            // 返回按钮
            Button(action: onBack) {
                Image(systemName: "chevron.left")
                    .font(.title2)
            }
//           

            // 根据 style 显示不同内容
            switch style {
            case .centerTitle(let title):
                Spacer()
                Text(title)
                    .font(.headline)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                Spacer()
            case .workAndAuthor(let work, let author):
                HStack(spacing: 4) {
                    Text(work)
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    Text("·", comment: "作品和作者之间的分隔符")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Text(author)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                Spacer()
            case .description(let text):
                Text(text)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                Spacer()
            }

            // 菜单按钮
            Button(action: onMenu) {
                Image(systemName: "ellipsis")
                    .font(.title2)
            }
        }
        .padding(.horizontal)
        .frame(height: 56)
        .background(Color.white)
    }
} 
