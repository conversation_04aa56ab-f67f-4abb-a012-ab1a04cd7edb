import SwiftUI
import ComposableArchitecture

struct AppView: View {
    @Bindable var store: StoreOf<AppFeature>
    @State private var tokenInvalidObserver: NSObjectProtocol? // 新增: 监听 token 失效
    
    var body: some View {
        NavigationStack(path: $store.scope(state: \.path, action: \.path)) {
            rootView
                .animation(.easeInOut(duration: 0.3), value: store.root) // Root 级导航动画
        } destination: { store in
            destinationView(for: store)
        }
        .alert("错误", isPresented: .constant(store.globalError != nil)) {
            Button("确定") {
                DispatchQueue.main.async {
                    store.send(.dismissError)
                }
            }
        } message: {
            if let error = store.globalError {
                Text(error)
            }
        }
        // 自定义登陆弹窗
        .overlay {
            if store.requireLoginAlert {
                loginAlertOverlay
            }
        }
        .onAppear {
            Task {
                setupTokenObserver()
            }
        }
        .onDisappear {
            Task {
                removeTokenObserver()
            }
        }
    }

    // 提取登录弹窗覆盖层
    @ViewBuilder
    private var loginAlertOverlay: some View {
        Color.black.opacity(0.4)
            .ignoresSafeArea()
            .transition(.opacity)

        CustomAlertView(
            message: NSLocalizedString("需要登陆才能进入该功能", comment: "需要登录提示文本"),
            confirmText: NSLocalizedString("登陆", comment: "登录按钮文本"),
            cancelText: NSLocalizedString("取消", comment: "取消按钮文本"),
            onConfirm: {
                DispatchQueue.main.async {
                    store.send(.confirmLogin)
                }
            },
            onCancel: {
                DispatchQueue.main.async {
                    store.send(.cancelLogin)
                }
            }
        )
    }

    // 提取根视图逻辑
    @ViewBuilder
    private var rootView: some View {
        switch store.state.root {
        case .launch:
            launchView
                .transition(.opacity) // Root 级导航使用淡入淡出
        case .login:
            loginView
                .transition(.opacity) // Root 级导航使用淡入淡出
        case .main:
            mainView
                .transition(.opacity) // Root 级导航使用淡入淡出
        }
    }
    
    // 提取 Launch 视图
    @ViewBuilder
    private var launchView: some View {
        LaunchView(store: store.scope(state: \.launch, action: \.launch))
    }
    
    // 提取 Login 视图
    @ViewBuilder
    private var loginView: some View {
        LoginView(store: store.scope(state: \.login, action: \.login))
    }
    
    // 提取 Main 视图
    @ViewBuilder
    private var mainView: some View {
        // 步骤8: 测试MainView结构但使用简单tab内容
        MainView(store: store.scope(state: \.main, action: \.main))
    }
    
    // 提取导航目的地视图逻辑
    @ViewBuilder
    private func destinationView(for store: StoreOf<AppFeature.Path>) -> some View {
        switch store.case {
        case let .login(loginStore):
            LoginView(store: loginStore)
                .transition(.move(edge: .trailing)) // Stack 级导航使用推入推出
        case let .userProfile(profileStore):
            UserProfileView(store: profileStore)
                .transition(.move(edge: .trailing)) // Stack 级导航使用推入推出
        }
    }

    // MARK: - Private Methods

    private func setupTokenObserver() {
        tokenInvalidObserver = NotificationCenter.default.addObserver(
            forName: .tokenInvalid,
            object: nil,
            queue: .main
        ) { _ in
            // 使用DispatchQueue.main.async避免在视图更新期间修改状态
            DispatchQueue.main.async {
                store.send(.requireLogin)
            }
        }
    }

    private func removeTokenObserver() {
        if let observer = tokenInvalidObserver {
            NotificationCenter.default.removeObserver(observer)
            tokenInvalidObserver = nil
        }
    }
}

