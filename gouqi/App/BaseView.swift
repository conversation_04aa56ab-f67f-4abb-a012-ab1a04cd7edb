import SwiftUI



public struct AppBaseView<Content: View>: View {
    let backgroundColor: Color
    let showNavigationBar: Bool
    let navigationBarStyle: NavigationBarStyle?
    let onBack: (() -> Void)?
    let onMenu: (() -> Void)?
    let content: () -> Content
    
    public init(
        backgroundColor: Color = .white,
        navigationBarStyle: NavigationBarStyle? = nil,
        onBack: (() -> Void)? = nil,
        onMenu: (() -> Void)? = nil,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.backgroundColor = backgroundColor
        self.navigationBarStyle = navigationBarStyle
        self.onBack = onBack
        self.onMenu = onMenu
        self.content = content
        self.showNavigationBar = navigationBarStyle != nil
    }
    
    public var body: some View {
        ZStack {
            backgroundColor.ignoresSafeArea()
            VStack(spacing: 0) {
                if let navigationBarStyle = navigationBarStyle,
                   let onBack = onBack,
                   let onMenu = onMenu {
                    CustomNavigationBar(style: navigationBarStyle, onBack: onBack, onMenu: onMenu)
                }
                content()
            }
        }
//        .modifier(HideNavigationBarModifier())
    }
}

private struct HideNavigationBarModifier: ViewModifier {
    func body(content: Content) -> some View {
        if #available(iOS 16.0, *) {
            content.toolbar(.hidden, for: .navigationBar)
        } else {
            content.navigationBarHidden(true)
        }
    }
}



// 预览
struct AppBaseView_Previews: PreviewProvider {
    static var previews: some View {
        AppBaseView {
            Text("Hello, World!", comment: "预览示例文本")
        }
    }
} 
