# 快速开始

本节将带你通过一个简单的计数器应用，快速了解 TCA 的核心用法。

## 1. 定义 State（状态）

State 用于描述应用的数据结构。例如：
```swift
struct AppState {
  var count = 0
}
```

## 2. 定义 Action（动作）

Action 用于描述所有可能影响 State 的事件。例如：
```swift
enum AppAction {
  case increment
  case decrement
}
```

## 3. 定义 Reducer（变换器）

Reducer 负责根据 Action 修改 State，并可返回副作用 Effect。
```swift
let appReducer = Reducer<AppState, AppAction, Void> { state, action, _ in
  switch action {
  case .increment:
    state.count += 1
    return .none
  case .decrement:
    state.count -= 1
    return .none
  }
}
```

## 4. 创建 Store（存储）

Store 负责持有 State、分发 Action，并通过 Reducer 处理逻辑。
```swift
let store = Store(
  initialState: AppState(),
  reducer: appReducer,
  environment: ()
)
```

## 5. 绑定到 View（视图）

在 SwiftUI 中，通常通过 `WithViewStore` 绑定 State 和 Action，实现界面与业务逻辑分离。

---

### 解析与学习建议
- 每个 TCA 应用都由 State、Action、Reducer、Store 四大核心组成。
- Reducer 只负责纯粹的状态变更，副作用通过 Effect 明确管理。
- Store 是唯一数据源，所有状态变更都通过 Action 驱动。
- 建议先用简单 Demo 熟悉流程，再逐步引入副作用和模块拆分。

> 原文链接: [Getting Started | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/gettingstarted) 