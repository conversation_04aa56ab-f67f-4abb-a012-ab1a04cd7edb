# FAQ

本节收录了 TCA 使用过程中常见的问题及解答，帮助你更好地理解和应用该架构。

## 常见问题

### 1. 为什么要用 TCA？
TCA 提供了高度一致、可组合、可测试的架构模式，适合中大型项目和对可维护性有高要求的团队。

### 2. TCA 会不会很重？
TCA 的学习曲线略高，但一旦掌握，能极大提升项目的可维护性和协作效率。小型项目也可用，但需权衡复杂度。

### 3. 如何管理副作用？
所有副作用都通过 Effect 明确管理，避免"隐式副作用"污染业务逻辑，便于测试和调试。

### 4. 如何拆分大型页面？
通过 State、Action、Reducer 的组合和作用域（scope），可将大型页面拆分为多个子模块，独立开发和测试。

### 5. 如何做单元测试？
用 TestStore 可以端到端测试 Action、State、Effect，配合依赖注入实现 Mock，轻松覆盖各种场景。

### 6. TCA 支持哪些平台？
TCA 支持 iOS、macOS、watchOS、tvOS，兼容 SwiftUI 和 UIKit。

## 学习建议
- 推荐结合官方文档和 Demo 实践，逐步掌握 TCA 各部分用法。
- 多关注社区最佳实践和常见误区，提升架构能力。

> 原文链接: [FAQ | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/faq) 