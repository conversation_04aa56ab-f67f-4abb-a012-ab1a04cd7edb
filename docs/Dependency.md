# Dependency

Dependency（依赖注入）用于将外部服务、环境变量等注入到 Reducer 和 Effect 中，实现解耦和可测试性。

## 设计原则
- 通过 Environment 或 Dependency 机制，将网络、数据库、日志等依赖注入 Reducer。
- 便于在测试时替换依赖，实现 Mock 或 Stub。
- 依赖注入让业务逻辑与具体实现解耦，提升代码复用性和可维护性。

## 示例
```swift
struct CounterEnvironment {
    var apiClient: APIClient
    var mainQueue: AnySchedulerOf<DispatchQueue>
}

let counterReducer = Reducer<CounterState, CounterAction, CounterEnvironment> { state, action, environment in
    // 可通过 environment.apiClient 访问依赖
}
```

## 解析与学习建议
- 推荐将所有副作用相关依赖集中在 Environment/Dependency 中统一管理。
- 测试时可传入 Mock 依赖，验证业务逻辑的正确性。
- 依赖注入是实现高可测试性和解耦的关键。

> 原文链接: [Dependency | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/dependency) 