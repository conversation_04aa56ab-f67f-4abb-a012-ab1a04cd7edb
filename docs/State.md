# State

State（状态）用于描述应用的所有数据，是 TCA 架构的核心。

## 设计原则
- State 应该只包含当前页面/模块所需的数据。
- 推荐使用 struct，保证值语义，便于追踪和测试。
- 所有状态变更都应通过 Action 和 Reducer 驱动，避免直接修改。

## 示例
```swift
struct CounterState: Equatable {
    var count: Int = 0
    var isLoading: Bool = false
}
```

## 解析与学习建议
- State 结构应简洁明了，避免冗余字段。
- 可选类型（如 `var error: String?`）适合描述临时状态。
- 复杂页面建议拆分为多个子 State，便于组合和维护。
- 所有 UI 展示的数据都应来源于 State，保证单一数据源。

> 原文链接: [State | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/state) 