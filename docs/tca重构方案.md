# tca重构方案

## 一、重构目标

- 统一工程架构，提升可维护性和可扩展性
- 严格遵循 TCA（Composable Architecture）最佳实践
- 明确模块边界，规范命名和文件结构
- 优化依赖注入、路由、主题色、表单、弹窗等核心能力
- 完善注释和文档，提升团队协作效率

## 二、重构规范

1. **目录结构与模块划分**：每个 Feature 独立目录，State/Action/Reducer/View 四大件分明，核心数据结构集中管理。
2. **命名规范**：结构体、枚举、变量、方法均采用 Swift 官方驼峰命名法，Action 命名为"动词+对象"。
3. **TCA 代码规范**：State/Action/Reducer/View 拆分明确，Reducer 统一用 @Reducer 宏，禁止 View 层直接修改 State。
4. **路由与导航**：主 State 用 StackState<Path.State>() 管理导航栈，Path 枚举集中声明页面，Reducer 用 forEach 注册路由。
5. **依赖注入**：所有依赖通过 @Dependency 注入，依赖声明集中管理。
6. **主题色与资源**：Theme 枚举集中管理主题色，资源文件命名规范。
7. **表单与弹窗**：表单 State 需 focus 字段，弹窗统一用 @Presents 管理。
8. **注释与文档**：关键结构、方法、业务流程必须有中文注释，文档统一在 docs/ 目录。
9. **测试与 mock**：测试代码与业务分离，依赖注入支持 mock。

## 三、重构步骤（勾选清单）

- [ ] 1. 梳理现有目录和文件，按规范调整结构
    - 细节补充：
        - 业务模块 Features/ 下各 Feature（如 Login、Launch、Main）均已独立成目录，State/Action/Reducer/View 文件齐全，命名规范。
        - Core/ 目录用于基础能力和依赖管理，结构合理。
        - App/ 目录为主入口，结构清晰。
        - 资源 Assets.xcassets/ 分类规范，Resources/、Utils/ 目录可根据实际需要补充。
        - **未发现统一核心数据结构，无需新建 Models/ 目录。**
        - 测试代码与业务代码分离良好。

- [ ] 2. 统一 State/Action/Reducer/View 命名和拆分
    - 细节补充：
        - 所有 Feature（Launch、Login、Main）均已严格拆分 State/Action/Reducer/View，命名规范，无混用、无遗漏。
        - Action 命名风格、Reducer 写法、View 依赖 Store 方式均符合 TCA 最佳实践。
        - 文件内注释明确，结构清晰，便于维护和扩展。

- [ ] 3. 规范主 State/Action/Reducer/Path，统一导航栈
    - 细节补充：
        - AppState 统一用 path = StackState<Path.State>() 管理导航栈，Path 枚举集中声明所有可导航页面，Action 统一管理路由相关动作。
        - AppReducer 通过 .ifLet 组合子模块 Reducer，导航跳转通过辅助方法清空并重置导航栈，保证路由与状态同步。
        - AppView 使用 NavigationStackStore 绑定 path，destination 闭包精准分发子页面 Store，完全符合 TCA 路由与导航最佳实践。
        - 结构清晰，注释明确，便于维护和扩展。

- [ ] 4. 依赖注入、主题色、表单、弹窗等按规范重构
    - 细节补充：
        - 依赖注入已通过 DependencyKey/DependencyValues 扩展集中管理，Reducer 层用法规范，便于 mock 和测试。
        - 主题色通过 Color 资源和命名规范实现统一，页面背景色等已集中管理，未采用 Theme 枚举但满足当前需求。
        - 表单体验采用 TCA binding 方式与 State 绑定，输入体验规范，暂未实现自动聚焦（如需可后续补充）。
        - 弹窗通过 AppView 的 .alert 修饰符实现全局错误弹窗，未采用 AlertState/@Presents，当前结构适合全局提示，局部弹窗可按需补充。

- [ ] 5. 完善注释和文档，补充测试用例
    - 细节补充：
        - 主要业务代码有结构注释，部分函数/流程缺少详细中文注释，建议后续补充关键流程、核心分支的注释说明。
        - docs/ 目录下文档体系完善，涵盖架构、开发、测试、TCA 各核心概念，便于团队查阅和新成员上手。
        - 单元测试、UI 测试代码结构规范，覆盖基础功能和主要页面，建议后续补充更多业务流程和边界场景的测试用例。

- [ ] 6. 常用宏定义及代码用法参考
    - 细节补充：
        - @Reducer：用于声明 Reducer，自动生成协议实现，推荐新功能模块直接使用。
        - @ObservableState：用于 State 结构体，支持状态变更监听和绑定。
        - @Presents：用于声明弹窗、sheet、子页面等临时状态，配合 PresentationAction 使用。
        - @Shared：用于声明全局共享状态，适用于全局数据存储、跨页面共享数据。
        - @CasePathable：用于枚举，自动生成 case 路径，便于 Action 模式匹配和路由。
        - @Dependency：用于依赖注入，将外部依赖注入 Reducer，便于 mock 和测试。
        - @Bindable：用于 View 层 Store 声明，支持 State 与 UI 的双向绑定。
        - 建议新增 Feature 时优先使用上述宏，减少模板代码，提升开发效率。
        - Action/State/Reducer/View 命名、结构、注释要规范，便于团队协作。

---

> 每完成一步，需在对应"细节补充"区详细记录本次重构的具体内容、遇到的问题及解决方案，然后再进行下一步。 