# View

View（视图）负责展示 State，并将用户操作转化为 Action 发送给 Store，实现界面与业务逻辑的解耦。

## 设计原则
- View 只负责展示和交互，不直接操作 State。
- 通过 `WithViewStore` 绑定 State 和 Action，实现响应式 UI。
- 所有用户操作都应转化为 Action，由 Store 统一处理。

## 示例
```swift
struct CounterView: View {
    let store: Store<CounterState, CounterAction>
    var body: some View {
        WithViewStore(self.store) { viewStore in
            VStack {
                Text("计数：\(viewStore.count)")
                Button("加一") { viewStore.send(.increment) }
                Button("减一") { viewStore.send(.decrement) }
            }
        }
    }
}
```

## 解析与学习建议
- View 通过 `ViewStore` 观察 State，自动响应状态变化。
- 用户操作（如点击按钮）通过 `send` 方法派发 Action，驱动业务逻辑。
- 推荐将 View 设计为"傻瓜视图"，所有逻辑都交给 Reducer 和 Store。
- 复杂页面可拆分为多个子 View，各自绑定独立的 Store。

> 原文链接: [View | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/view) 