# Effect

Effect（副作用）用于处理异步操作或与外部系统的交互，是 TCA 架构中副作用管理的核心。

## 设计原则
- Effect 用于描述网络请求、定时器、数据库操作等异步任务。
- Reducer 只返回 Effect，不直接执行副作用。
- Effect 完成后可通过 Action 将结果反馈给 Reducer，驱动状态变更。

## 示例
```swift
case .fetchData:
    state.isLoading = true
    return .task {
        let result = await apiClient.fetchData()
        return .fetchDataResponse(result)
    }
case .fetchDataResponse(let result):
    state.isLoading = false
    // 根据 result 更新 state
    return .none
```

## 解析与学习建议
- 所有副作用都应通过 Effect 明确管理，避免"隐式副作用"污染业务逻辑。
- Effect 支持取消、合并、延迟等操作，适合复杂异步场景。
- 推荐将副作用相关代码集中在 Environment 或专用服务中，便于测试和复用。
- Effect 机制让异步流程变得可预测、可测试。

> 原文链接: [Effect | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/effect) 