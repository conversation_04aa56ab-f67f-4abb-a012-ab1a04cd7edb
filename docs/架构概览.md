# 架构概览

TCA 架构围绕以下核心要素展开：

- **State（状态）**：集中存储应用的所有数据。
- **Action（动作）**：描述所有可能影响状态的事件。
- **Reducer（变换器）**：根据 Action 修改 State，并可产生副作用。
- **Store（存储）**：连接 State、Action 和 Reducer，是唯一数据源。
- **Effect（副作用）**：处理异步操作、网络请求等副作用。

## 架构流程
1. 用户在 View 上操作，触发 Action。
2. Store 接收 Action，交由 Reducer 处理。
3. Reducer 根据 Action 修改 State，并可返回 Effect。
4. Effect 处理异步任务，完成后可再次派发 Action。
5. View 通过 Store 订阅 State，自动刷新界面。

## 解析与学习建议
- TCA 强调"单向数据流"，所有状态变更都可追踪。
- 副作用与业务逻辑分离，便于测试和维护。
- 架构天然支持模块化和功能拆分，适合大型项目。
- 推荐结合官方 Demo 理解各部分协作关系。

> 原文链接: [Architecture Overview | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture) 