# Scope

Scope（作用域）用于将父 State/Action/Reducer 映射到子 State/Action/Reducer，实现模块化和复用。

## 设计原则
- Scope 让大型页面可以拆分为多个子模块，每个子模块拥有独立的 State、Action、Reducer。
- 父 Store 通过 scope 方法派生出子 Store，子 View 只关心自己的状态和动作。
- 作用域机制让代码结构更清晰，便于团队协作和单元测试。

## 示例
```swift
struct ParentState {
    var child: ChildState = .init()
}
enum ParentAction {
    case child(ChildAction)
}
let parentReducer = Reducer<ParentState, ParentAction, Void> {
    ...
}.combine(
    childReducer.pullback(
        state: \ParentState.child,
        action: /ParentAction.child,
        environment: { _ in () }
    )
)
```

## 解析与学习建议
- 推荐所有页面和业务模块都用 Scope 拆分，提升可维护性。
- 子模块可独立开发、测试和复用，降低耦合。
- Scope 机制是 TCA 实现"可组合架构"的基础。

> 原文链接: [Scope | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/scope) 