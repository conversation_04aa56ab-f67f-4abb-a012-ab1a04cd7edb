# Navigation

Navigation（导航）介绍 TCA 中的导航与路由管理方法。

## 设计原则
- TCA 支持基于 State 的导航，所有页面跳转都由状态驱动。
- 路由信息存储在 State 中，便于追踪和测试。
- 支持多级嵌套导航、条件跳转等复杂场景。

## 示例
```swift
struct AppState {
    var path: [Route] = []
}
enum Route {
    case login
    case main
}
// 通过修改 path 实现页面跳转
```

## 解析与学习建议
- 推荐所有导航逻辑都用 State 管理，避免"隐式跳转"。
- 路由状态可序列化，便于深链、恢复等高级场景。
- 导航机制让页面跳转变得可预测、可测试。

> 原文链接: [Navigation | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/navigation) 