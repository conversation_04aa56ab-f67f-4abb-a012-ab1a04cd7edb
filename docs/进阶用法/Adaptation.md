# Adaptation

Adaptation（适配）介绍如何适配不同的 State、Action 或 Reducer，以实现灵活的架构扩展。

## 设计原则
- 通过适配器模式，可以将外部模块或第三方库的 State/Action/Reducer 集成到 TCA 架构中。
- 适配让不同模块间的数据和事件流转更灵活，便于系统集成和扩展。
- 适合需要兼容旧系统或多种架构风格的场景。

## 示例
```swift
// 假设有外部模块 ExternalState/ExternalAction
let adaptedReducer = externalReducer.adapt(
    state: \AppState.external,
    action: /AppAction.external
)
```

## 解析与学习建议
- 推荐在系统集成、架构升级等场景下使用 Adaptation，提升兼容性。
- 适配器模式让 TCA 更易与其他架构协作，降低迁移成本。
- 适配机制是实现"架构无缝对接"的关键。

> 原文链接: [Adaptation | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/adaptation) 