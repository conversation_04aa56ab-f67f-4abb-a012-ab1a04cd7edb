# Pullback

Pullback（下拉/拉取）用于将子模块的 Reducer 嵌入到父模块，实现局部状态和动作的组合。

## 设计原则
- Pullback 让子模块的 Reducer 可以在父模块中复用，提升代码复用性。
- 通过 pullback，可以将父 State/Action/Environment 映射到子模块，子模块无需感知外部结构。
- 适合多层嵌套、复杂页面的模块化开发。

## 示例
```swift
let parentReducer = childReducer.pullback(
    state: \ParentState.child,
    action: /ParentAction.child,
    environment: { _ in () }
)
```

## 解析与学习建议
- Pullback 是 TCA 实现"功能组合"的核心机制。
- 推荐所有可复用的业务逻辑都用 Pullback 组合，避免重复造轮子。
- Pullback 让子模块保持独立，便于单元测试和维护。

> 原文链接: [Pullback | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/pullback) 