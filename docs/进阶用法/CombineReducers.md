# CombineReducers

CombineReducers（合并 Reducer）用于将多个 Reducer 组合成一个整体 Reducer，便于模块化和解耦。

## 设计原则
- 通过 combine/combineReducers，可以将多个子模块的 Reducer 合并，形成完整的业务逻辑链路。
- 每个子 Reducer 只关注自身业务，合并后由父 Reducer 统一调度。
- 适合大型页面、复杂业务的功能拆分。

## 示例
```swift
let appReducer = combineReducers(
    counterReducer,
    todoReducer,
    ...
)
```

## 解析与学习建议
- 推荐所有页面和业务模块都用 CombineReducers 组合，提升可维护性。
- 合并 Reducer 后，业务逻辑更清晰，便于团队协作和单元测试。
- CombineReducers 是 TCA 实现"可组合架构"的重要工具。

> 原文链接: [CombineReducers | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/combinereducers) 