# Sharing and Persisting State

本节介绍如何在 TCA 中实现状态的共享与持久化，提升应用的数据一致性和用户体验。

## 设计原则
- 支持多个模块/页面共享同一份 State，避免数据冗余。
- 通过持久化机制（如 UserDefaults、数据库）保存 State，实现数据恢复和持久化。
- 状态共享和持久化应通过 Reducer/Effect 明确管理，避免隐式副作用。

## 示例
```swift
// 通过依赖注入共享 UserState
struct AppState {
    var user: UserState
}
// 持久化 State 到本地
Effect.run { send in
    UserDefaults.standard.set(state.user, forKey: "user")
    return .none
}
```

## 解析与学习建议
- 推荐将全局共享数据（如用户信息、设置等）集中管理，避免多处维护。
- 持久化操作建议通过 Effect 实现，便于测试和追踪。
- 状态共享和持久化让应用更健壮，用户体验更佳。

> 原文链接: [Sharing and Persisting State | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/sharingandpersistingstate) 