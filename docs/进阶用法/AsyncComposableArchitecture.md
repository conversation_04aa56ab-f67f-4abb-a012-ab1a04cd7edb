# Async Composable Architecture

Async Composable Architecture（异步架构）介绍如何在 TCA 中优雅地处理异步操作和并发场景。

## 设计原则
- TCA 支持 async/await，简化异步副作用的编写和管理。
- 异步 Effect 可直接返回 Action，驱动状态变更。
- 支持任务取消、超时等高级并发控制。

## 示例
```swift
case .loadData:
    return .run { send in
        let data = await apiClient.fetch()
        await send(.loadDataResponse(data))
    }
case .loadDataResponse(let data):
    state.data = data
    return .none
```

## 解析与学习建议
- 推荐优先使用 async/await 处理异步副作用，代码更简洁、易读。
- 善用 TCA 的任务取消和并发控制，避免资源泄漏和竞态问题。
- 异步架构让复杂业务流程变得可预测、可测试。

> 原文链接: [Async Composable Architecture | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/asynccomposablearchitecture) 