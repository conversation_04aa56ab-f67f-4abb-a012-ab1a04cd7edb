# Testing

TCA 提供了完善的测试支持，可以对 State、Action、Reducer、Effect 进行单元测试和集成测试。

## 设计原则
- 所有业务逻辑都集中在 Reducer，便于隔离测试。
- 副作用通过 Effect 明确管理，可用 TestStore 进行断言。
- 依赖注入机制让 Mock/Stub 测试变得简单。

## 示例
```swift
let store = TestStore(
    initialState: CounterState(),
    reducer: counterReducer,
    environment: CounterEnvironment(
        apiClient: .mock,
        mainQueue: .immediate
    )
)

await store.send(.increment) {
    $0.count = 1
}
```

## 解析与学习建议
- 推荐用 TestStore 进行端到端测试，覆盖 Action、State、Effect 全流程。
- 测试用例应覆盖所有分支和副作用，提升代码质量。
- 通过依赖注入和 Mock，轻松验证异步和边界场景。
- 测试驱动开发（TDD）在 TCA 架构下实现成本极低。

> 原文链接: [Testing | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/testing) 