# Reducer

Reducer（变换器）是 TCA 的核心，负责根据 Action 修改 State，并可产生副作用（Effect）。

## 设计原则
- Reducer 是一个纯函数，输入当前 State 和 Action，输出新的 State 和 Effect。
- 所有业务逻辑、状态变更都应集中在 Reducer 内实现。
- 副作用（如网络请求）通过 Effect 明确返回，便于测试和追踪。

## 示例
```swift
let counterReducer = Reducer<CounterState, CounterAction, CounterEnvironment> { state, action, environment in
    switch action {
    case .increment:
        state.count += 1
        return .none
    case .decrement:
        state.count -= 1
        return .none
    case .setLoading(let isLoading):
        state.isLoading = isLoading
        return .none
    case .fetchCountResponse(let result):
        switch result {
        case .success(let value):
            state.count = value
            state.isLoading = false
        case .failure:
            state.isLoading = false
        }
        return .none
    }
}
```

## 解析与学习建议
- Reducer 只处理同步状态变更和副作用描述，不直接执行异步任务。
- 复杂业务可拆分为多个 Reducer，通过组合（如 combine、ifLet、pullback）实现模块化。
- 所有副作用都应通过 Effect 返回，便于单元测试和调试。
- Reducer 代码应保持纯净、无副作用，便于维护和复用。

> 原文链接: [Reducer | Composable Architecture](https://pointfreeco.github.io/swift-composable-architecture/main/documentation/composablearchitecture/reducer) 